<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pharmacy Accounting Flowchart Animation</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="container">
        <div class="flowchart-content">
            <!-- Phase Labels -->
            <div id="orderPhase" class="phase-label">Order Processing Phase</div>
            <div id="dailyPhase" class="phase-label">Daily Reconciliation Phase</div>
            <div id="monthlyPhase" class="phase-label">Monthly Reconciliation Phase</div>
            
            <!-- Phase Separators -->
            <div id="separator1" class="phase-separator"></div>
            <div id="separator2" class="phase-separator"></div>
            
            <!-- Flowchart Lines -->
            <!-- Main process lines -->
            <div id="lineAB" class="line"></div>
            <div id="lineBC" class="line"></div>
            <div id="lineCD" class="line"></div>
            <div id="lineDE" class="line"></div>
            <div id="lineEF" class="line"></div>
            <div id="lineFG" class="line"></div>
            <div id="lineGK" class="line"></div>
            
            <!-- Branch lines -->
            <div id="lineCX" class="line"></div>
            <div id="lineXY" class="line"></div>
            <div id="lineYC" class="line"></div>
            <div id="lineDZ" class="line"></div>
            <div id="lineZY" class="line"></div>
            <div id="lineGH" class="line"></div>
            <div id="lineGI" class="line"></div>
            <div id="lineIJ" class="line"></div>
            <div id="lineJK" class="line"></div>
            <div id="lineHK" class="line"></div>
            
            <!-- Monthly process lines -->
            <div id="lineKL" class="line"></div>
            <div id="lineLM" class="line"></div>
            <div id="lineMN" class="line"></div>
            <div id="lineNO" class="line"></div>
            <div id="lineOP" class="line"></div>
            <div id="lineOQ" class="line"></div>
            <div id="lineQR" class="line"></div>
            <div id="lineRL" class="line"></div>

            <!-- Step Nodes -->
            <!-- Main process row -->
            <div id="nodeA" class="node active">
                <div class="icon"><i class="fa-solid fa-clipboard-check"></i></div>
                <div class="text">Order Placed in System</div>
            </div>
            <div id="nodeB" class="node">
                <div class="icon"><i class="fa-solid fa-list"></i></div>
                <div class="text">Log Order Details</div>
            </div>
            <div id="nodeC" class="node decision">
                <div class="icon"><i class="fa-solid fa-envelope"></i></div>
                <div class="text">Receive Invoice?</div>
            </div>
            <div id="nodeD" class="node decision">
                <div class="icon"><i class="fa-solid fa-truck"></i></div>
                <div class="text">Medicines Delivered?</div>
            </div>
            <div id="nodeE" class="node">
                <div class="icon"><i class="fa-solid fa-check"></i></div>
                <div class="text">Tick Off Delivered Items</div>
            </div>
            <div id="nodeF" class="node">
                <div class="icon"><i class="fa-solid fa-clipboard-list"></i></div>
                <div class="text">Compare Items with Invoice</div>
            </div>
            <div id="nodeG" class="node decision">
                <div class="icon"><i class="fa-solid fa-question"></i></div>
                <div class="text">Do Items Match?</div>
            </div>
            <div id="nodeK" class="node">
                <div class="icon"><i class="fa-solid fa-chart-line"></i></div>
                <div class="text">Complete Daily Reconciliation</div>
            </div>
            
            <!-- Branch nodes -->
            <div id="nodeX" class="node">
                <div class="icon"><i class="fa-solid fa-exclamation-triangle"></i></div>
                <div class="text">Missing Invoice</div>
                <div id="alertX" class="alert">Alert: Missing Invoice</div>
            </div>
            <div id="nodeY" class="node">
                <div class="icon"><i class="fa-solid fa-paper-plane"></i></div>
                <div class="text">Send Follow-up Email</div>
                <div id="emailY" class="email">Sending email...</div>
            </div>
            <div id="nodeZ" class="node">
                <div class="icon"><i class="fa-solid fa-exclamation-triangle"></i></div>
                <div class="text">Undelivered Medicines</div>
                <div id="alertZ" class="alert">Alert: Undelivered</div>
            </div>
            <div id="nodeYY" class="node">
                <div class="icon"><i class="fa-solid fa-paper-plane"></i></div>
                <div class="text">Send Follow-up Email</div>
                <div id="emailYY" class="email">Sending email...</div>
            </div>
            <div id="nodeH" class="node">
                <div class="icon"><i class="fa-solid fa-credit-card"></i></div>
                <div class="text">Apply Credit Notes</div>
            </div>
            <div id="nodeI" class="node">
                <div class="icon"><i class="fa-solid fa-file-alt"></i></div>
                <div class="text">Generate Discrepancy Report</div>
                <div id="alertI" class="alert">Discrepancy Detected</div>
            </div>
            <div id="nodeJ" class="node">
                <div class="icon"><i class="fa-solid fa-paper-plane"></i></div>
                <div class="text">Contact Supplier</div>
                <div id="emailJ" class="email">Contacting supplier...</div>
            </div>
            
            <!-- Monthly process row -->
            <div id="nodeL" class="node">
                <div class="icon"><i class="fa-solid fa-file-invoice"></i></div>
                <div class="text">Receive Month-End Statement</div>
            </div>
            <div id="nodeM" class="node">
                <div class="icon"><i class="fa-solid fa-calculator"></i></div>
                <div class="text">Aggregate Invoices & Credits</div>
            </div>
            <div id="nodeN" class="node">
                <div class="icon"><i class="fa-solid fa-balance-scale"></i></div>
                <div class="text">Reconcile with Statement</div>
            </div>
            <div id="nodeO" class="node decision">
                <div class="icon"><i class="fa-solid fa-question"></i></div>
                <div class="text">Statement Match?</div>
            </div>
            <div id="nodeP" class="node">
                <div class="icon"><i class="fa-solid fa-check-circle"></i></div>
                <div class="text">Reconciliation Successful</div>
                <div id="completeP" class="complete">Process Complete!</div>
            </div>
            <div id="nodeQ" class="node">
                <div class="icon"><i class="fa-solid fa-exclamation-triangle"></i></div>
                <div class="text">Statement Discrepancy</div>
                <div id="alertQ" class="alert">Statement Mismatch</div>
            </div>
            <div id="nodeR" class="node">
                <div class="icon"><i class="fa-solid fa-search"></i></div>
                <div class="text">Initiate Investigation</div>
                <div id="emailR" class="email">Starting investigation...</div>
            </div>

            <!-- Traveler Element -->
            <div id="traveler">
                <i class="fa-solid fa-person-running"></i>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
