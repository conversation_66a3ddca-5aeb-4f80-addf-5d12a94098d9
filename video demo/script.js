document.addEventListener('DOMContentLoaded', function () {
    // Add a small delay to ensure CSS is fully applied
    setTimeout(function () {
        const tl = gsap.timeline({
            defaults: { duration: 2, ease: "power2.out" },
            onComplete: () => {
                console.log("Animation complete");
                setTimeout(() => {
                    // Reset all nodes and messages before restarting
                    nodeIds.forEach(nodeId => {
                        unhighlightNode(nodeId);
                        const alertId = `#alert${nodeId.slice(-1)}`;
                        const emailId = `#email${nodeId.slice(-1)}`;
                        const completeId = `#complete${nodeId.slice(-1)}`;
                        hideMessage(alertId);
                        hideMessage(emailId);
                        hideMessage(completeId);
                    });

                    // After completion, don't automatically restart
                    gsap.to(container, {
                        scrollLeft: 0,
                        duration: 1,
                        ease: "power2.inOut"
                    });
                }, 3000);
            }
        });

        const nodeIds = [
            "#nodeA", "#nodeB", "#nodeC", "#nodeD", "#nodeE", "#nodeF", "#nodeG", "#nodeH",
            "#nodeI", "#nodeJ", "#nodeK", "#nodeL", "#nodeM", "#nodeN", "#nodeO", "#nodeP",
            "#nodeQ", "#nodeR", "#nodeX", "#nodeY", "#nodeYY", "#nodeZ"
        ];

        const traveler = "#traveler";
        const travelerElement = document.querySelector(traveler);

        // Get container for scrolling functionality
        const container = document.querySelector('#container');
        const flowchartContent = document.querySelector('.flowchart-content');
        // Define containerBounds here to avoid the undefined reference
        const containerBounds = container.getBoundingClientRect();

        let currentTravelerPos = { x: 0, y: 0 };

        function highlightNode(nodeId) {
            gsap.to(nodeId, {
                duration: 0.2,
                backgroundColor: "#d4edda",
                borderColor: "#28a745",
            }).then(() => {
                document.querySelector(nodeId).classList.add("active");
            });
        }

        function unhighlightNode(nodeId) {
            const isDecision = document.querySelector(nodeId).classList.contains("decision");
            gsap.to(nodeId, {
                duration: 0.2,
                backgroundColor: isDecision ? "#e2f0fb" : "#e9ecef",
                borderColor: isDecision ? "#9ec5fe" : "#ced4da",
            }).then(() => {
                document.querySelector(nodeId).classList.remove("active");
            });
        }

        function showMessage(messageId) {
            const msgElement = document.querySelector(messageId);
            if (!msgElement) return;

            gsap.to(messageId, {
                opacity: 1, duration: 0.3, onComplete: () => {
                    msgElement.classList.add("show");
                }
            });
        }

        function hideMessage(messageId) {
            const msgElement = document.querySelector(messageId);
            if (!msgElement) return;

            gsap.to(messageId, {
                opacity: 0, duration: 0.3, onComplete: () => {
                    msgElement.classList.remove("show");
                }
            });
        }

        // Improved scroll function with better traveler tracking        
        function scrollToPosition(xPos) {
            if (typeof xPos !== 'number') return;

            // Add a threshold for minimum scroll distance
            const scrollThreshold = 50;
            if (Math.abs(xPos - currentTravelerPos.x) < scrollThreshold) return;

            currentTravelerPos.x = xPos;

            const viewportWidth = containerBounds.width;
            const buffer = viewportWidth * 0.3; // 30% buffer zone

            // Calculate base scroll position (centered)
            let targetPosition = xPos - viewportWidth / 2;

            // Early return if trying to scroll backwards
            if (targetPosition <= container.scrollLeft) {
                return;
            }

            // Add buffer for forward movement
            targetPosition += buffer;

            // Ensure we don't scroll beyond content limits
            const maxScrollLeft = flowchartContent.scrollWidth - viewportWidth;
            targetPosition = Math.min(targetPosition, maxScrollLeft);

            // Stop any current scroll animation
            gsap.killTweensOf(container);

            // Apply scroll animation
            gsap.to(container, {
                scrollLeft: targetPosition,
                duration: 3,
                // ease: "power2.inOut",
                overwrite: true
            });
        }

        // Reset scroll position initially
        container.scrollLeft = 0;

        // Calculate positions for all nodes with proper offsets
        const nodePositions = {};
        const travelerSize = { width: 32, height: 32 }; // Size of traveler element

        // Function to calculate node positions
        function calculateNodePositions() {
            const flowchartBounds = flowchartContent.getBoundingClientRect();
            nodeIds.forEach(nodeId => {
                const node = document.querySelector(nodeId);
                if (!node) return;

                const bounds = node.getBoundingClientRect();

                nodePositions[nodeId] = {
                    x: bounds.left - flowchartBounds.left + container.scrollLeft + (bounds.width / 2),
                    y: bounds.top - flowchartBounds.top + (bounds.height / 2)
                };
            });

            // Set initial position for traveler
            if (nodePositions["#nodeA"]) {
                const initialPos = nodePositions["#nodeA"];
                gsap.set(traveler, {
                    x: initialPos.x - (travelerSize.width / 2),
                    y: initialPos.y - (travelerSize.height / 2)
                });
                currentTravelerPos = { x: initialPos.x, y: initialPos.y };
            }
        }

        // Enhanced traveler movement with better scrolling
        const centerTraveler = (x, y) => {
            if (typeof x !== 'number' || typeof y !== 'number') {
                console.error("Invalid coordinates:", x, y);
                return {};
            }

            // Update current position for scrolling reference
            currentTravelerPos = { x, y };

            // Trigger scroll to keep traveler in view
            scrollToPosition(x);

            return {
                x: x - (travelerSize.width / 2),
                y: y - (travelerSize.height / 2),
                onUpdate: function () {
                    // Update scroll position during the animation for smoother following
                    scrollToPosition(x);
                }
            };
        };

        // Function to start the animation sequence
        function startAnimation() {
            // Recalculate positions to ensure accuracy
            calculateNodePositions();

            // Reset scroll position before starting animation
            container.scrollLeft = 0;

            // Clear any previous animation
            tl.clear();

            // Start the animation sequence
            // Start the animation sequence
            tl
                // A to C and back once at the start
                .add(() => highlightNode("#nodeA"))
                .to(traveler, centerTraveler(nodePositions["#nodeB"].x, nodePositions["#nodeB"].y))
                .add(() => {
                    unhighlightNode("#nodeA");
                    highlightNode("#nodeB");
                })
                .to(traveler, centerTraveler(nodePositions["#nodeC"].x, nodePositions["#nodeC"].y))
                .add(() => {
                    unhighlightNode("#nodeB");
                    highlightNode("#nodeC");
                })

                // C to X: Missing Invoice Branch
                .to(traveler, centerTraveler(nodePositions["#nodeX"].x, nodePositions["#nodeX"].y))
                .add(() => {
                    highlightNode("#nodeX");
                    showMessage("#alertX");
                })

                // X to Y: Follow-up Email
                .to(traveler, centerTraveler(nodePositions["#nodeY"].x, nodePositions["#nodeY"].y))
                .add(() => {
                    highlightNode("#nodeY");
                    hideMessage("#alertX");
                    showMessage("#emailY");
                })

                // Back to C through Y
                .to(traveler, centerTraveler(nodePositions["#nodeC"].x, nodePositions["#nodeC"].y))
                .add(() => {
                    unhighlightNode("#nodeX");
                    unhighlightNode("#nodeY");
                    hideMessage("#emailY");
                })

                // C to D: Delivery Check
                .to(traveler, centerTraveler(nodePositions["#nodeD"].x, nodePositions["#nodeD"].y))
                .add(() => {
                    unhighlightNode("#nodeC");
                    highlightNode("#nodeD");
                })

                // D to Z: Undelivered Branch
                .to(traveler, centerTraveler(nodePositions["#nodeZ"].x, nodePositions["#nodeZ"].y))
                .add(() => {
                    highlightNode("#nodeZ");
                    showMessage("#alertZ");
                })

                // Z to YY: Delivery Follow-up
                .to(traveler, centerTraveler(nodePositions["#nodeYY"].x, nodePositions["#nodeYY"].y))
                .add(() => {
                    highlightNode("#nodeYY");
                    hideMessage("#alertZ");
                })

                // Back to D through YY
                .to(traveler, centerTraveler(nodePositions["#nodeD"].x, nodePositions["#nodeD"].y))
                .add(() => {
                    unhighlightNode("#nodeZ");
                    unhighlightNode("#nodeYY");
                })

                // D to E: Item Check
                .to(traveler, centerTraveler(nodePositions["#nodeE"].x, nodePositions["#nodeE"].y))
                .add(() => {
                    unhighlightNode("#nodeD");
                    highlightNode("#nodeE");
                })

                // E to F: Invoice Comparison
                .to(traveler, centerTraveler(nodePositions["#nodeF"].x, nodePositions["#nodeF"].y))
                .add(() => {
                    unhighlightNode("#nodeE");
                    highlightNode("#nodeF");
                })

                // F to G: Match Check
                .to(traveler, centerTraveler(nodePositions["#nodeG"].x, nodePositions["#nodeG"].y))
                .add(() => {
                    unhighlightNode("#nodeF");
                    highlightNode("#nodeG");
                })

                // G to I: Discrepancy Branch
                .to(traveler, centerTraveler(nodePositions["#nodeI"].x, nodePositions["#nodeI"].y))
                .add(() => {
                    highlightNode("#nodeI");
                    showMessage("#alertI");
                })

                // I to J: Supplier Contact
                .to(traveler, centerTraveler(nodePositions["#nodeJ"].x, nodePositions["#nodeJ"].y))
                .add(() => {
                    highlightNode("#nodeJ");
                    hideMessage("#alertI");
                    showMessage("#emailJ");
                })

                // Back to K through J
                .to(traveler, centerTraveler(nodePositions["#nodeK"].x, nodePositions["#nodeK"].y))
                .add(() => {
                    unhighlightNode("#nodeI");
                    unhighlightNode("#nodeJ");
                    unhighlightNode("#nodeG");
                    hideMessage("#emailJ");
                    highlightNode("#nodeK");
                })

                // Apply credit and return to main flow
                .to(traveler, centerTraveler(nodePositions["#nodeH"].x, nodePositions["#nodeH"].y))
                .add(() => {
                    unhighlightNode("#nodeK");
                    highlightNode("#nodeH");
                    hideMessage("#emailJ");
                })

                // K to L: Month-End Process
                .to(traveler, centerTraveler(nodePositions["#nodeL"].x, nodePositions["#nodeL"].y))
                .add(() => {
                    highlightNode("#nodeL");
                })

                // L to M: Statement Processing
                .to(traveler, centerTraveler(nodePositions["#nodeM"].x, nodePositions["#nodeM"].y))
                .add(() => {
                    unhighlightNode("#nodeL");
                    highlightNode("#nodeM");
                })

                // M to N: Statement Reconciliation
                .to(traveler, centerTraveler(nodePositions["#nodeN"].x, nodePositions["#nodeN"].y))
                .add(() => {
                    unhighlightNode("#nodeM");
                    highlightNode("#nodeN");
                })


                // Continue to O: Statement Check
                .to(traveler, centerTraveler(nodePositions["#nodeO"].x, nodePositions["#nodeO"].y))
                .add(() => {
                    unhighlightNode("#nodeN");
                    unhighlightNode("#nodeK");
                    highlightNode("#nodeO");
                })

                // N to statement discrepancy branch
                .to(traveler, centerTraveler(nodePositions["#nodeQ"].x, nodePositions["#nodeQ"].y))
                .add(() => {
                    highlightNode("#nodeQ");
                    showMessage("#alertQ");
                })

                // Initiate investigation
                .to(traveler, centerTraveler(nodePositions["#nodeR"].x, nodePositions["#nodeR"].y))
                .add(() => {
                    highlightNode("#nodeR");
                    hideMessage("#alertQ");
                    showMessage("#emailR");
                })

                // Continue to O: Statement Check
                .to(traveler, centerTraveler(nodePositions["#nodeO"].x, nodePositions["#nodeO"].y))
                .add(() => {
                    unhighlightNode("#nodeN");
                    unhighlightNode("#nodeK");
                    highlightNode("#nodeO");
                    hideMessage("#emailR");
                })

                // O to P: Success Path
                .to(traveler, centerTraveler(nodePositions["#nodeP"].x, nodePositions["#nodeP"].y))
                .add(() => {
                    unhighlightNode("#nodeO");
                    highlightNode("#nodeP");
                    showMessage("#completeP");
                })
                .add(() => {
                    hideMessage("#completeP");
                }, "+=1.2")
                .add(() => {
                    unhighlightNode("#nodeP");
                });
        }

        // Initialize animation with proper safety checks
        const initAnimation = () => {
            // Make sure the DOM is fully loaded and rendered
            if (!travelerElement || !container || !flowchartContent) {
                console.error("Critical elements not found in DOM");
                return;
            }

            try {
                calculateNodePositions();

                // Verify we have node positions before starting
                if (Object.keys(nodePositions).length === 0) {
                    console.error("Failed to calculate node positions");
                    return;
                }

                startAnimation();
            } catch (error) {
                console.error("Animation initialization error:", error);
            }
        };

        // Start the animation with sufficient delay for DOM rendering
        setTimeout(initAnimation, 1000);
    }, 1000); // Increased delay for initial setup
});
