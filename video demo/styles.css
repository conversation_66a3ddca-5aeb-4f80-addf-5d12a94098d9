:root {
    /* Modern pharma palette with futuristic accents - light theme */
    --primary-color: #0091A1;    /* Deep<PERSON>l */
    --secondary-color: #6B42B0;  /* Pharmaceutical Purple */
    --success-color: #00B454;    /* Clean Green */
    --warning-color: #E87200;    /* Clear Orange */
    --error-color: #E63535;      /* Soft Red */
    --background-light: #F5F7FC; /* Clean Light Background */
    --background-gradient: #E5EAF5; /* Secondary Light Gradient */
    --card-background: #FFFFFF;  /* White Card Background */
    --text-primary: #2A3350;     /* Dark Navy Text */
    --text-secondary: #4D5B7C;   /* Medium Blue Text */
    --accent-glow: rgba(0, 145, 161, 0.2); /* Subtle Teal Glow */
    --element-blur: 10px;
    --element-border: rgba(0, 145, 161, 0.2);
    --shadow-color: rgba(42, 51, 80, 0.1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--background-light) 0%, var(--background-gradient) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
    color: var(--text-primary);
}

#container {
    width: 1600px;
    height: 800px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    box-shadow: 0 10px 30px var(--shadow-color);
    position: relative;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    backdrop-filter: blur(var(--element-blur));
    border: 1px solid var(--element-border);
}

.flowchart-content {
    position: relative;
    width: 3000px;
    height: 100%;
    padding: 20px;
    min-width: max-content;
}

.node {
    position: absolute;
    width: 150px;
    height: 90px;
    background: var(--card-background);
    border: 1px solid var(--element-border);
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    z-index: 2;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px var(--shadow-color);
    padding: 12px;
}

.node:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 145, 161, 0.15);
    border-color: var(--primary-color);
}

.node.decision {
    background: linear-gradient(145deg, #FFFFFF, #F7FBFF);
    border: 1px solid rgba(107, 66, 176, 0.3);
    box-shadow: 0 4px 15px rgba(107, 66, 176, 0.1);
    border-radius: 50%;
    width: 120px;
    height: 120px;
}

.node.active {
    background: linear-gradient(135deg, rgba(0, 180, 84, 0.05), rgba(0, 145, 161, 0.1));
    border-color: var(--success-color);
    box-shadow: 0 0 20px rgba(0, 180, 84, 0.2);
    animation: nodeGlowLight 2s infinite alternate;
}

@keyframes nodeGlowLight {
    from {
        box-shadow: 0 0 15px rgba(0, 180, 84, 0.1);
    }
    to {
        box-shadow: 0 0 25px rgba(0, 180, 84, 0.2);
    }
}

.icon {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--primary-color);
    filter: drop-shadow(0 0 2px rgba(0, 145, 161, 0.3));
}

.text {
    font-size: 14px;
    color: var(--text-secondary);
    padding: 0 8px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.alert, .email, .complete {
    position: fixed;
    padding: 14px 24px;
    border-radius: 12px;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.3px;
    backdrop-filter: blur(var(--element-blur));
    box-shadow: 0 8px 25px var(--shadow-color);
    z-index: 1000;
    pointer-events: none;
    transform: translateY(20px);
    background: white;
}

.alert {
    color: var(--error-color);
    border-left: 4px solid var(--error-color);
}

.email {
    color: var(--primary-color);
    border-left: 4px solid var(--primary-color);
}

.complete {
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert.show, .email.show, .complete.show {
    opacity: 1;
    transform: translateY(0);
}

#traveler {
    position: absolute;
    width: 40px;
    height: 40px;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

#traveler i {
    color: var(--primary-color);
    font-size: 80px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 0 10px rgba(0, 145, 161, 0.3));
    animation: run 0.5s infinite;
}

@keyframes run {
    0% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-2px) scale(1.05); }
    100% { transform: translateY(0) scale(1); }
}

.line {
    position: absolute;
    background: var(--primary-color);
    opacity: 0.4;
    z-index: 1;
    border-radius: 4px;
    height: 2px;
    box-shadow: 0 0 5px rgba(0, 145, 161, 0.2);
}

.line:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 145, 161, 0.6), transparent);
    animation: shimmer 2.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.phase-label {
    position: absolute;
    font-size: 15px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: 8px;
    backdrop-filter: blur(var(--element-blur));
    border: 1px solid var(--element-border);
    background: rgba(255, 255, 255, 0.9);
}

.phase-separator {
    position: absolute;
    width: 2px;
    background: linear-gradient(to bottom, transparent, var(--primary-color), transparent);
    z-index: 1;
    border-radius: 4px;
    top: 60px;
    bottom: 60px;
    opacity: 0.3;
    box-shadow: 0 0 10px rgba(0, 145, 161, 0.2);
}

.phase-separator:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent, rgba(0, 145, 161, 0.3), transparent);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0% { opacity: 0.3; }
    50% { opacity: 0.6; }
    100% { opacity: 0.3; }
}

/* Additional pharmaceutical-inspired elements */
.node.medication {
    border-left: 4px solid var(--primary-color);
}

.node.patient {
    border-left: 4px solid var(--secondary-color);
}

.node.analysis {
    border-left: 4px solid var(--success-color);
}

/* Clean information display */
.info-panel {
    position: absolute;
    right: 20px;
    top: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 15px;
    width: 280px;
    backdrop-filter: blur(var(--element-blur));
    border: 1px solid var(--element-border);
    box-shadow: 0 4px 20px var(--shadow-color);
}

.info-panel h3 {
    margin-top: 0;
    font-size: 16px;
    color: var(--text-primary);
    letter-spacing: 1px;
    border-bottom: 1px solid var(--element-border);
    padding-bottom: 8px;
}

.stats-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.stats-label {
    color: var(--text-secondary);
    font-size: 13px;
}

.stats-value {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 13px;
}

/* Status indicators with light background */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 8px;
}

.status-badge.active {
    background-color: rgba(0, 180, 84, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(0, 180, 84, 0.2);
}

.status-badge.pending {
    background-color: rgba(232, 114, 0, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(232, 114, 0, 0.2);
}

.status-badge.error {
    background-color: rgba(230, 53, 53, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(230, 53, 53, 0.2);
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

/* Positioning for Specific Elements */
#orderPhase { top: 30px; left: 400px; }
#dailyPhase { top: 30px; left: 1300px; }
#monthlyPhase { top: 30px; left: 2250px; }

#separator1 { left: 1000px; }
#separator2 { left: 2050px; }

#nodeA { top: 450px; left: 150px; }
#nodeB { top: 450px; left: 350px; }
#nodeC { top: 450px; left: 550px; }
#nodeD { top: 450px; left: 800px; }
#nodeE { top: 450px; left: 1050px; }
#nodeF { top: 450px; left: 1300px; }
#nodeG { top: 450px; left: 1550px; }
#nodeK { top: 450px; left: 1850px; }

#nodeX { top: 300px; left: 550px; }
#nodeY { top: 150px; left: 550px; }
#nodeYY { top: 150px; left: 800px; }
#nodeZ { top: 300px; left: 800px; }
#nodeI { top: 300px; left: 1550px; }
#nodeJ { top: 150px; left: 1550px; }
#nodeH { top: 150px; left: 1850px; }

#nodeL { top: 450px; left: 2150px; }
#nodeM { top: 450px; left: 2400px; }
#nodeN { top: 450px; left: 2650px; }
#nodeO { top: 450px; left: 2900px; }
#nodeP { top: 450px; left: 3400px; }
#nodeQ { top: 300px; left: 2900px; }
#nodeR { top: 300px; left: 3150px; }

.alert, .email, .complete {
    bottom: 20px;
    left: 20px;
}

#lineAB { top: 490px; left: 210px; width: 140px; }
#lineBC { top: 490px; left: 410px; width: 140px; }
#lineCD { top: 490px; left: 610px; width: 190px; }
#lineDE { top: 490px; left: 860px; width: 190px; }
#lineEF { top: 490px; left: 1110px; width: 190px; }
#lineFG { top: 490px; left: 1360px; width: 190px; }
#lineGK { top: 490px; left: 1610px; width: 240px; }

#lineCX { top: 340px; left: 620px; width: 4px; height: 110px; }
#lineXY { top: 190px; left: 620px; width: 4px; height: 110px; }
#lineYC { top: 190px; left: 620px; width: 4px; height: 260px; }
#lineDZ { top: 340px; left: 870px; width: 4px; height: 110px; }
#lineZY { top: 190px; left: 870px; width: 4px; height: 110px; }

#lineGH { top: 190px; left: 1620px; width: 4px; height: 260px; }
#lineGI { top: 340px; left: 1620px; width: 4px; height: 110px; }
#lineIJ { top: 190px; left: 1620px; width: 4px; height: 110px; }
#lineJK { top: 190px; left: 1620px; width: 4px; height: 260px; }
#lineHK { top: 190px; left: 1920px; width: 4px; height: 260px; }

#lineKL { top: 490px; left: 1910px; width: 240px; }
#lineLM { top: 490px; left: 2210px; width: 190px; }
#lineMN { top: 490px; left: 2460px; width: 190px; }
#lineNO { top: 490px; left: 2710px; width: 190px; }
#lineOP { top: 490px; left: 2960px; width: 440px; }
#lineOQ { top: 340px; left: 2955px; width: 4px; height: 110px; }
#lineQR { top: 340px; left: 2960px; width: 190px; }
#lineRL { top: 340px; left: 3205px; width: 4px; height: 150px; }