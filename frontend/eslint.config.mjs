import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

/** @type {import('eslint/lib/shared/types').ConfigArray} */
const eslintConfig = [
  {
    ignores: ['**/node_modules/**', '**/.next/**', '**/dist/**'],
  },
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    ...(() => {
      const { useEslintrc, extensions, ...filtered } = compat.config({
        extends: ['next/core-web-vitals'],
      });
      return filtered;
    })(),
    languageOptions: {
      parserOptions: {
        project: './tsconfig.json',
        ecmaVersion: 'latest',
        sourceType: 'module',
      },
    },
  },
];
console.log('ESLint config loaded:', eslintConfig);

export default eslintConfig;
