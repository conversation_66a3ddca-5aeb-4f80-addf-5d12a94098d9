# nextjs-admin

This is the Next.js Admin Dashboard project.

## Getting Started

1. Install dependencies:
   ```
   npm install
   ```

2. Run the development server:
   ```
   npm run dev
   ```

## Storybook Reference

This project uses Storybook as a reference for all new features to ensure design consistency and component reusability.

To start Storybook, run:
   ```
   npm run storybook
   ```

For detailed instructions on integrating and maintaining Storybook, please refer to:
   `storybook-implementation.md`

## Available Scripts

- **npm run dev**: Starts the development server.
- **npm run build**: Builds the project.
- **npm run storybook**: Starts Storybook on port 6006.
- **npm run lint**: Runs the linter.

## Contributing

Please follow the repository guidelines and ensure every new feature includes corresponding Storybook stories.

## License

MIT License.
