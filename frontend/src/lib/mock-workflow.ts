import { WorkflowStep } from '@/components/workflow-stepper';
import { DashboardStats } from '@/lib/api/workflow';

// Mock workflow steps
export const mockWorkflowSteps: WorkflowStep[] = [
  {
    id: 'step-1',
    title: 'Retrieve Orders',
    description: 'Retrieve orders from Drug Comparison',
    status: 'completed',
    link: '/orders',
    helpText: 'This step retrieves all orders from Drug Comparison. You can filter orders by date and supplier.'
  },
  {
    id: 'step-2',
    title: 'Generate Checklist',
    description: 'Create checklist items from orders',
    status: 'in-progress',
    link: '/checklist/generate',
    helpText: 'Select orders to generate checklist items. You can filter orders by date and supplier before generating.'
  },
  {
    id: 'step-3',
    title: 'Process Checklist',
    description: 'Mark items as received, incorrect, or missing',
    status: 'not-started',
    link: '/checklist',
    helpText: 'Process each item in the checklist by marking it as received, incorrect, or missing. You can also add notes for each item.'
  },
  {
    id: 'step-4',
    title: 'Process Invoices',
    description: 'Upload and process invoices',
    status: 'not-started',
    link: '/invoices',
    helpText: 'Upload invoice PDFs or process invoices from emails. The system will extract invoice data automatically.'
  },
  {
    id: 'step-5',
    title: 'Reconcile Invoices',
    description: 'Compare invoices with orders',
    status: 'not-started',
    link: '/reconciliation',
    helpText: 'Compare invoices with orders to identify discrepancies. You can resolve discrepancies by adding notes and marking them as resolved.'
  },
  {
    id: 'step-6',
    title: 'Process Statements',
    description: 'Upload and process statements',
    status: 'not-started',
    link: '/statements',
    helpText: 'Upload statement PDFs or process statements from emails. The system will extract statement data automatically.'
  },
  {
    id: 'step-7',
    title: 'Reconcile Statements',
    description: 'Compare statements with invoices',
    status: 'not-started',
    link: '/statements/reconcile',
    helpText: 'Compare statements with invoices to identify discrepancies. You can resolve discrepancies by adding notes and marking them as resolved.'
  },
  {
    id: 'step-8',
    title: 'Create Credit Requests',
    description: 'Request credit for missing or incorrect items',
    status: 'not-started',
    link: '/credit-requests/create',
    helpText: 'Create credit requests for missing or incorrect items. You can select items from the checklist and add details for each request.'
  },
  {
    id: 'step-9',
    title: 'Generate Reports',
    description: 'Generate reports for reconciliation',
    status: 'not-started',
    link: '/reports',
    helpText: 'Generate reports for reconciliation. You can select the report type, date range, and other parameters.'
  }
];

// Mock dashboard stats
export const mockDashboardStats: DashboardStats = {
  orders: {
    total: 45,
    pending: 12,
    processed: 33
  },
  checklist: {
    total: 156,
    pending: 42,
    received: 98,
    incorrect: 8,
    missing: 8
  },
  invoices: {
    total: 28,
    reconciled: 22,
    unreconciled: 6
  },
  statements: {
    total: 3,
    reconciled: 1,
    unreconciled: 2
  },
  creditRequests: {
    total: 12,
    pending: 3,
    sent: 5,
    approved: 3,
    rejected: 1
  }
};

// Helper functions
export function getCurrentWorkflowStep(): WorkflowStep {
  const inProgressStep = mockWorkflowSteps.find(step => step.status === 'in-progress');
  if (inProgressStep) {
    return inProgressStep;
  }
  
  // If no step is in progress, find the first not-started step
  const nextStep = mockWorkflowSteps.find(step => step.status === 'not-started');
  if (nextStep) {
    return nextStep;
  }
  
  // If all steps are completed, return the last step
  return mockWorkflowSteps[mockWorkflowSteps.length - 1];
}

export function getWorkflowStepById(id: string): WorkflowStep | undefined {
  return mockWorkflowSteps.find(step => step.id === id);
}

export function getNextWorkflowStep(currentStepId: string): WorkflowStep | undefined {
  const currentIndex = mockWorkflowSteps.findIndex(step => step.id === currentStepId);
  if (currentIndex === -1 || currentIndex === mockWorkflowSteps.length - 1) {
    return undefined;
  }
  return mockWorkflowSteps[currentIndex + 1];
}

export function getPreviousWorkflowStep(currentStepId: string): WorkflowStep | undefined {
  const currentIndex = mockWorkflowSteps.findIndex(step => step.id === currentStepId);
  if (currentIndex <= 0) {
    return undefined;
  }
  return mockWorkflowSteps[currentIndex - 1];
}