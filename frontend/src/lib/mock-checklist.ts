import { ChecklistItem } from '@/types/checklist';

export const mockChecklistItems: ChecklistItem[] = [
  {
    id: '1',
    orderId: 'ORD001',
    productName: 'Paracetamol 500mg Tablets',
    pipCode: 'PIP123456',
    expectedQuantity: 100,
    receivedQuantity: 100,
    status: 'received',
    notes: 'All items received in good condition',
    dateChecked: new Date('2025-03-24T10:00:00'),
    checkedBy: '<PERSON>'
  },
  {
    id: '2',
    orderId: 'ORD001',
    productName: 'Ibuprofen 200mg Tablets',
    pipCode: 'PIP789012',
    expectedQuantity: 50,
    receivedQuantity: 45,
    status: 'incorrect',
    notes: 'Short delivery - 5 packs missing',
    dateChecked: new Date('2025-03-24T10:05:00'),
    checkedBy: '<PERSON>'
  },
  {
    id: '3',
    orderId: 'ORD002',
    productName: 'Amoxicillin 250mg Capsules',
    pipCode: 'PIP345678',
    expectedQuantity: 30,
    receivedQuantity: null,
    status: 'pending',
    notes: '',
    dateChecked: null,
    checkedBy: ''
  },
  {
    id: '4',
    orderId: 'ORD002',
    productName: 'Omeprazole 20mg Tablets',
    pipCode: 'PIP901234',
    expectedQuantity: 20,
    receivedQuantity: 0,
    status: 'missing',
    notes: 'Item not included in delivery',
    dateChecked: new Date('2025-03-24T11:15:00'),
    checkedBy: 'Jane Smith'
  },
  {
    id: '5',
    orderId: 'ORD003',
    productName: 'Simvastatin 40mg Tablets',
    pipCode: 'PIP567890',
    expectedQuantity: 40,
    receivedQuantity: null,
    status: 'pending',
    notes: '',
    dateChecked: null,
    checkedBy: ''
  }
];
