import { Statement, StatementItem } from '@/types/statement';
import { addDays, subDays } from 'date-fns';

// Create a date for the current month's statement period
const currentDate = new Date();
const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

// Create dates for invoices within the statement period
const invoiceDate1 = new Date(startOfMonth.getTime() + Math.random() * (endOfMonth.getTime() - startOfMonth.getTime()));
const invoiceDate2 = new Date(startOfMonth.getTime() + Math.random() * (endOfMonth.getTime() - startOfMonth.getTime()));
const invoiceDate3 = new Date(startOfMonth.getTime() + Math.random() * (endOfMonth.getTime() - startOfMonth.getTime()));
const invoiceDate4 = new Date(startOfMonth.getTime() + Math.random() * (endOfMonth.getTime() - startOfMonth.getTime()));

// Create a date for the previous month's statement period
const prevMonthStart = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
const prevMonthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);

// Create dates for invoices within the previous statement period
const prevInvoiceDate1 = new Date(prevMonthStart.getTime() + Math.random() * (prevMonthEnd.getTime() - prevMonthStart.getTime()));
const prevInvoiceDate2 = new Date(prevMonthStart.getTime() + Math.random() * (prevMonthEnd.getTime() - prevMonthStart.getTime()));

export const mockStatements: Statement[] = [
  {
    id: 'st-001',
    supplier: 'AAH Pharmaceuticals',
    period: {
      start: startOfMonth,
      end: endOfMonth
    },
    invoices: [
      {
        invoiceId: 'INV-AAH-001',
        date: invoiceDate1,
        amount: 1250.75,
        status: 'matched'
      },
      {
        invoiceId: 'INV-AAH-002',
        date: invoiceDate2,
        amount: 875.50,
        status: 'unmatched',
        discrepancies: [
          {
            field: 'Total Amount',
            statementValue: 875.50,
            invoiceValue: 825.50,
            difference: 50.00
          }
        ]
      },
      {
        invoiceId: 'INV-AAH-003',
        date: invoiceDate3,
        amount: 320.25,
        status: 'partial',
        discrepancies: [
          {
            field: 'Item Count',
            statementValue: 15,
            invoiceValue: 14,
            difference: 1
          }
        ]
      }
    ],
    totalAmount: 2446.50,
    reconciled: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'st-002',
    supplier: 'Phoenix Healthcare',
    period: {
      start: startOfMonth,
      end: endOfMonth
    },
    invoices: [
      {
        invoiceId: 'INV-PHX-001',
        date: invoiceDate1,
        amount: 950.25,
        status: 'matched'
      },
      {
        invoiceId: 'INV-PHX-002',
        date: invoiceDate4,
        amount: 1125.75,
        status: 'matched'
      }
    ],
    totalAmount: 2076.00,
    reconciled: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'st-003',
    supplier: 'Alliance Healthcare',
    period: {
      start: prevMonthStart,
      end: prevMonthEnd
    },
    invoices: [
      {
        invoiceId: 'INV-ALH-001',
        date: prevInvoiceDate1,
        amount: 1875.50,
        status: 'matched'
      },
      {
        invoiceId: 'INV-ALH-002',
        date: prevInvoiceDate2,
        amount: 725.25,
        status: 'unmatched',
        discrepancies: [
          {
            field: 'Total Amount',
            statementValue: 725.25,
            invoiceValue: 775.25,
            difference: -50.00
          },
          {
            field: 'VAT',
            statementValue: 120.88,
            invoiceValue: 129.21,
            difference: -8.33
          }
        ]
      }
    ],
    totalAmount: 2600.75,
    reconciled: false,
    createdAt: subDays(new Date(), 30),
    updatedAt: subDays(new Date(), 30)
  }
];

// Helper function to get a statement by ID
export function getStatementById(id: string): Statement | undefined {
  return mockStatements.find(statement => statement.id === id);
}

// Helper function to get statements by supplier
export function getStatementsBySupplier(supplier: string): Statement[] {
  return mockStatements.filter(statement => statement.supplier === supplier);
}

// Helper function to get statements by date range
export function getStatementsByDateRange(startDate: Date, endDate: Date): Statement[] {
  return mockStatements.filter(statement => {
    return statement.period.start >= startDate && statement.period.end <= endDate;
  });
}