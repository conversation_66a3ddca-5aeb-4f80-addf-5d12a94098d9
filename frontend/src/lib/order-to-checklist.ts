import { Order } from './csv-utils';
import { ChecklistItem } from '@/types/checklist';

/**
 * Converts an order to a checklist item
 * @param order The order to convert
 * @param userId The ID of the user creating the checklist item
 * @returns A checklist item based on the order
 */
export function orderToChecklistItem(order: Order, userId: string): ChecklistItem {
  return {
    id: `cl-${order.orderNo}-${order.pipCode}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    orderId: order.orderNo,
    productName: order.description,
    pipCode: order.pipCode,
    expectedQuantity: order.approvedQty,
    receivedQuantity: null,
    status: 'pending',
    notes: '',
    dateChecked: null,
    checkedBy: userId,
  };
}

/**
 * Converts multiple orders to checklist items
 * @param orders The orders to convert
 * @param userId The ID of the user creating the checklist items
 * @returns An array of checklist items based on the orders
 */
export function ordersToChecklistItems(orders: Order[], userId: string): ChecklistItem[] {
  return orders.map(order => orderToChecklistItem(order, userId));
}

/**
 * Filters orders by supplier and date range
 * @param orders The orders to filter
 * @param supplier The supplier to filter by (optional)
 * @param startDate The start date to filter by (optional)
 * @param endDate The end date to filter by (optional)
 * @returns Filtered orders
 */
export function filterOrders(
  orders: Order[],
  supplier?: string,
  startDate?: Date,
  endDate?: Date
): Order[] {
  console.log('Filtering orders with:', {
    supplier,
    startDate: startDate?.toISOString(),
    endDate: endDate?.toISOString(),
    totalOrders: orders.length
  });
  
  // Parse dates once outside the filter loop
  let startDateObj: Date | undefined;
  let endDateObj: Date | undefined;
  
  if (startDate) {
    startDateObj = new Date(startDate);
    startDateObj.setHours(0, 0, 0, 0);
    console.log('Start date for filtering:', startDateObj);
  }
  
  if (endDate) {
    endDateObj = new Date(endDate);
    endDateObj.setHours(23, 59, 59, 999);
    console.log('End date for filtering:', endDateObj);
  }
  
  const filtered = orders.filter(order => {
    // Filter by supplier if provided
    if (supplier) {
      if (!order.supplier) {
        return false;
      }
      
      const normalizedOrderSupplier = order.supplier.toLowerCase().trim();
      const normalizedSupplier = supplier.toLowerCase().trim();
      
      if (normalizedOrderSupplier !== normalizedSupplier) {
        return false;
      }
    }
    
    // Filter by date range if provided
    if (startDateObj || endDateObj) {
      if (!order.dateTime) {
        return false;
      }
      
      // Try different date formats
      let orderDate: Date | null = null;
      
      // Try DD-MM-YYYY format (26-02-2025)
      if (order.dateTime.match(/^\d{2}-\d{2}-\d{4}/)) {
        const [day, month, year] = order.dateTime.split(' ')[0].split('-').map(Number);
        orderDate = new Date(year, month - 1, day);
      }
      // Try standard ISO format
      else {
        orderDate = new Date(order.dateTime);
      }
      
      if (!orderDate || isNaN(orderDate.getTime())) {
        console.log('Invalid date format:', order.dateTime);
        return false;
      }
      
      console.log('Order date parsed:', {
        original: order.dateTime,
        parsed: orderDate
      });
      
      if (startDateObj && orderDate < startDateObj) {
        return false;
      }
      
      if (endDateObj && orderDate > endDateObj) {
        return false;
      }
    }
    
    return true;
  });
  
  console.log('Filtered orders:', {
    filteredCount: filtered.length
  });
  
  return filtered;
}