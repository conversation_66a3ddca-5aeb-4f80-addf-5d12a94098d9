export interface InvoiceItem {
  Amount: number;
  unitPrice: number;
  Description: string;
  Quantity: number;
  itemId: number;
  pipCode: string;
  packSize: string;
}

export interface Invoice {
  VendorName: string;
  CustomerName: string;
  InvoiceDate: string;
  DueDate: string;
  InvoiceId: string;
  CustomerAddress: string;
  CustomerAddressRecipient: string;
  CustomerId: string;
  ShippingAddress: string;
  ShippingAddressRecipient: string;
  VendorAddress: string;
  VendorAddressRecipient: string;
  VendorTaxId: string;
  InvoiceTotal: number;
  Items: InvoiceItem[];
  orderReconciled: boolean;
  statementReconciled: boolean;
}

export const mockInvoices: Invoice[] = [
  {
    VendorName: "AAH",
    CustomerName: "NIHAL HEALTHCARE LTD, T/A DEDHAM PHARMACY",
    InvoiceDate: "31/03/2023",
    DueDate: "28/04/2023",
    InvoiceId: "17226735T",
    CustomerAddress: "BRANNAN CT HIGH STREET, DEDHAM, CO7 6DE",
    CustomerAddressRecipient: "DEDHAM PHARMACY HIGH ST",
    CustomerId: "706S11002609D",
    ShippingAddress: "BRANNAN CT HIGH STREET, DEDHAM, CO7 6DE",
    ShippingAddressRecipient: "DEDHAM PHARMACY",
    VendorAddress: "Paradise Way, Sapphire Court, Walsgrave Triangle, CV2 2TX",
    VendorAddressRecipient: "AAH Pharmaceuticals Ltd",
    VendorTaxId: "GB222516987",
    InvoiceTotal: 274.90,
    orderReconciled: true,
    statementReconciled: true,
    Items: [
      {
        Amount: 2.87,
        unitPrice: 2.39,
        Description: "DOXYCYCLINE CAP 100MG",
        Quantity: 1,
        itemId: 1,
        pipCode: "DOX0017H",
        packSize: "50"
      },
      {
        Amount: 41.62,
        unitPrice: 34.68,
        Description: "EPILIM 500MG EC TABS",
        Quantity: 1,
        itemId: 2,
        pipCode: "EPI0157Y",
        packSize: "30TAB"
      },
      {
        Amount: 99.29,
        unitPrice: 82.74,
        Description: "PARACETAMOL SUSP 250MG/5ML",
        Quantity: 1,
        itemId: 3,
        pipCode: "PAR0595F",
        packSize: "500ML"
      },
      {
        Amount: 176.26,
        unitPrice: 146.88,
        Description: "TRILEPTAL SUSPENSION",
        Quantity: 1,
        itemId: 4,
        pipCode: "TRI0732E",
        packSize: "250ML"
      },
      {
        Amount: 3.16,
        unitPrice: 2.63,
        Description: "XAILIN NIGHT",
        Quantity: 1,
        itemId: 5,
        pipCode: "XAI0001X",
        packSize: "5G"
      },
      {
        Amount: 6.70,
        unitPrice: 5.58,
        Description: "ZEROBASE CREAM PUMP DISPENSER",
        Quantity: 1,
        itemId: 6,
        pipCode: "ZER0038X",
        packSize: "500G"
      }
    ]
  },
  {
    VendorName: "AAH",
    CustomerName: "NIHAL HEALTHCARE LTD, T/A DEDHAM PHARMACY",
    InvoiceDate: "15/04/2023",
    DueDate: "15/05/2023",
    InvoiceId: "17226736T",
    CustomerAddress: "BRANNAN CT HIGH STREET, DEDHAM, CO7 6DE",
    CustomerAddressRecipient: "DEDHAM PHARMACY HIGH ST",
    CustomerId: "706S11002609D",
    ShippingAddress: "BRANNAN CT HIGH STREET, DEDHAM, CO7 6DE",
    ShippingAddressRecipient: "DEDHAM PHARMACY",
    VendorAddress: "Paradise Way, Sapphire Court, Walsgrave Triangle, CV2 2TX",
    VendorAddressRecipient: "AAH Pharmaceuticals Ltd",
    VendorTaxId: "GB222516987",
    InvoiceTotal: 156.45,
    orderReconciled: false,
    statementReconciled: true,
    Items: [
      {
        Amount: 41.62,
        unitPrice: 34.68,
        Description: "EPILIM 500MG EC TABS",
        Quantity: 1,
        itemId: 1,
        pipCode: "EPI0157Y",
        packSize: "30TAB"
      },
      {
        Amount: 99.29,
        unitPrice: 82.74,
        Description: "PARACETAMOL SUSP 250MG/5ML",
        Quantity: 1,
        itemId: 2,
        pipCode: "PAR0595F",
        packSize: "500ML"
      }
    ]
  }
];