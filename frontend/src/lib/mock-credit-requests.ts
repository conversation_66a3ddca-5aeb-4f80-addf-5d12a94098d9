import { CreditRequest } from "@/types/credit-request";

export const mockCreditRequests: CreditRequest[] = [
  {
    id: "CR001",
    orderId: "ORD123",
    invoiceId: "INV456",
    supplier: "AAH",
    status: "sent",
    requestDate: new Date("2025-03-20"),
    items: [
      {
        productName: "Paracetamol 500mg Tablets",
        pipCode: "PIP123456",
        quantity: 2,
        reason: "missing",
        details: "Items missing from delivery",
        originalUnitPrice: 2.99,
        originalTotal: 5.98
      }
    ],
    emailHistory: [
      {
        id: "EM001",
        sentDate: new Date("2025-03-20T10:00:00"),
        to: ["<EMAIL>"],
        subject: "Credit Request - Order ORD123",
        content: "Please issue credit note for missing items...",
        status: "sent"
      }
    ],
    notes: "First request sent"
  },
  {
    id: "CR002",
    orderId: "ORD124",
    invoiceId: "INV457",
    supplier: "Phoenix",
    status: "approved",
    requestDate: new Date("2025-03-19"),
    responseDate: new Date("2025-03-21"),
    creditNoteNumber: "CN789",
    items: [
      {
        productName: "Ibuprofen 200mg Tablets",
        pipCode: "PIP789012",
        quantity: 1,
        reason: "damaged",
        details: "Box crushed during delivery",
        originalUnitPrice: 3.99,
        originalTotal: 3.99,
        images: ["https://example.com/damage1.jpg"]
      }
    ],
    emailHistory: [
      {
        id: "EM002",
        sentDate: new Date("2025-03-19T14:30:00"),
        to: ["<EMAIL>"],
        subject: "Credit Request - Order ORD124",
        content: "Please issue credit note for damaged items...",
        status: "sent",
        response: {
          date: new Date("2025-03-21T09:15:00"),
          content: "Credit note CN789 issued for damaged items"
        }
      }
    ],
    notes: "Credit note received"
  },
  {
    id: "CR003",
    orderId: "ORD125",
    invoiceId: "INV458",
    supplier: "Alliance",
    status: "pending",
    requestDate: new Date("2025-03-24"),
    items: [
      {
        productName: "Amoxicillin 250mg Capsules",
        pipCode: "PIP345678",
        quantity: 3,
        reason: "incorrect",
        details: "Received 500mg instead of 250mg",
        originalUnitPrice: 5.99,
        originalTotal: 17.97
      }
    ],
    emailHistory: [],
    notes: "Awaiting initial email to be sent"
  }
];
