import { Invoice, ApiResponse, UploadResponse, OrdersResponse, AuthResponse, UserSettings, SettingsResponse, EmailProcessingResult } from '@/types/api';
import { ChecklistItem } from '@/types/checklist';

export const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000"; // Add export
// Initialize auth token from cookie
const getStoredToken = () => {
  if (typeof document === 'undefined') return null; // Handle server-side rendering
  const cookies = document.cookie.split(';');
  const tokenCookie = cookies.find(cookie => cookie.trim().startsWith('auth-token='));
  return tokenCookie ? tokenCookie.split('=')[1].trim() : null;
};

// Function to refresh the auth token from cookie
export const refreshAuthToken = () => {
  authToken = getStoredToken();
  return authToken;
};

let authToken: string | null = getStoredToken();

class ApiError extends Error {
  constructor(public statusCode: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function handleResponse<T>(response: Response): Promise<T> {
  const contentType = response.headers.get('content-type');
  
  if (!response.ok) {
    let errorMessage = 'An error occurred';
    if (contentType?.includes('application/json')) {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || errorData.error || errorMessage;
    }
    throw new ApiError(response.status, errorMessage);
  }

  if (!contentType?.includes('application/json')) {
    throw new Error('Invalid response format');
  }

  return response.json();
}

// Add auth token to all API requests
export const withAuth = (headers: HeadersInit = {}) => {
  if (authToken) {
    return {
      ...headers,
      'Authorization': `Bearer ${authToken}`
    };
  }
  return headers;
};

export const authApi = {
  login: async (email: string, password: string) => {
    try {
      // Use the Next.js API route instead of directly accessing the backend
      const response = await fetch(`/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password }),
        credentials: 'include'
      });
  
      const data = await handleResponse<AuthResponse>(response);
      if (data.success && data.token) {
        authToken = data.token;
        // The cookie is now set by the API route with HttpOnly flag
      }
      return data;
    } catch (error) {
      console.error('Error during login:', error);
      throw error instanceof ApiError ? error : new Error('Login failed');
    }
  },

  logout: () => {
    authToken = null;
    document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
  },

  getSettings: async () => {
    try {
      // Use the Next.js API route instead of directly accessing the backend
      const response = await fetch(`/api/auth/settings`, {
        headers: {
          'Accept': 'application/json'
        }
      });

      return handleResponse<SettingsResponse>(response);
    } catch (error) {
      console.error('Error fetching settings:', error);
      throw error instanceof ApiError ? error : new Error('Failed to fetch settings');
    }
  },

  updateSettings: async (settings: UserSettings) => {
    try {
      // Use the Next.js API route instead of directly accessing the backend
      const response = await fetch(`/api/auth/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings),
      });

      return handleResponse<ApiResponse<void>>(response);
    } catch (error) {
      console.error('Error updating settings:', error);
      throw error instanceof ApiError ? error : new Error('Failed to update settings');
    }

  }
  ,
  // DEVELOPMENT ONLY: Fetch password for test user
  devGetPassword: async (email: string) => {
    try {
      const response = await fetch(`/api/auth/dev-get-password?email=${encodeURIComponent(email)}`);
      const data = await handleResponse<{ success: boolean; password?: string; error?: string }>(response);
      return data;
    } catch (error) {
      console.error('Error fetching dev password:', error);
      return { success: false, error: 'Failed to fetch password' };
    }
  },

  // Request password reset email
  resetPassword: async (email: string) => {
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      return handleResponse<{ success: boolean; error?: string }>(response);
    } catch (error) {
      console.error('Error sending password reset:', error);
      return { success: false, error: 'Failed to send password reset email' };
    }
  }
};

export const ordersApi = {
  retrieveOrders: async (startDate?: string, endDate?: string): Promise<OrdersResponse> => {
    try {
      const response = await fetch('/api/orders/retrieve', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          startDate,
          endDate
        })
      });
      
      return handleResponse<OrdersResponse>(response);
    } catch (error) {
      console.error('Error retrieving orders:', error);
      throw error instanceof ApiError ? error : new Error('Failed to retrieve orders');
    }
  }
};

export const checklistApi = {
  getItems: async (): Promise<ChecklistItem[]> => {
    try {
      // Use Next.js API route instead of direct backend call
      const response = await fetch(`/api/checklist/items`, {
        headers: {
          'Accept': 'application/json'
        }
      });
      
      return handleResponse<ChecklistItem[]>(response);
    } catch (error) {
      console.error('Error fetching checklist items:', error);
      throw error instanceof ApiError ? error : new Error('Failed to fetch checklist items');
    }
  },

  updateItem: async (id: string, data: Partial<ChecklistItem>): Promise<ChecklistItem> => {
    try {
      // Use Next.js API route instead of direct backend call
      const response = await fetch(`/api/checklist/items/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });
      
      return handleResponse<ChecklistItem>(response);
    } catch (error) {
      console.error('Error updating checklist item:', error);
      throw error instanceof ApiError ? error : new Error('Failed to update checklist item');
    }
  },

  bulkUpdate: async (items: { id: string; data: Partial<ChecklistItem> }[]): Promise<ChecklistItem[]> => {
    try {
      // Use Next.js API route instead of direct backend call
      const response = await fetch(`/api/checklist/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(items)
      });
      
      return handleResponse<ChecklistItem[]>(response);
    } catch (error) {
      console.error('Error bulk updating checklist items:', error);
      throw error instanceof ApiError ? error : new Error('Failed to bulk update checklist items');
    }
  }
};

export const invoicesApi = {
  uploadInvoice: async (
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<any> => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const promise = new Promise<any>((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        const backendUrl = (typeof process !== 'undefined' && process.env && process.env.NEXT_PUBLIC_BACKEND_URL)
          ? process.env.NEXT_PUBLIC_BACKEND_URL
          : 'http://localhost:3000';
        xhr.open('POST', `${backendUrl}/api/invoices/process`);
        xhr.withCredentials = true; // Send HttpOnly cookies automatically

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable && onProgress) {
            const percent = Math.round((event.loaded / event.total) * 100);
            onProgress(percent);
          }
        });

        xhr.onreadystatechange = () => {
          if (xhr.readyState === XMLHttpRequest.DONE) {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                resolve(JSON.parse(xhr.responseText));
              } catch (e) {
                resolve({});
              }
            } else {
              let message = xhr.responseText || 'Upload failed';
              if (typeof message === 'string' && (message.startsWith('<!DOCTYPE') || message.startsWith('<html'))) {
                message = 'Upload failed: server returned an unexpected response (404 or HTML error)';
              }
              reject(new Error(message));
            }
          }
        };

        xhr.addEventListener('error', () => {
          reject(new Error('Network error during upload'));
        });

        // Removed manual header setting - cookie will be sent via withCredentials

        xhr.send(formData);
      });

      return await promise;
    } catch (error) {
      console.error('Error uploading invoice:', error);
      throw error;
    }
  },
  processEmail: async (): Promise<EmailProcessingResult> => {
    try {
      const response = await fetch('/api/emails/process', {
        method: 'POST',
        headers: {
          'Accept': 'application/json'
        }
      });
      
      const data = await handleResponse<EmailProcessingResult>(response);
      return {
        message: data.message,
        processed: data.processed || 0,
        errors: data.errors || [],
        details: data.details || []
      };
    } catch (error) {
      console.error('Error processing email:', error);
      throw error instanceof ApiError ? error : new Error('Failed to process email');
    }
  },

  getInvoice: async (id: string): Promise<Invoice> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`/api/invoices/${id}`, {
        method: 'GET',
        headers: withAuth({
          'Accept': 'application/json'
        }),
        credentials: 'include'
      });
      
      const invoice = await handleResponse<Invoice>(response);
      
      return {
        ...invoice,
        orderReconciled: invoice.orderReconciled || false,
        statementReconciled: invoice.statementReconciled || false,
        Items: invoice.Items || [],
        InvoiceDate: invoice.InvoiceDate,
        DueDate: invoice.DueDate,
        id: invoice.id || invoice.InvoiceId,
        VendorName: invoice.VendorName || 'Unknown Vendor',
        CustomerName: invoice.CustomerName || '',
        InvoiceTotal: invoice.InvoiceTotal || 0
      };
    } catch (error) {
      console.error('Error fetching invoice:', error);
      throw error instanceof ApiError ? error : new Error('Failed to fetch invoice');
    }
  },

  getInvoices: async (): Promise<Invoice[]> => {
    try {
      // Use Next.js API route instead of direct backend call
      const response = await fetch('/api/invoices', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      
      const data = await handleResponse<Invoice[]>(response);
      
      return data.map(invoice => ({
        ...invoice,
        orderReconciled: invoice.orderReconciled || false,
        statementReconciled: invoice.statementReconciled || false,
        Items: invoice.Items || [],
        InvoiceDate: invoice.InvoiceDate,
        DueDate: invoice.DueDate,
        id: invoice.id || invoice.InvoiceId,
        VendorName: invoice.VendorName || 'Unknown Vendor',
        CustomerName: invoice.CustomerName || '',
        InvoiceTotal: invoice.InvoiceTotal || 0
      }));
    } catch (error) {
      console.error('Error fetching invoices:', error);
      throw error instanceof ApiError ? error : new Error('Failed to fetch invoices');
    }
  },

  deleteInvoices: async (ids: string[]): Promise<ApiResponse<void>> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/invoices/delete`, {
        method: 'POST',
        headers: withAuth({
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }),
        body: JSON.stringify({ ids })
      });
      
      return handleResponse<ApiResponse<void>>(response);
    } catch (error) {
      console.error('Error deleting invoices:', error);
      throw error instanceof ApiError ? error : new Error('Failed to delete invoices');
    }
  },
};
