import { Notification } from '@/components/notification-center';
import { Activity, ActivityType } from '@/components/activity-log';
import { subHours, subDays, subMinutes } from 'date-fns';

// Mock notifications
export const mockNotifications: Notification[] = [
  {
    id: 'notif-001',
    title: 'New Invoice Received',
    message: 'A new invoice from AAH Pharmaceuticals has been received and needs to be processed.',
    type: 'info',
    timestamp: subHours(new Date(), 1),
    read: false,
    link: '/invoices/INV-AAH-004',
    linkText: 'View Invoice'
  },
  {
    id: 'notif-002',
    title: 'Order Discrepancy Detected',
    message: 'An order from Phoenix Healthcare has discrepancies that need to be resolved.',
    type: 'warning',
    timestamp: subHours(new Date(), 3),
    read: false,
    link: '/reconciliation/ORD-PHX-002',
    linkText: 'View Discrepancies'
  },
  {
    id: 'notif-003',
    title: 'Credit Request Approved',
    message: 'Your credit request CR001 for Alliance Healthcare has been approved.',
    type: 'success',
    timestamp: subHours(new Date(), 5),
    read: true,
    link: '/credit-requests/CR001',
    linkText: 'View Credit Request'
  },
  {
    id: 'notif-004',
    title: 'Statement Reconciliation Required',
    message: 'The monthly statement from Bestway Pharmacy has been received and needs to be reconciled.',
    type: 'info',
    timestamp: subDays(new Date(), 1),
    read: false,
    link: '/statements/st-004',
    linkText: 'View Statement'
  },
  {
    id: 'notif-005',
    title: 'Credit Request Rejected',
    message: 'Your credit request CR002 for Sigma Pharmaceuticals has been rejected.',
    type: 'error',
    timestamp: subDays(new Date(), 2),
    read: true,
    link: '/credit-requests/CR002',
    linkText: 'View Credit Request'
  }
];

// Mock activities
export const mockActivities: Activity[] = [
  {
    id: 'act-001',
    type: 'order',
    action: 'Order Retrieved',
    description: 'Retrieved new orders from Drug Comparison',
    timestamp: subMinutes(new Date(), 15),
    user: {
      id: 'user-001',
      name: 'John Smith'
    },
    details: {
      'Orders Retrieved': 12,
      'Supplier': 'Multiple'
    }
  },
  {
    id: 'act-002',
    type: 'checklist',
    action: 'Checklist Generated',
    description: 'Generated checklist items from orders',
    timestamp: subMinutes(new Date(), 30),
    user: {
      id: 'user-001',
      name: 'John Smith'
    },
    details: {
      'Items Generated': 45,
      'From Orders': 8
    }
  },
  {
    id: 'act-003',
    type: 'checklist',
    action: 'Items Checked',
    description: 'Marked checklist items as received',
    timestamp: subHours(new Date(), 2),
    user: {
      id: 'user-002',
      name: 'Jane Doe'
    },
    details: {
      'Items Received': 32,
      'Items Missing': 3,
      'Items Incorrect': 2
    }
  },
  {
    id: 'act-004',
    type: 'invoice',
    action: 'Invoice Processed',
    description: 'Processed invoice from AAH Pharmaceuticals',
    timestamp: subHours(new Date(), 3),
    user: {
      id: 'user-002',
      name: 'Jane Doe'
    },
    details: {
      'Invoice ID': 'INV-AAH-003',
      'Amount': '£320.25'
    }
  },
  {
    id: 'act-005',
    type: 'statement',
    action: 'Statement Reconciled',
    description: 'Reconciled statement from Phoenix Healthcare',
    timestamp: subHours(new Date(), 4),
    user: {
      id: 'user-001',
      name: 'John Smith'
    },
    details: {
      'Statement ID': 'st-002',
      'Period': 'March 2025',
      'Invoices': 2,
      'Total Amount': '£2076.00'
    }
  },
  {
    id: 'act-006',
    type: 'credit-request',
    action: 'Credit Request Created',
    description: 'Created credit request for missing items',
    timestamp: subDays(new Date(), 1),
    user: {
      id: 'user-002',
      name: 'Jane Doe'
    },
    details: {
      'Request ID': 'CR003',
      'Supplier': 'AAH Pharmaceuticals',
      'Items': 3,
      'Total Amount': '£156.75'
    }
  },
  {
    id: 'act-007',
    type: 'credit-request',
    action: 'Credit Request Approved',
    description: 'Credit request approved by supplier',
    timestamp: subDays(new Date(), 2),
    user: {
      id: 'user-001',
      name: 'John Smith'
    },
    details: {
      'Request ID': 'CR001',
      'Supplier': 'Alliance Healthcare',
      'Total Amount': '£210.50'
    }
  },
  {
    id: 'act-008',
    type: 'user',
    action: 'User Login',
    description: 'User logged in to the system',
    timestamp: subMinutes(new Date(), 5),
    user: {
      id: 'user-001',
      name: 'John Smith'
    }
  },
  {
    id: 'act-009',
    type: 'user',
    action: 'Settings Updated',
    description: 'User updated notification settings',
    timestamp: subDays(new Date(), 3),
    user: {
      id: 'user-002',
      name: 'Jane Doe'
    },
    details: {
      'Email Notifications': 'Enabled',
      'Browser Notifications': 'Disabled'
    }
  },
  {
    id: 'act-010',
    type: 'invoice',
    action: 'Invoice Uploaded',
    description: 'Manually uploaded invoice PDF',
    timestamp: subDays(new Date(), 4),
    user: {
      id: 'user-001',
      name: 'John Smith'
    },
    details: {
      'Invoice ID': 'INV-ALH-002',
      'Supplier': 'Alliance Healthcare',
      'Amount': '£775.25'
    }
  }
];

// Helper functions
export function getUnreadNotificationsCount(): number {
  return mockNotifications.filter(notification => !notification.read).length;
}

export function getNotificationById(id: string): Notification | undefined {
  return mockNotifications.find(notification => notification.id === id);
}

export function getActivitiesByType(type: ActivityType): Activity[] {
  return mockActivities.filter(activity => activity.type === type);
}

export function getActivitiesByUser(userId: string): Activity[] {
  return mockActivities.filter(activity => activity.user.id === userId);
}

export function getActivitiesByDateRange(startDate: Date, endDate: Date): Activity[] {
  return mockActivities.filter(activity => {
    return activity.timestamp >= startDate && activity.timestamp <= endDate;
  });
}