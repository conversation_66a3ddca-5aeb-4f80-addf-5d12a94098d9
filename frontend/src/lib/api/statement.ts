import { Statement } from '@/types/statement';
import { API_URL } from '../api'; // Import API_URL

// Get all statements
export const getStatements = async (): Promise<Statement[]> => {
  try {
    const response = await fetch('/api/statements', {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch statements');
    }

    const data = await response.json();
    return data.map((statement: any) => ({
      ...statement,
      id: statement.id || statement.StatementId,
      StatementId: statement.id || statement.StatementId,
      period: {
        start: statement.period?.start ? new Date(statement.period.start) : null,
        end: statement.period?.end ? new Date(statement.period.end) : null
      },
      createdAt: statement.createdAt ? new Date(statement.createdAt) : null,
      updatedAt: statement.updatedAt ? new Date(statement.updatedAt) : null
    }));
  } catch (error) {
    console.error('Error fetching statements:', error);
    throw error;
  }
};

// Get statement by ID
export const getStatementById = async (id: string, token: string): Promise<Statement> => { // Add token parameter
  try {
    if (!API_URL) throw new Error('API URL is not configured'); // Add check for API_URL
    const response = await fetch(`${API_URL}/api/statements/${id}`, { // Prepend API_URL
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}` // Add Authorization header
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch statement');
    }

    const data = await response.json();
    return {
      ...data.statement,
      id: data.statement.id || data.statement.StatementId,
      StatementId: data.statement.id || data.statement.StatementId,
      period: {
        start: data.statement.period?.start ? new Date(data.statement.period.start) : null,
        end: data.statement.period?.end ? new Date(data.statement.period.end) : null
      },
      // Return null if the date string is missing or invalid
      createdAt: data.statement.createdAt ? new Date(data.statement.createdAt) : null,
      updatedAt: data.statement.updatedAt ? new Date(data.statement.updatedAt) : null,
      statementDate: data.statement.statementDate ? new Date(data.statement.statementDate) : null
    };
  } catch (error) {
    console.error('Error fetching statement:', error);
    throw error;
  }
};

// Upload statement
export const uploadStatement = async (
  file: File, 
  metadata: { supplier: string; periodStart: string; periodEnd: string },
  onProgress?: (progress: number) => void
): Promise<Statement> => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('supplier', metadata.supplier);
    formData.append('periodStart', metadata.periodStart);
    formData.append('periodEnd', metadata.periodEnd);

    const xhr = new XMLHttpRequest();
    const promise = new Promise<Statement>((resolve, reject) => {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', async () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          const response = JSON.parse(xhr.responseText);
          if (response.success && response.statement) {
            resolve({
              ...response.statement,
              id: response.statement.id || response.statement.StatementId,
              StatementId: response.statement.id || response.statement.StatementId,
              period: {
                start: response.statement.period?.start ? new Date(response.statement.period.start) : null,
                end: response.statement.period?.end ? new Date(response.statement.period.end) : null
              },
              createdAt: response.statement.createdAt ? new Date(response.statement.createdAt) : null,
              updatedAt: response.statement.updatedAt ? new Date(response.statement.updatedAt) : null
            });
          } else {
            reject(new Error(response.error || 'Upload failed'));
          }
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Network error during upload'));
      });
    });

    xhr.open('POST', '/api/statements/upload');
    xhr.send(formData);

    return promise;
  } catch (error) {
    console.error('Error uploading statement:', error);
    throw error;
  }
};

// Process statements from email
export const processStatementsFromEmail = async (): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch('/api/statements/process-email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to process statements from email');
    }

    const data = await response.json();
    return {
      success: data.success,
      message: data.message || 'Statements processed successfully'
    };
  } catch (error) {
    console.error('Error processing statements from email:', error);
    throw error;
  }
};
