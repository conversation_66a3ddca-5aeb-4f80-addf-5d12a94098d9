 import { withAuth, refreshAuthToken } from '@/lib/api';

/**
 * Get all orders with optional filtering
 * @param params Optional filter parameters
 * @returns Promise with the orders
 */
export async function getOrders(params?: {
  supplier?: string;
  startDate?: string;
  endDate?: string;
}) {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const queryParams = new URLSearchParams();
  
  if (params?.supplier) queryParams.append('supplier', params.supplier);
  if (params?.startDate) queryParams.append('startDate', params.startDate);
  if (params?.endDate) queryParams.append('endDate', params.endDate);
  
  const url = `/api/orders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
  
  const response = await fetch(url, {
    headers: withAuth({
      'Accept': 'application/json'
    })
  });
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to fetch orders');
  }
  
  const data = await response.json();
  // Check if data is an array or an object with a success property
  if (Array.isArray(data)) {
    return data;
  } else if (data && typeof data === 'object') {
    // If it's an object with a success property, it might be from the scrapeOrders endpoint
    if (data.success) {
      console.log('Fetching orders from:', `${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000'}/api/scrape/orders`);
      console.log('Orders fetch successful:', {
        count: data.ordersCount || 0,
        sampleDateTimes: data.sampleDateTimes || []
      });
    }
    return data.orders || [];
  }
  return [];
}

/**
 * Get a specific order by ID
 * @param id Order ID
 * @returns Promise with the order details
 */
export async function getOrderById(id: string) {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const response = await fetch(`/api/orders/${id}`, {
    headers: withAuth({
      'Accept': 'application/json'
    })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to fetch order');
  }
  
  const data = await response.json();
  return data.order || data;
}

/**
 * Update order status
 * @param id Order ID
 * @param status New status
 * @returns Promise with the update result
 */
export async function updateOrderStatus(
  id: string,
  status: 'pending' | 'processed' | 'completed'
) {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const response = await fetch(`/api/orders/${id}`, {
    method: 'PATCH',
    headers: withAuth({
      'Content-Type': 'application/json',
    }),
    body: JSON.stringify({ status }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to update order status');
  }
  
  const data = await response.json();
  return data.order || data;
}

/**
 * Force synchronization with Drug Comparison
 * @returns Promise with the sync result
 */
export async function syncOrders() {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const response = await fetch('/api/orders/sync', {
    headers: withAuth({
      'Accept': 'application/json'
    })
  });
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to sync orders');
  }
  
  const data = await response.json();
  return data;
  return response.json();
}