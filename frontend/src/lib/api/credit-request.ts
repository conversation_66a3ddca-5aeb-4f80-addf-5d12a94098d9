import { CreditRequest } from "@/types/credit-request";

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

export const creditRequestApi = {
  create: async (data: Omit<CreditRequest, "id" | "status" | "requestDate" | "emailHistory">): Promise<CreditRequest> => {
    try {
      const response = await fetch(`${API_URL}/api/credit-requests`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error('Failed to create credit request');
      }

      return response.json();
    } catch (error) {
      console.error('Error creating credit request:', error);
      throw error;
    }
  },

  getAll: async (): Promise<CreditRequest[]> => {
    try {
      const response = await fetch(`${API_URL}/api/credit-requests`);

      if (!response.ok) {
        throw new Error('Failed to fetch credit requests');
      }

      return response.json();
    } catch (error) {
      console.error('Error fetching credit requests:', error);
      throw error;
    }
  },

  updateStatus: async (id: string, status: CreditRequest["status"]): Promise<CreditRequest> => {
    try {
      const response = await fetch(`${API_URL}/api/credit-requests/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status })
      });

      if (!response.ok) {
        throw new Error('Failed to update credit request status');
      }

      return response.json();
    } catch (error) {
      console.error('Error updating credit request status:', error);
      throw error;
    }
  },

  sendEmail: async (id: string): Promise<void> => {
    try {
      const response = await fetch(`${API_URL}/api/credit-requests/${id}/email`, {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error('Failed to send credit request email');
      }
    } catch (error) {
      console.error('Error sending credit request email:', error);
      throw error;
    }
  },

  uploadImages: async (id: string, images: File[]): Promise<string[]> => {
    try {
      const formData = new FormData();
      images.forEach(image => {
        formData.append('images', image);
      });

      const response = await fetch(`${API_URL}/api/credit-requests/${id}/images`, {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Failed to upload images');
      }

      return response.json();
    } catch (error) {
      console.error('Error uploading images:', error);
      throw error;
    }
  }
};
