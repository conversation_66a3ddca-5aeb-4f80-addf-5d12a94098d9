import { ApiResponse } from '@/types/api';
import { Notification } from '@/components/notification-center';
import { Activity, ActivityType } from '@/components/activity-log';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

// Initialize auth token from cookie
const getStoredToken = () => {
  if (typeof document === 'undefined') return null; // Handle server-side rendering
  const cookies = document.cookie.split(';');
  const tokenCookie = cookies.find(cookie => cookie.trim().startsWith('auth-token='));
  return tokenCookie ? tokenCookie.split('=')[1].trim() : null;
};

let authToken: string | null = getStoredToken();

class ApiError extends Error {
  constructor(public statusCode: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function handleResponse<T>(response: Response): Promise<T> {
  const contentType = response.headers.get('content-type');
  
  if (!response.ok) {
    let errorMessage = 'An error occurred';
    if (contentType?.includes('application/json')) {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || errorData.error || errorMessage;
    }
    throw new ApiError(response.status, errorMessage);
  }

  if (!contentType?.includes('application/json')) {
    throw new Error('Invalid response format');
  }

  return response.json();
}

// Add auth token to all API requests
const withAuth = (headers: HeadersInit = {}) => {
  if (authToken) {
    return {
      ...headers,
      'Authorization': `Bearer ${authToken}`
    };
  }
  return headers;
};

export const notificationApi = {
  getNotifications: async (): Promise<Notification[]> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/notifications`, {
        headers: withAuth({
          'Accept': 'application/json'
        })
      });
      
      return handleResponse<Notification[]>(response);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error instanceof ApiError ? error : new Error('Failed to fetch notifications');
    }
  },

  markAsRead: async (id: string): Promise<ApiResponse<void>> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/notifications/${id}/read`, {
        method: 'POST',
        headers: withAuth({
          'Accept': 'application/json'
        })
      });
      
      return handleResponse<ApiResponse<void>>(response);
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error instanceof ApiError ? error : new Error('Failed to mark notification as read');
    }
  },

  markAllAsRead: async (): Promise<ApiResponse<void>> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/notifications/read-all`, {
        method: 'POST',
        headers: withAuth({
          'Accept': 'application/json'
        })
      });
      
      return handleResponse<ApiResponse<void>>(response);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error instanceof ApiError ? error : new Error('Failed to mark all notifications as read');
    }
  },

  dismissNotification: async (id: string): Promise<ApiResponse<void>> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/notifications/${id}`, {
        method: 'DELETE',
        headers: withAuth({
          'Accept': 'application/json'
        })
      });
      
      return handleResponse<ApiResponse<void>>(response);
    } catch (error) {
      console.error('Error dismissing notification:', error);
      throw error instanceof ApiError ? error : new Error('Failed to dismiss notification');
    }
  },

  updateNotificationSettings: async (settings: {
    email: boolean;
    browser: boolean;
    types: string[];
  }): Promise<ApiResponse<void>> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/notifications/settings`, {
        method: 'PUT',
        headers: withAuth({
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }),
        body: JSON.stringify(settings)
      });
      
      return handleResponse<ApiResponse<void>>(response);
    } catch (error) {
      console.error('Error updating notification settings:', error);
      throw error instanceof ApiError ? error : new Error('Failed to update notification settings');
    }
  },

  getActivityLog: async (
    filters?: {
      startDate?: Date;
      endDate?: Date;
      type?: ActivityType | string;
      userId?: string;
    }
  ): Promise<Activity[]> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      // Build query string from filters
      const queryParams = new URLSearchParams();
      if (filters?.startDate) {
        queryParams.append('startDate', filters.startDate.toISOString());
      }
      if (filters?.endDate) {
        queryParams.append('endDate', filters.endDate.toISOString());
      }
      if (filters?.type) {
        queryParams.append('type', filters.type);
      }
      if (filters?.userId) {
        queryParams.append('userId', filters.userId);
      }
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      
      const response = await fetch(`${API_URL}/api/activity-log${queryString}`, {
        headers: withAuth({
          'Accept': 'application/json'
        })
      });
      
      return handleResponse<Activity[]>(response);
    } catch (error) {
      console.error('Error fetching activity log:', error);
      throw error instanceof ApiError ? error : new Error('Failed to fetch activity log');
    }
  }
};