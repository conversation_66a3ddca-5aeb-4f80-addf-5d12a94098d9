import { ChecklistItem } from '@/types/checklist';
import { withAuth, refreshAuthToken } from '@/lib/api';

/**
 * Create new checklist items
 * @param items Array of checklist items to create
 * @returns Promise with the created items
 */
export async function createChecklistItems(items: Partial<ChecklistItem>[]) {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const response = await fetch('/api/checklist/items', {
    method: 'POST',
    headers: withAuth({
      'Content-Type': 'application/json',
    }),
    body: JSON.stringify({ items }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to create checklist items');
  }

  const data = await response.json();
  return data.items || [];
}

/**
 * Get checklist items with optional filtering
 * @param params Optional filter parameters
 * @returns Promise with the checklist items
 */
export async function getChecklistItems(params?: {
  orderId?: string;
  status?: string;
  supplier?: string;
  startDate?: string;
  endDate?: string;
}) {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const queryParams = new URLSearchParams();
  
  if (params?.orderId) queryParams.append('orderId', params.orderId);
  if (params?.status) queryParams.append('status', params.status);
  if (params?.supplier) queryParams.append('supplier', params.supplier);
  if (params?.startDate) queryParams.append('startDate', params.startDate);
  if (params?.endDate) queryParams.append('endDate', params.endDate);
  
  const url = `/api/checklist/items${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
  
  const response = await fetch(url, {
    headers: withAuth({
      'Accept': 'application/json'
    })
  });
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to fetch checklist items');
  }
  
  const data = await response.json();
  return data.items || [];
}

/**
 * Update a checklist item
 * @param id Item ID
 * @param updates Updates to apply
 * @returns Promise with the updated item
 */
export async function updateChecklistItem(
  id: string,
  updates: {
    status?: 'pending' | 'received' | 'incorrect' | 'missing';
    receivedQuantity?: number;
    notes?: string;
  }
) {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const response = await fetch(`/api/checklist/items/${id}`, {
    method: 'PATCH',
    headers: withAuth({
      'Content-Type': 'application/json',
    }),
    body: JSON.stringify(updates),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to update checklist item');
  }
  
  const data = await response.json();
  return data.item || null;
}

/**
 * Bulk update multiple checklist items
 * @param items Array of item IDs to update
 * @param updates Updates to apply to all items
 * @returns Promise with the result
 */
export async function bulkUpdateItems(
  items: string[],
  updates: {
    status?: 'pending' | 'received' | 'incorrect' | 'missing';
    receivedQuantity?: number | 'match';
    notes?: string;
  }
) {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const response = await fetch('/api/checklist/bulk', {
    method: 'POST',
    headers: withAuth({
      'Content-Type': 'application/json',
    }),
    body: JSON.stringify({ items, updates }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to bulk update checklist items');
  }
  
  const data = await response.json();
  return data.updatedItems || [];
}

/**
 * Get checklist summary statistics
 * @param params Optional filter parameters
 * @returns Promise with the summary statistics
 */
export async function getChecklistSummary(params?: {
  orderId?: string;
  supplier?: string;
  startDate?: string;
  endDate?: string;
}) {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const queryParams = new URLSearchParams();
  
  if (params?.orderId) queryParams.append('orderId', params.orderId);
  if (params?.supplier) queryParams.append('supplier', params.supplier);
  if (params?.startDate) queryParams.append('startDate', params.startDate);
  if (params?.endDate) queryParams.append('endDate', params.endDate);
  
  const url = `/api/checklist/summary${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
  
  const response = await fetch(url, {
    headers: withAuth({
      'Accept': 'application/json'
    })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to fetch checklist summary');
  }
  
  const data = await response.json();
  return data.summary || {};
}

/**
 * Generate checklist items from an order
 * @param orderId Order ID
 * @param useTemplate Whether to apply supplier-specific template
 * @returns Promise with the generated items
 */
export async function generateChecklistFromOrder(orderId: string, useTemplate: boolean = false) {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const response = await fetch('/api/checklist/generate', {
    method: 'POST',
    headers: withAuth({
      'Content-Type': 'application/json',
    }),
    body: JSON.stringify({ orderId, useTemplate }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to generate checklist from order');
  }
  
  const data = await response.json();
  return data.items || [];
}

/**
 * Batch generate checklist items from multiple orders
 * @param orderIds Array of order IDs
 * @param useTemplate Whether to apply supplier-specific templates
 * @returns Promise with the generated items
 */
export async function batchGenerateChecklistItems(orderIds: string[], useTemplate: boolean = false) {
  // Refresh auth token from cookie
  refreshAuthToken();
  
  const response = await fetch('/api/checklist/batch-generate', {
    method: 'POST',
    headers: withAuth({
      'Content-Type': 'application/json',
    }),
    body: JSON.stringify({ orderIds, useTemplate }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || 'Failed to batch generate checklist items');
  }
  
  const data = await response.json();
  return data.items || [];
}