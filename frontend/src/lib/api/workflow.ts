import { ApiResponse } from '@/types/api';
import { WorkflowStep } from '@/components/workflow-stepper';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

// Initialize auth token from cookie
const getStoredToken = () => {
  if (typeof document === 'undefined') return null; // Handle server-side rendering
  const cookies = document.cookie.split(';');
  const tokenCookie = cookies.find(cookie => cookie.trim().startsWith('auth-token='));
  return tokenCookie ? tokenCookie.split('=')[1].trim() : null;
};

let authToken: string | null = getStoredToken();

class ApiError extends Error {
  constructor(public statusCode: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function handleResponse<T>(response: Response): Promise<T> {
  const contentType = response.headers.get('content-type');
  
  if (!response.ok) {
    let errorMessage = 'An error occurred';
    if (contentType?.includes('application/json')) {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || errorData.error || errorMessage;
    }
    throw new ApiError(response.status, errorMessage);
  }

  if (!contentType?.includes('application/json')) {
    throw new Error('Invalid response format');
  }

  return response.json();
}

// Add auth token to all API requests
const withAuth = (headers: HeadersInit = {}) => {
  if (authToken) {
    return {
      ...headers,
      'Authorization': `Bearer ${authToken}`
    };
  }
  return headers;
};

export interface DashboardStats {
  orders: {
    total: number;
    pending: number;
    processed: number;
  };
  checklist: {
    total: number;
    pending: number;
    received: number;
    incorrect: number;
    missing: number;
  };
  invoices: {
    total: number;
    reconciled: number;
    unreconciled: number;
  };
  statements: {
    total: number;
    reconciled: number;
    unreconciled: number;
  };
  creditRequests: {
    total: number;
    pending: number;
    sent: number;
    approved: number;
    rejected: number;
  };
}

export const workflowApi = {
  getDashboardStats: async (): Promise<DashboardStats> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/dashboard`, {
        headers: withAuth({
          'Accept': 'application/json'
        })
      });
      
      return handleResponse<DashboardStats>(response);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error instanceof ApiError ? error : new Error('Failed to fetch dashboard stats');
    }
  },

  getWorkflowSteps: async (): Promise<WorkflowStep[]> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/workflow/steps`, {
        headers: withAuth({
          'Accept': 'application/json'
        })
      });
      
      return handleResponse<WorkflowStep[]>(response);
    } catch (error) {
      console.error('Error fetching workflow steps:', error);
      throw error instanceof ApiError ? error : new Error('Failed to fetch workflow steps');
    }
  },

  completeWorkflowStep: async (stepId: string): Promise<ApiResponse<void>> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/workflow/steps/${stepId}/complete`, {
        method: 'POST',
        headers: withAuth({
          'Accept': 'application/json'
        })
      });
      
      return handleResponse<ApiResponse<void>>(response);
    } catch (error) {
      console.error('Error completing workflow step:', error);
      throw error instanceof ApiError ? error : new Error('Failed to complete workflow step');
    }
  },

  skipWorkflowStep: async (stepId: string): Promise<ApiResponse<void>> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/workflow/steps/${stepId}/skip`, {
        method: 'POST',
        headers: withAuth({
          'Accept': 'application/json'
        })
      });
      
      return handleResponse<ApiResponse<void>>(response);
    } catch (error) {
      console.error('Error skipping workflow step:', error);
      throw error instanceof ApiError ? error : new Error('Failed to skip workflow step');
    }
  },

  generateChecklist: async (orderIds: string[]): Promise<ApiResponse<void>> => {
    try {
      if (!API_URL) throw new Error('API URL is not configured');
      
      const response = await fetch(`${API_URL}/api/checklist/generate`, {
        method: 'POST',
        headers: withAuth({
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }),
        body: JSON.stringify({ orderIds })
      });
      
      return handleResponse<ApiResponse<void>>(response);
    } catch (error) {
      console.error('Error generating checklist:', error);
      throw error instanceof ApiError ? error : new Error('Failed to generate checklist');
    }
  }
};