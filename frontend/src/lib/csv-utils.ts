export interface Order {
  supplier: string;
  dateTime: string;
  description: string;
  orderNo: string;
  pipCode: string;
  approvedQty: number;
  orderQty: number;
  price: number;
  subTotal: number;
  dtPrice: number;
}

export interface RawOrder {
  supplier: string;
  dateTime: string;
  description: string;
  orderNo: string;
  pipCode: string;
  approvedQty: string;
  orderQty: string;
  price: string;
  subTotal: string;
  dtPrice: string;
}

export function isOrderSuccessful(order: Order): boolean {
  return order.approvedQty > 0;
}

export function getSupplierLogoUrl(supplierName: string): string | null {
  if (!supplierName) return null;
  
  const lowerCaseSupplierName = supplierName.toLowerCase();
  const logoMappings: Record<string, string> = {
    "aah": "/aah.png",
    "alliance": "/alliance.png",
    "phoenix": "/phoenix.jpg",
    "bestway": "/bestway.jpeg",
    "bns": "/bns.jpeg",
    "cavendish": "/cavendish.jpg",
    "sigma": "/sigma.jpg",
    "otc": "/otc.jpg",
    "trident": "/trident.png",
    "wardle": "/wardles.jpeg", // Updated to match Donald Wardle & Son Limited
  };

  // Supplier name variations for matching
  const supplierVariations: Record<string, string[]> = {
    "bns": ["bns", "b&s"],
    "wardle": ["wardle", "donald wardle"]
  };

  // First try direct matching with variations
  for (const [key, variations] of Object.entries(supplierVariations)) {
    if (variations.some(v => lowerCaseSupplierName.includes(v))) {
      return logoMappings[key];
    }
  }

  // Then try standard partial matches for other suppliers
  const matchedSupplier = Object.keys(logoMappings).find(supplier =>
    !supplierVariations[supplier] && // Skip suppliers with variations as they're already checked
    lowerCaseSupplierName.includes(supplier)
  );
  
  return matchedSupplier ? logoMappings[matchedSupplier] : null;
}