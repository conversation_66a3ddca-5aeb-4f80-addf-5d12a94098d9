import { ComparisonResult } from "@/types/comparison";
import { Invoice } from "@/types/api";
import { Order } from "@/lib/csv-utils";

export function compareInvoiceWithOrder(
  invoice: Invoice,
  order: Order
): ComparisonResult {
  const discrepancies = [];

  // Compare quantities
  if (order.orderQty !== invoice.Items[0]?.Quantity) {
    discrepancies.push({
      field: "Quantity",
      orderValue: order.orderQty,
      invoiceValue: invoice.Items[0]?.Quantity || 0,
      difference: order.orderQty - (invoice.Items[0]?.Quantity || 0),
      explanation: `Order quantity (${order.orderQty}) does not match invoice quantity (${invoice.Items[0]?.Quantity || 0})`
    });
  }

  // Compare prices
  if (order.price !== invoice.Items[0]?.unitPrice) {
    discrepancies.push({
      field: "Price",
      orderValue: order.price,
      invoiceValue: invoice.Items[0]?.unitPrice || 0,
      difference: order.price - (invoice.Items[0]?.unitPrice || 0),
      explanation: `Order price (£${order.price}) does not match invoice price (£${invoice.Items[0]?.unitPrice || 0})`
    });
  }

  // Compare total amounts
  const orderTotal = order.orderQty * order.price;
  const invoiceTotal = invoice.InvoiceTotal;
  if (orderTotal !== invoiceTotal) {
    discrepancies.push({
      field: "Total",
      orderValue: orderTotal,
      invoiceValue: invoiceTotal,
      difference: orderTotal - invoiceTotal,
      explanation: `Order total (£${orderTotal}) does not match invoice total (£${invoiceTotal})`
    });
  }

  // Determine match status
  let matchStatus: ComparisonResult["matchStatus"];
  if (discrepancies.length === 0) {
    matchStatus = "exact";
  } else if (discrepancies.length < 3) {
    matchStatus = "partial";
  } else {
    matchStatus = "mismatch";
  }

  return {
    orderId: order.orderNo,
    invoiceId: invoice.InvoiceId,
    matchStatus,
    discrepancies
  };
}
