export interface DiscrepancyFilters {
  field?: string;           // Filter by field (e.g., "Quantity", "Price", "Total")
  matchStatus?: 'exact' | 'partial' | 'mismatch';
  startDate?: Date;
  endDate?: Date;
  supplier?: string;
  minDifference?: number;
  maxDifference?: number;
}

export interface DiscrepancyStats {
  total: number;
  byStatus: {
    exact: number;
    partial: number;
    mismatch: number;
  };
  byField: {
    [key: string]: number;
  };
  totalDifference: number;
}
