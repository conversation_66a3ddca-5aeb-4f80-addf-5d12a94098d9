import { QuerySnapshot, DocumentData, DocumentSnapshot } from 'firebase/firestore';

// Add any missing Firebase type declarations here
declare module 'firebase/app' {
  export interface FirebaseApp {
    name: string;
    options: object;
  }
}

// Add any custom type augmentations for your Firebase documents
declare module 'firebase/firestore' {
  interface InvoiceDocument extends DocumentData {
    VendorName: string;
    CustomerName: string;
    InvoiceDate: string;
    DueDate: string;
    InvoiceId: string;
    CustomerAddress: string;
    CustomerAddressRecipient: string;
    CustomerId: string;
    ShippingAddress: string;
    ShippingAddressRecipient: string;
    VendorAddress: string;
    VendorAddressRecipient: string;
    VendorTaxId: string;
    InvoiceTotal: number;
    Items: Array<{
      Amount: number;
      unitPrice: number;
      Description: string;
      Quantity: number;
      itemId: number;
      pipCode: string;
      packSize: string;
    }>;
    orderReconciled?: boolean;
    statementReconciled?: boolean;
    processedAt: Date;
    filePath: string;
  }
}