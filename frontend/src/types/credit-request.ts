export interface CreditRequest {
  id: string;
  orderId: string;
  invoiceId: string;
  items: CreditRequestItem[];
  supplier: string;
  requestDate: Date;
  status: 'pending' | 'sent' | 'approved' | 'rejected';
  responseDate?: Date;
  creditNoteNumber?: string;
  emailHistory: EmailRecord[];
  notes: string;
}

export interface CreditRequestItem {
  productName: string;
  pipCode: string;
  quantity: number;
  reason: 'missing' | 'damaged' | 'incorrect' | 'other';
  details: string;
  originalUnitPrice: number;
  originalTotal: number;
  images?: string[]; // URLs to any photos of damaged items
}

export interface EmailRecord {
  id: string;
  sentDate: Date;
  to: string[];
  subject: string;
  content: string;
  attachments?: string[];
  status: 'sent' | 'failed';
  response?: {
    date: Date;
    content: string;
  };
}
