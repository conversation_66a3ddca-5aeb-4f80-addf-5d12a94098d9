export interface Report {
  id: string;
  type: 'discrepancy' | 'reconciliation' | 'monthly';
  dateRange: {
    start: Date;
    end: Date;
  };
  data: {
    [key: string]: any;
    summary?: {
      totalItems: number;
      totalDiscrepancies: number;
      totalValue: number;
    };
    details?: any[];
  };
  status: 'generated' | 'failed';
  generatedAt: Date;
  generatedBy: string;
}

export interface ReportFilter {
  type?: Report['type'];
  startDate?: Date;
  endDate?: Date;
  status?: Report['status'];
}

export interface ReportTemplate {
  id: string;
  name: string;
  type: Report['type'];
  description: string;
  fields: {
    name: string;
    type: 'string' | 'number' | 'date' | 'boolean';
    required: boolean;
  }[];
  customization: {
    includeCharts: boolean;
    includeSummary: boolean;
    detailLevel: 'basic' | 'detailed' | 'comprehensive';
  };
}
