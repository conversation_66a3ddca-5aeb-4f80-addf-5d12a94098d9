declare module 'firebase/app' {
  import { FirebaseOptions } from '@firebase/app-types';
  export function initializeApp(options: FirebaseOptions, name?: string): any;
  export function getApps(): any[];
}

declare module 'firebase/firestore' {
  export interface DocumentData {
    [field: string]: any;
  }

  export interface QueryDocumentSnapshot<T = DocumentData> {
    data(): T;
    id: string;
  }

  export interface QuerySnapshot<T = DocumentData> {
    docs: Array<QueryDocumentSnapshot<T>>;
    forEach(callback: (result: QueryDocumentSnapshot<T>) => void): void;
  }

  export function getFirestore(app?: any): any;
  export function collection(firestore: any, path: string): any;
  export function query(query: any, ...queryConstraints: any[]): any;
  export function orderBy(field: string, direction?: 'asc' | 'desc'): any;
  export function onSnapshot(
    query: any,
    observer: {
      next?: (snapshot: QuerySnapshot) => void;
      error?: (error: Error) => void;
      complete?: () => void;
    }
  ): () => void;
  export function onSnapshot(
    query: any,
    onNext: (snapshot: QuerySnapshot) => void,
    onError?: (error: Error) => void
  ): () => void;
}

// Extend Window interface to include Firebase config
declare global {
  interface Window {
    firebaseConfig?: {
      apiKey: string;
      authDomain: string;
      projectId: string;
      storageBucket: string;
      messagingSenderId: string;
      appId: string;
    };
  }
}