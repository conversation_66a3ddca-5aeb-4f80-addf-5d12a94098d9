export interface Resolution {
  id: string;
  discrepancyId: string;  // Reference to the ComparisonResult
  status: 'pending' | 'in_progress' | 'resolved' | 'escalated';
  comments: ResolutionComment[];
  assignedTo: string;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  resolvedBy?: string;
  resolutionType?: 'credit_note' | 'reorder' | 'write_off' | 'other';
  resolutionDetails?: string;
}

export interface ResolutionComment {
  id: string;
  content: string;
  createdBy: string;
  createdAt: Date;
  type: 'note' | 'status_change' | 'system';
  metadata?: {
    oldStatus?: string;
    newStatus?: string;
    [key: string]: any;
  };
}
