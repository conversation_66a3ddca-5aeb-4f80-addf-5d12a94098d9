export interface StatementItem {
  invoiceId: string; // Or item reference
  date: Date | null; // Date can be null
  amount: number | null; // Amount can be null
  status?: 'matched' | 'unmatched' | 'partial' | string; // Keep status for reconciliation, make optional
  type?: 'invoice' | 'credit' | string; // Add type for 'invoice' or 'credit'
  discrepancies?: Array<{
    field: string;
    statementValue: any;
    invoiceValue: any;
    difference: number | string;
  }>;
}

// StatementCreditItem is no longer needed as credits are part of StatementItem with a 'type'
// export interface StatementCreditItem { 
//   creditId: string | null; 
//   date: Date | null;
//   amount: number | null;
// }

export interface Statement {
  // Original properties from the type definition
  id: string;
  supplier: string;
  period: {
    start: Date;
    end: Date;
  };
  invoices: StatementItem[];
  // credits?: StatementCreditItem[]; // Remove separate credits array
  totalAmount: number | null; // Allow totalAmount to be null if not parsed
  reconciled: boolean;
  createdAt: Date | null; // Allow dates to be null
  updatedAt: Date | null; // Allow dates to be null
  statementDate?: Date | null; // Explicit field for statement issue date, ensure it's here
  
  // Legacy properties used in components / or fields from AI parsing
  StatementId?: string | null; // Keep if used by older data or specific components
  CustomerName?: string | null;
  CustomerAddress?: string | null;
  // InvoiceDate?: string | Date | null; // This is now statementDate
  DueDate?: string | Date | null;
  // TotalAmount is already defined above
  // Items?: StatementItem[]; // 'invoices' array now holds all items
  TotalTax?: number | null;
  supplierName?: string | null; // If AI uses SupplierName for the issuer
  // statementDate is already defined above
  periodStart?: Date | null; // Keep for potential future use or filtering
  periodEnd?: Date | null;   // Keep for potential future use or filtering
}

// Remove StatementCreditItem as it's merged into StatementItem with a Type
// export interface StatementCreditItem { 
//   creditId: string | null; 
//   date: Date | null;
//   amount: number | null;
// }

export interface StatementFilter {
  supplier?: string;
  startDate?: Date;
  endDate?: Date;
  reconciled?: boolean;
}
