"use client";

import { useRouter } from "next/navigation";
import { useState, useMemo, useEffect } from 'react';
import { ArrowUpDown, Calendar as CalendarIcon } from 'lucide-react';
import { Statement } from "@/types/statement";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";

interface StatementsTableProps {
  statements: Statement[];
}

type SortField = 'statementDate' | 'dueDate' | 'total' | 'invoiceCount';
type SortOrder = 'asc' | 'desc';

const getSortIcon = (currentSortField: SortField, field: SortField, currentSortOrder: SortOrder) => {
  if (currentSortField !== field) return null;
  return (
    <ArrowUpDown
      className="ml-1 h-4 w-4 text-gray-900"
      style={{
        transform: currentSortOrder === 'asc' ? 'rotate(180deg)' : 'none'
      }}
    />
  );
};

const formatNumber = (num: number): string => {
  return num.toLocaleString('en-GB', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

export function StatementsTable({ statements }: StatementsTableProps) {
  const router = useRouter();
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [sortField, setSortField] = useState<SortField>('statementDate');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [startDate, endDate]);

  const parseDate = (dateInput: string | Date | undefined | null): Date => {
    // 1. Handle Date instances directly
    if (dateInput instanceof Date) {
      return !isNaN(dateInput.getTime()) ? dateInput : new Date(0); // Return epoch for invalid Date objects
    }

    // 2. Handle falsy values (null, undefined, empty string)
    if (!dateInput) {
      return new Date(0);
    }

    // 3. Ensure it's a string before attempting to split
    if (typeof dateInput === 'string') {
      const parts = dateInput.trim().split('/'); // Trim whitespace
      if (parts.length === 3) {
        const dayStr = parts[0];
        const monthStr = parts[1];
        const yearStr = parts[2];

        const numDay = parseInt(dayStr, 10);
        const numMonth = parseInt(monthStr, 10); // Input month is 1-indexed
        const numYear = parseInt(yearStr, 10);

        // Basic validation of parsed numbers
        if (
          !isNaN(numDay) && !isNaN(numMonth) && !isNaN(numYear) &&
          numYear > 1000 && numYear < 3000 && // Reasonable year range
          numMonth >= 1 && numMonth <= 12 &&
          numDay >= 1 && numDay <= 31 // Basic day check
        ) {
          // JavaScript Date month is 0-indexed
          const parsed = new Date(numYear, numMonth - 1, numDay);
          
          // Final check: ensure the Date object reflects the input,
          // preventing issues like Date constructor rolling over invalid day/month.
          if (
            !isNaN(parsed.getTime()) &&
            parsed.getFullYear() === numYear &&
            parsed.getMonth() === numMonth - 1 &&
            parsed.getDate() === numDay
          ) {
            return parsed;
          }
        }
      }
    }
    
    // 4. Fallback for any other type or format that failed parsing
    return new Date(0); 
  };

  const isDateInRange = useMemo(() => {
    return (dateInput: string | Date | undefined | null) => {
      if (!startDate && !endDate) return true;
      if (!dateInput) return false; // Don't include if date is not available
      
      const statementDateValue = parseDate(dateInput);
      // If parseDate returned epoch (new Date(0)), it means the date was invalid or couldn't be parsed.
      // We should not include these in date range filtering unless specifically intended.
      if (statementDateValue.getTime() === new Date(0).getTime() && dateInput !== null && dateInput !== undefined && String(dateInput).trim() !== "") { // Check if it was a non-empty invalid date
          return false; 
      }
      if (statementDateValue.getTime() === new Date(0).getTime()) { // For truly empty/null dates that parse to epoch
          if(startDate || endDate) return false; // Don't match if filters are set
          // If no filters, an epoch date (from null/undefined) could be considered "in range" if that's desired.
          // For now, let's assume they are out of range if any filter is active.
      }


      const start = startDate;
      const end = endDate;
      
      if (start) {
        const startOfDay = new Date(start);
        startOfDay.setHours(0, 0, 0, 0);
        if (statementDateValue < startOfDay) return false;
      }
      
      if (end) {
        const endOfDay = new Date(end);
        endOfDay.setHours(23, 59, 59, 999);
        if (statementDateValue > endOfDay) return false;
      }
      
      return true;
    };
  }, [startDate, endDate]);

  const filteredStatements = useMemo(() => {
    return statements.filter(statement => {
      const matchesDate = isDateInRange(statement.statementDate); // Use statementDate for filtering
      return matchesDate;
    });
  }, [statements, isDateInRange]);

  const sortedStatements = useMemo(() => {
    return [...filteredStatements].sort((a, b) => {
      let comparison = 0;
      switch (sortField) {
        case 'statementDate':
          comparison = parseDate(a.statementDate).getTime() - parseDate(b.statementDate).getTime();
          break;
        case 'dueDate':
          comparison = parseDate(a.DueDate).getTime() - parseDate(b.DueDate).getTime();
          break;
        case 'invoiceCount':
          comparison = (a.invoices?.length || 0) - (b.invoices?.length || 0);
          break;
        case 'total':
          comparison = (a.totalAmount || 0) - (b.totalAmount || 0);
          break;
      }
      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }, [filteredStatements, sortField, sortOrder]);

  // Calculate pagination
  const paginatedStatements = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedStatements.slice(startIndex, endIndex);
  }, [sortedStatements, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(sortedStatements.length / itemsPerPage);

  const totalAmount = useMemo(() => {
    return filteredStatements.reduce((sum, statement) => {
      return sum + (statement.totalAmount || 0);
    }, 0);
  }, [filteredStatements]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('desc');
    }
  };

  return (
    <div className="w-full max-w-[1400px] mx-auto px-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">
          Statements
          <span className="ml-4 text-lg font-normal text-gray-600">(Total: £{formatNumber(totalAmount)})</span>
        </h2>
      </div>

      <div className="flex gap-4 mb-6 items-center">
        <div className="flex gap-2 items-center">
          <span className="text-sm text-gray-600">From:</span>
          <Popover>
            <PopoverTrigger asChild className="border border-gray-200">
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50" align="start">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={setStartDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <span className="text-sm text-gray-600">To:</span>
          <Popover>
            <PopoverTrigger asChild className="border border-gray-200">
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50" align="start">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={setEndDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100">
        <table className="w-full text-sm text-left">
          <thead className="bg-gray-50 border-b border-gray-100">
            <tr>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Customer</th>
              <th
                scope="col"
                className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600"
                onClick={() => handleSort('statementDate')}
              >
                <div className="flex items-center">
                  <span>Statement Date</span>
                  {getSortIcon(sortField, 'statementDate', sortOrder)}
                </div>
              </th>
              <th
                scope="col"
                className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600"
                onClick={() => handleSort('dueDate')}
              >
                <div className="flex items-center">
                  <span>Due Date</span>
                  {getSortIcon(sortField, 'dueDate', sortOrder)}
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Statement ID</th>
              <th
                scope="col"
                className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600 text-right"
                onClick={() => handleSort('invoiceCount')}
              >
                <div className="flex items-center justify-end">
                  <span>Invoices</span>
                  {getSortIcon(sortField, 'invoiceCount', sortOrder)}
                </div>
              </th>
              <th
                scope="col"
                className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600 text-right"
                onClick={() => handleSort('total')}
              >
                <div className="flex items-center justify-end">
                  <span>Total</span>
                  {getSortIcon(sortField, 'total', sortOrder)}
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            {sortedStatements.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                  No statements found matching your filters
                </td>
              </tr>
            ) : (
              paginatedStatements.map((statement) => (
                <tr
                  key={statement.id || statement.StatementId || `statement-${Math.random()}`}
                  onClick={() => router.push(`/statements/${statement.id || statement.StatementId}`)}
                  className="bg-white border-b hover:bg-gray-50 transition-colors cursor-pointer"
                >
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {statement.CustomerName}
                    </div>
                    <div className="text-sm text-gray-500">
                      {statement.CustomerAddress}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-600">
                    {statement.statementDate ? format(parseDate(statement.statementDate), "dd/MM/yyyy") : 'N/A'}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-600">
                    {statement.DueDate ? format(parseDate(statement.DueDate), "dd/MM/yyyy") : 'N/A'}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-600">{statement.StatementId}</td>
                  <td className="px-6 py-4 text-sm text-gray-900 text-right">
                    {statement.invoices?.length || 0}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 text-right font-medium">
                    £{formatNumber(statement.totalAmount || 0)}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {sortedStatements.length > 0 && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-4 rounded-lg">
          <div className="flex flex-1 items-center justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * itemsPerPage, sortedStatements.length)}
                </span> of{' '}
                <span className="font-medium">{sortedStatements.length}</span> results
              </p>
            </div>
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
                className="min-w-[70px] text-center"
              >
                First
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                disabled={currentPage === 1}
                className="min-w-[70px] text-center"
              >
                Previous
              </Button>
              <div className="flex items-center gap-1 px-2">
                <span className="text-sm text-gray-700 text-center min-w-[100px]">Page {currentPage} of {totalPages}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                disabled={currentPage === totalPages}
                className="min-w-[70px] text-center"
              >
                Next
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
                className="min-w-[70px] text-center"
              >
                Last
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
