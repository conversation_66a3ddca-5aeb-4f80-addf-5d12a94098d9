"use client";

import { useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from "date-fns";
import { CalendarIcon, CheckCircle, XCircle, AlertTriangle } from "lucide-react";
import { Statement, StatementItem } from "@/types/statement";
import { Invoice } from "@/types/api";
import { useToast } from "@/components/ui/toast";

interface StatementReconciliationProps {
  statement: Statement;
  invoices: Invoice[];
  onResolveDiscrepancy: (statementItemId: string, resolution: string) => Promise<void>;
}

export function StatementReconciliation({ statement, invoices, onResolveDiscrepancy }: StatementReconciliationProps) {
  const { showToast, ToastComponent } = useToast();
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [resolutionNote, setResolutionNote] = useState<string>("");
  const [isResolving, setIsResolving] = useState(false);

  const invoiceMap = useMemo(() => {
    const map = new Map<string, Invoice>();
    invoices.forEach(invoice => {
      map.set(invoice.InvoiceId, invoice);
    });
    return map;
  }, [invoices]);

  const handleResolveDiscrepancy = async (statementItem: StatementItem) => {
    if (!resolutionNote) {
      showToast("Please enter a resolution note", "error");
      return;
    }

    setIsResolving(true);
    try {
      await onResolveDiscrepancy(statementItem.invoiceId, resolutionNote);
      showToast("Discrepancy resolved successfully", "success");
      setResolutionNote("");
      setSelectedInvoiceId(null);
    } catch (error) {
      console.error("Failed to resolve discrepancy:", error);
      showToast("Failed to resolve discrepancy", "error");
    } finally {
      setIsResolving(false);
    }
  };

  const getStatusIcon = (status: StatementItem['status']) => {
    switch (status) {
      case 'matched':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'unmatched':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'partial':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="w-full max-w-[1400px] mx-auto px-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Statement Reconciliation</h1>
          <div className="text-sm text-gray-600 mt-1">
            <span>Supplier: {statement.supplier}</span>
            <span className="mx-2">|</span>
            <span>Period: {format(statement.period.start, "PPP")} - {format(statement.period.end, "PPP")}</span>
          </div>
          <p className="text-gray-600">Review and resolve discrepancies between statements and invoices</p>
        </div>
        <div className="text-right">
          <div className="text-lg font-semibold">Total: £{statement.totalAmount.toFixed(2)}</div>
          <div className="text-sm text-gray-600">
            {statement.invoices.length} invoices, {statement.invoices.filter(i => i.status !== 'matched').length} with discrepancies
          </div>
        </div>
      </div>

      <div className="relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100 mb-6">
        <table className="w-full text-sm text-left">
          <thead className="bg-gray-50 border-b border-gray-100">
            <tr>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Status</th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Invoice ID</th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Date</th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600 text-right">Amount</th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Actions</th>
            </tr>
          </thead>
          <tbody>
            {statement.invoices.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                  No invoices found in this statement
                </td>
              </tr>
            ) : (
              statement.invoices.map((item) => (
                <tr key={item.invoiceId} className="bg-white border-b hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4">
                    {getStatusIcon(item.status)}
                  </td>
                  <td className="px-6 py-4 font-medium text-gray-900">{item.invoiceId}</td>
                  <td className="px-6 py-4 text-gray-600">{format(item.date, "PPP")}</td>
                  <td className="px-6 py-4 text-right text-gray-900">£{item.amount.toFixed(2)}</td>
                  <td className="px-6 py-4">
                    {item.status !== 'matched' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedInvoiceId(item.invoiceId)}
                      >
                        View Discrepancies
                      </Button>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {selectedInvoiceId && (
        <div className="bg-white shadow-sm rounded-xl border border-gray-100 p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Discrepancies for Invoice {selectedInvoiceId}</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedInvoiceId(null)}
            >
              Close
            </Button>
          </div>

          {statement.invoices.find(i => i.invoiceId === selectedInvoiceId)?.discrepancies?.length ? (
            <>
              <div className="relative overflow-x-auto mb-4">
                <table className="w-full text-sm text-left">
                  <thead className="bg-gray-50 border-b border-gray-100">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-xs font-semibold text-gray-600">Field</th>
                      <th scope="col" className="px-4 py-3 text-xs font-semibold text-gray-600">Statement Value</th>
                      <th scope="col" className="px-4 py-3 text-xs font-semibold text-gray-600">Invoice Value</th>
                      <th scope="col" className="px-4 py-3 text-xs font-semibold text-gray-600">Difference</th>
                    </tr>
                  </thead>
                  <tbody>
                    {statement.invoices.find(i => i.invoiceId === selectedInvoiceId)?.discrepancies?.map((discrepancy, index) => (
                      <tr key={index} className="bg-white border-b">
                        <td className="px-4 py-3 font-medium text-gray-900">{discrepancy.field}</td>
                        <td className="px-4 py-3 text-gray-600">{discrepancy.statementValue}</td>
                        <td className="px-4 py-3 text-gray-600">{discrepancy.invoiceValue}</td>
                        <td className="px-4 py-3 text-red-600">{discrepancy.difference}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Resolution Note
                  </label>
                  <textarea
                    value={resolutionNote}
                    onChange={(e) => setResolutionNote(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md"
                    rows={3}
                    placeholder="Enter details about how this discrepancy was resolved..."
                  />
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={() => handleResolveDiscrepancy(statement.invoices.find(i => i.invoiceId === selectedInvoiceId)!)}
                    disabled={isResolving || !resolutionNote}
                  >
                    {isResolving ? "Resolving..." : "Mark as Resolved"}
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No discrepancies found for this invoice
            </div>
          )}
        </div>
      )}

      {ToastComponent}
    </div>
  );
}