"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { ComparisonResult } from "@/types/comparison";
import { DiscrepancyFilters, DiscrepancyStats } from "@/types/discrepancy";

interface DiscrepancyDashboardProps {
  discrepancies: ComparisonResult[];
}

export function DiscrepancyDashboard({ discrepancies }: DiscrepancyDashboardProps) {
  const [filters, setFilters] = useState<DiscrepancyFilters>({});
  
  const stats: DiscrepancyStats = {
    total: discrepancies.length,
    byStatus: {
      exact: discrepancies.filter(d => d.matchStatus === 'exact').length,
      partial: discrepancies.filter(d => d.matchStatus === 'partial').length,
      mismatch: discrepancies.filter(d => d.matchStatus === 'mismatch').length
    },
    byField: discrepancies.reduce((acc, d) => {
      d.discrepancies.forEach(disc => {
        acc[disc.field] = (acc[disc.field] || 0) + 1;
      });
      return acc;
    }, {} as { [key: string]: number }),
    totalDifference: discrepancies.reduce((sum, d) => 
      sum + d.discrepancies.reduce((discSum, disc) => 
        discSum + (typeof disc.difference === 'number' ? Math.abs(disc.difference) : 0), 0), 0)
  };

  const filteredDiscrepancies = discrepancies.filter(d => {
    if (filters.matchStatus && d.matchStatus !== filters.matchStatus) return false;
    if (filters.field && !d.discrepancies.some(disc => disc.field === filters.field)) return false;
    // Add more filter conditions here
    return true;
  });

  return (
    <div className="w-full max-w-[1400px] mx-auto px-8 py-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">Discrepancy Dashboard</h2>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-xl p-6 transition-transform hover:scale-105">
          <div className="text-sm text-gray-500">Total Discrepancies</div>
          <div className="text-2xl font-semibold">{stats.total}</div>
        </div>
        <div className="bg-white rounded-xl shadow-xl p-6 transition-transform hover:scale-105">
          <div className="text-sm text-gray-500">Exact Matches</div>
          <div className="text-2xl font-semibold text-green-600">{stats.byStatus.exact}</div>
        </div>
        <div className="bg-white rounded-xl shadow-xl p-6 transition-transform hover:scale-105">
          <div className="text-sm text-gray-500">Partial Matches</div>
          <div className="text-2xl font-semibold text-yellow-600">{stats.byStatus.partial}</div>
        </div>
        <div className="bg-white rounded-xl shadow-xl p-6 transition-transform hover:scale-105">
          <div className="text-sm text-gray-500">Mismatches</div>
          <div className="text-2xl font-semibold text-red-600">{stats.byStatus.mismatch}</div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6 items-center bg-white rounded-xl shadow-xl p-6">
        <Select
          value={filters.matchStatus}
          onValueChange={(value: ComparisonResult['matchStatus']) => 
            setFilters(f => ({ ...f, matchStatus: value }))
          }
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="exact">Exact Match</SelectItem>
            <SelectItem value="partial">Partial Match</SelectItem>
            <SelectItem value="mismatch">Mismatch</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={filters.field}
          onValueChange={(value: string) => 
            setFilters(f => ({ ...f, field: value }))
          }
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="All Fields" />
          </SelectTrigger>
          <SelectContent>
            {Object.keys(stats.byField).map(field => (
              <SelectItem key={field} value={field}>
                {field} ({stats.byField[field]})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <div className="flex gap-2 items-center">
          <span className="text-sm text-gray-600">Date Range:</span>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {filters.startDate ? format(filters.startDate, "PPP") : "Start date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={filters.startDate}
                onSelect={(date) => setFilters(f => ({ ...f, startDate: date }))}
              />
            </PopoverContent>
          </Popover>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {filters.endDate ? format(filters.endDate, "PPP") : "End date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={filters.endDate}
                onSelect={(date) => setFilters(f => ({ ...f, endDate: date }))}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Discrepancies Table */}
      <div className="bg-white rounded-xl shadow-xl p-6 overflow-hidden">
        <table className="w-full">
          <thead className="bg-muted">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Order ID
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Invoice ID
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Discrepancies
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredDiscrepancies.map((item) => (
              <tr key={`${item.orderId}-${item.invoiceId}`}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {item.orderId}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {item.invoiceId}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full
                    ${item.matchStatus === 'exact' ? 'bg-green-100 text-green-800' :
                      item.matchStatus === 'partial' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'}`}>
                    {item.matchStatus}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {item.discrepancies.length} issues found
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
