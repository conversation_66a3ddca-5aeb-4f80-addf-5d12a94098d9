import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Upload, AlertCircle, CheckCircle } from 'lucide-react';
import { uploadStatement } from '@/lib/api/statement';

interface StatementUploadProps {
  onUploadComplete: () => void;
}

export function StatementUpload({ onUploadComplete }: StatementUploadProps) {
  const [isUploading, setIsUploading] = useState(false); // Tracks file transfer
  const [isProcessing, setIsProcessing] = useState(false); // Tracks backend AI processing
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setIsProcessing(false); // Reset processing state
    setUploadProgress(0);
    setError(null);
    setSuccess(null);

    try {
      // For simplicity, we're using the current date for the period
      const today = new Date();
      const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      // Extract supplier from filename (simplified approach)
      const filenameWithoutExt = file.name.split('.')[0];
      const supplier = filenameWithoutExt.split('_')[0] || 'Unknown';

      await uploadStatement(
        file,
        {
          supplier,
          periodStart: firstDayOfMonth.toISOString(),
          periodEnd: lastDayOfMonth.toISOString()
        },
        (progress) => {
          setUploadProgress(progress);
          if (progress === 100) {
            setIsUploading(false); // File transfer done
            setIsProcessing(true); // Now backend is processing
          }
        }
      );
      // uploadStatement promise resolves AFTER backend processing (including AI) is done
      setIsProcessing(false); // Backend processing finished
      setSuccess(`Statement "${file.name}" processed successfully`);
      onUploadComplete();
    } catch (error: any) {
      console.error('Error uploading statement:', error);
      setError(error.message || 'Failed to upload statement');
    } finally {
      setIsUploading(false);
      setIsProcessing(false);
      // Reset the file input
      e.target.value = '';
    }
  };

  return (
    <div className="relative space-y-2">
      <input
        type="file"
        id="statement-upload"
        accept=".pdf,.csv,.xlsx"
        className="hidden"
        onChange={handleFileChange}
        disabled={isUploading || isProcessing}
      />
      <label htmlFor="statement-upload">
        <Button
          variant="secondary"
          className={`cursor-pointer ${(isUploading || isProcessing) ? 'opacity-50' : ''}`}
          disabled={isUploading || isProcessing}
          onClick={() => document.getElementById('statement-upload')?.click()}
          type="button"
        >
          <Upload className="w-4 h-4 mr-2" />
          {isUploading ? `Uploading... ${uploadProgress}%` : isProcessing ? 'Processing...' : 'Upload Statement'}
        </Button>
      </label>
      
      {error && (
        <div className="flex items-center text-sm text-red-500 mt-2">
          <AlertCircle className="w-4 h-4 mr-1" />
          <span>{error}</span>
        </div>
      )}
      
      {success && (
        <div className="flex items-center text-sm text-green-500 mt-2">
          <CheckCircle className="w-4 h-4 mr-1" />
          <span>{success}</span>
        </div>
      )}
    </div>
  );
}