'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { authApi } from '@/lib/api';
import { usePathname } from 'next/navigation';
import { LogOut, Bell } from 'lucide-react';

export function Navbar() {
  const pathname = usePathname();
  
  const handleLogout = () => {
    authApi.logout();
    // Use direct window location change instead of router
    window.location.href = '/login';
  };

  return (
    <nav className="bg-white border-b border-border/40 sticky top-0 z-50">
      <div className="max-w-[1400px] mx-auto px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/" className="text-lg font-semibold text-primary">
                Admin Portal
              </Link>
            </div>
            <div className="hidden sm:ml-10 sm:flex sm:space-x-8">
              {[
                { href: '/overview', label: 'Overview' },
                { href: '/invoices', label: 'Invoices' },
                { href: '/statements', label: 'Statements' },
                { href: '/orders', label: 'Orders' },
                { href: '/checklist', label: 'Checklist' },
                { href: '/reconciliation', label: 'Reconciliation' },
                { href: '/cashflow', label: 'Cash Flow' },
                { href: '/analytics', label: 'Analytics' },
                { href: '/settings', label: 'Settings' },
              ].map(({ href, label }) => (
                <Link
                  key={href}
                  href={href}
                  className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors ${
                    pathname === href
                      ? 'border-primary text-primary'
                      : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                  }`}
                >
                  {label}
                </Link>
              ))}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              className="text-muted-foreground hover:text-foreground"
            >
              <Bell className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-sm font-medium text-muted-foreground hover:text-foreground"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign out
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
}