"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu, Package, LineChart, FileText, BarChart3, Receipt, DollarSign, Settings, CheckCircle, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

const navItems: NavItem[] = [
  {
    title: "Overview",
    href: "/overview",
    icon: LineChart,
  },
  {
    title: "Orders",
    href: "/orders",
    icon: Package,
  },
  {
    title: "Checklist",
    href: "/checklist",
    icon: CheckCircle,
  },
  {
    title: "Reconciliation",
    href: "/reconciliation",
    icon: RefreshCw,
  },
  {
    title: "Invoices",
    href: "/invoices",
    icon: Receipt,
  },
  {
    title: "Statements",
    href: "/statements",
    icon: FileText,
  },
  {
    title: "Analytics",
    href: "/analytics",
    icon: BarChart3,
  },
  {
    title: "Cashflow",
    href: "/cashflow",
    icon: DollarSign,
  },
];

function NavContent() {
  const pathname = usePathname();
  
  return (
    <div className="flex h-full flex-col">
      <div className="flex-1">
        <div className="px-3 py-4">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            Admin Dashboard
          </h2>
          <nav className="space-y-1">
            {navItems.map((item) => (
              <Button
                key={item.href}
                asChild
                variant="ghost"
                className={cn(
                  "w-full justify-start",
                  pathname === item.href
                    ? "bg-accent text-accent-foreground"
                    : "hover:bg-accent hover:text-accent-foreground"
                )}
              >
                <Link href={item.href}>
                  <item.icon className="mr-2 h-4 w-4" />
                  {item.title}
                </Link>
              </Button>
            ))}
          </nav>
        </div>
      </div>

      <div className="mt-auto px-3 py-4 border-t border-[#bff2d6]/30">
        <Button
          asChild
          variant="ghost"
          className={cn(
            "w-full justify-start",
            pathname === "/settings"
              ? "bg-[#bff2d6] text-[#087f5b]"
              : "hover:bg-[#bff2d6]/80 hover:text-[#087f5b]"
          )}
        >
          <Link href="/settings">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Link>
        </Button>
      </div>
    </div>
  );
}

export default function Sidebar() {
  return (
    <>
      {/* Desktop sidebar */}
      <aside className="hidden border-r border-gray-200 bg-background lg:block lg:w-64">
        <NavContent />
      </aside>

      {/* Mobile sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button 
            variant="ghost" 
            className="px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 lg:hidden"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-0 bg-[#e8fff3] border-[#bff2d6]">
          <NavContent />
        </SheetContent>
      </Sheet>
    </>
  );
}