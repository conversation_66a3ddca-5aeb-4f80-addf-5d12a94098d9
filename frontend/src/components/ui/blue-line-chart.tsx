import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';

// Register the required Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface BlueLineChartProps {
  title?: string;
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor?: string;
    backgroundColor?: string;
  }[];
  height?: number;
  showLegend?: boolean;
}

export function BlueLineChart({
  title,
  labels,
  datasets,
  height = 300,
  showLegend = true,
}: BlueLineChartProps) {
  // Process datasets to add default styling if not provided
  const processedDatasets = datasets.map((dataset, index) => {
    // Default colors based on our blue theme
    const colors = [
      { border: '#4361ee', background: 'rgba(67, 97, 238, 0.1)' },
      { border: '#3a86ff', background: 'rgba(58, 134, 255, 0.1)' },
      { border: '#38bdf8', background: 'rgba(56, 189, 248, 0.1)' },
    ];
    
    const colorIndex = index % colors.length;
    
    return {
      ...dataset,
      borderColor: dataset.borderColor || colors[colorIndex].border,
      backgroundColor: dataset.backgroundColor || colors[colorIndex].background,
      tension: 0.3,
      fill: true,
      pointBackgroundColor: dataset.borderColor || colors[colorIndex].border,
      pointBorderColor: '#fff',
      pointBorderWidth: 1,
      pointRadius: 4,
      pointHoverRadius: 6,
      borderWidth: 2,
    };
  });

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: showLegend,
        position: 'top' as const,
        align: 'end' as const,
        labels: {
          boxWidth: 12,
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 20,
          color: '#64748b',
          font: {
            family: "'Inter', sans-serif",
            size: 12,
          },
        },
      },
      title: {
        display: !!title,
        text: title || '',
        color: '#111827',
        font: {
          family: "'Inter', sans-serif",
          size: 16,
          weight: 'normal' as const,
        },
        padding: {
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#111827',
        bodyColor: '#111827',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        bodyFont: {
          family: "'Inter', sans-serif",
        },
        titleFont: {
          family: "'Inter', sans-serif",
          weight: 'bold' as const,
        },
        callbacks: {
          label: function(context: any) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat('en-GB', { 
                style: 'currency', 
                currency: 'GBP',
                minimumFractionDigits: 2
              }).format(context.parsed.y);
            }
            return label;
          }
        }
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#64748b',
          font: {
            family: "'Inter', sans-serif",
            size: 12,
          },
          maxRotation: 0,
        },
        border: {
          display: false,
        },
      },
      y: {
        grid: {
          color: '#f1f5f9',
        },
        border: {
          display: false,
        },
        ticks: {
          color: '#64748b',
          font: {
            family: "'Inter', sans-serif",
            size: 12,
          },
          callback: function(value: any) {
            return '£' + value.toLocaleString('en-GB');
          }
        },
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    elements: {
      line: {
        borderJoinStyle: 'round' as const,
        capBezierPoints: true,
      },
    },
  };

  return (
    <div style={{ height: height }}>
      <Line data={{ labels, datasets: processedDatasets }} options={options} />
    </div>
  );
}