"use client";

import { useState, useEffect } from "react";
import { X } from "lucide-react";
import { Button } from "./button";

export interface ToastProps {
  message: string;
  type?: "success" | "error" | "info";
  duration?: number;
  onClose: () => void;
  onUndo?: () => void;
}

export function Toast({ 
  message, 
  type = "info", 
  duration = 5000, 
  onClose, 
  onUndo 
}: ToastProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      onClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const handleClose = () => {
    setIsVisible(false);
    onClose();
  };

  if (!isVisible) return null;

  return (
    <div
      className={`
        fixed bottom-4 right-4 flex items-center gap-2 p-4 rounded-lg shadow-lg
        ${type === "success" ? "bg-green-100 text-green-800" : ""}
        ${type === "error" ? "bg-red-100 text-red-800" : ""}
        ${type === "info" ? "bg-blue-100 text-blue-800" : ""}
      `}
    >
      <p className="text-sm">{message}</p>
      {onUndo && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            onUndo();
            handleClose();
          }}
          className="ml-2"
        >
          Undo
        </Button>
      )}
      <button
        onClick={handleClose}
        className="ml-2 p-1 hover:bg-black/5 rounded"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  );
}

interface ToastState {
  message: string;
  type: "success" | "error" | "info";
  onUndo?: () => void;
}

export function useToast() {
  const [toast, setToast] = useState<ToastState | null>(null);

  const showToast = (message: string, type: ToastState["type"] = "info", onUndo?: () => void) => {
    setToast({ message, type, onUndo });
  };

  const hideToast = () => {
    setToast(null);
  };

  const ToastComponent = toast ? (
    <Toast
      message={toast.message}
      type={toast.type}
      onClose={hideToast}
      onUndo={toast.onUndo}
    />
  ) : null;

  return { showToast, hideToast, ToastComponent };
}
