"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Check<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, Clock, RefreshCw } from "lucide-react";
import Link from "next/link";

interface DashboardStats {
  orders: {
    total: number;
    pending: number;
    processed: number;
  };
  checklist: {
    total: number;
    pending: number;
    received: number;
    incorrect: number;
    missing: number;
  };
  invoices: {
    total: number;
    reconciled: number;
    unreconciled: number;
  };
  statements: {
    total: number;
    reconciled: number;
    unreconciled: number;
  };
  creditRequests: {
    total: number;
    pending: number;
    sent: number;
    approved: number;
    rejected: number;
  };
}

interface MainDashboardProps {
  stats: DashboardStats;
  onRefresh: () => Promise<void>;
}

export function MainDashboard({ stats, onRefresh }: MainDashboardProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusColor = (value: number, total: number) => {
    if (total === 0) return "bg-gray-100";
    const percentage = (value / total) * 100;
    if (percentage >= 90) return "bg-green-500";
    if (percentage >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  return (
    <div className="w-full max-w-[1400px] mx-auto px-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">Dashboard</h2>
        <Button
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </div>

      {/* Workflow Steps */}
      <div className="mb-8">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Workflow</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <WorkflowStep
            title="Orders"
            description="Retrieve and manage orders from Drug Comparison"
            count={stats.orders.total}
            status={stats.orders.processed === stats.orders.total ? "complete" : "in-progress"}
            link="/orders"
          />
          <div className="hidden md:flex items-center justify-center">
            <ArrowRight className="h-6 w-6 text-gray-400" />
          </div>
          <WorkflowStep
            title="Checklist"
            description="Verify received items against orders"
            count={stats.checklist.total}
            status={stats.checklist.pending === 0 ? "complete" : "in-progress"}
            link="/checklist"
          />
          <div className="hidden md:flex items-center justify-center">
            <ArrowRight className="h-6 w-6 text-gray-400" />
          </div>
          <WorkflowStep
            title="Reconciliation"
            description="Compare invoices with orders and statements"
            count={stats.invoices.total}
            status={stats.invoices.unreconciled === 0 ? "complete" : "in-progress"}
            link="/reconciliation"
          />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatsCard
          title="Orders"
          total={stats.orders.total}
          items={[
            { label: "Pending", value: stats.orders.pending },
            { label: "Processed", value: stats.orders.processed }
          ]}
        />
        <StatsCard
          title="Checklist Items"
          total={stats.checklist.total}
          items={[
            { label: "Pending", value: stats.checklist.pending },
            { label: "Received", value: stats.checklist.received },
            { label: "Incorrect", value: stats.checklist.incorrect },
            { label: "Missing", value: stats.checklist.missing }
          ]}
        />
        <StatsCard
          title="Invoices"
          total={stats.invoices.total}
          items={[
            { label: "Reconciled", value: stats.invoices.reconciled },
            { label: "Unreconciled", value: stats.invoices.unreconciled }
          ]}
        />
        <StatsCard
          title="Credit Requests"
          total={stats.creditRequests.total}
          items={[
            { label: "Pending", value: stats.creditRequests.pending },
            { label: "Sent", value: stats.creditRequests.sent },
            { label: "Approved", value: stats.creditRequests.approved },
            { label: "Rejected", value: stats.creditRequests.rejected }
          ]}
        />
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <QuickActionCard
            title="Retrieve Orders"
            description="Pull new orders from Drug Comparison"
            link="/orders/retrieve"
          />
          <QuickActionCard
            title="Generate Checklist"
            description="Create checklist items from orders"
            link="/checklist/generate"
          />
          <QuickActionCard
            title="Process Emails"
            description="Retrieve and process invoice emails"
            link="/invoices/emails"
          />
          <QuickActionCard
            title="Upload Invoice"
            description="Manually upload an invoice PDF"
            link="/invoices/upload"
          />
        </div>
      </div>

      {/* Recent Activity */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div className="bg-white shadow-sm rounded-xl border border-gray-100 overflow-hidden">
          <div className="p-4 text-center text-gray-500">
            Activity tracking will be implemented in a future update
          </div>
        </div>
      </div>
    </div>
  );
}

interface WorkflowStepProps {
  title: string;
  description: string;
  count: number;
  status: "not-started" | "in-progress" | "complete";
  link: string;
}

function WorkflowStep({ title, description, count, status, link }: WorkflowStepProps) {
  return (
    <Link href={link} className="block">
      <div className="bg-white shadow-sm rounded-xl border border-gray-100 p-4 hover:shadow-md transition-shadow">
        <div className="flex justify-between items-start mb-2">
          <h4 className="font-medium text-gray-900">{title}</h4>
          {status === "complete" ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : status === "in-progress" ? (
            <Clock className="h-5 w-5 text-yellow-500" />
          ) : (
            <AlertTriangle className="h-5 w-5 text-gray-400" />
          )}
        </div>
        <p className="text-sm text-gray-600 mb-2">{description}</p>
        <div className="text-sm font-medium text-gray-900">{count} items</div>
      </div>
    </Link>
  );
}

interface StatsCardProps {
  title: string;
  total: number;
  items: Array<{ label: string; value: number }>;
}

function StatsCard({ title, total, items }: StatsCardProps) {
  return (
    <div className="bg-white shadow-sm rounded-xl border border-gray-100 p-4">
      <h4 className="font-medium text-gray-900 mb-2">{title}</h4>
      <div className="text-2xl font-semibold text-gray-900 mb-4">{total}</div>
      <div className="space-y-2">
        {items.map((item, index) => (
          <div key={index} className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{item.label}</span>
            <span className="text-sm font-medium text-gray-900">{item.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

interface QuickActionCardProps {
  title: string;
  description: string;
  link: string;
}

function QuickActionCard({ title, description, link }: QuickActionCardProps) {
  return (
    <Link href={link} className="block">
      <div className="bg-white shadow-sm rounded-xl border border-gray-100 p-4 hover:shadow-md transition-shadow">
        <h4 className="font-medium text-gray-900 mb-1">{title}</h4>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
    </Link>
  );
}