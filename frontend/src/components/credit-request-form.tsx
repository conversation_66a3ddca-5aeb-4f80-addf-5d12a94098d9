"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CreditRequest, CreditRequestItem } from "@/types/credit-request";
import { ChecklistItem } from "@/types/checklist";
import { useToast } from "@/components/ui/toast";

interface CreditRequestFormProps {
  checkedItems: ChecklistItem[];
  supplier: string;
  orderId: string;
  invoiceId: string;
  onSubmit: (request: Omit<CreditRequest, "id" | "status" | "requestDate" | "emailHistory">) => Promise<void>;
  onCancel: () => void;
}

export function CreditRequestForm({
  checkedItems,
  supplier,
  orderId,
  invoiceId,
  onSubmit,
  onCancel
}: CreditRequestFormProps) {
  const { showToast, ToastComponent } = useToast();
  const [items, setItems] = useState<CreditRequestItem[]>(
    checkedItems.map(item => ({
      productName: item.productName,
      pipCode: item.pipCode,
      quantity: Math.abs(item.expectedQuantity - (item.receivedQuantity || 0)),
      reason: item.status as CreditRequestItem["reason"],
      details: item.notes || "",
      originalUnitPrice: 0, // To be filled by user
      originalTotal: 0,
      images: []
    }))
  );
  const [notes, setNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleItemUpdate = (index: number, field: keyof CreditRequestItem, value: any) => {
    setItems(prev => {
      const newItems = [...prev];
      newItems[index] = {
        ...newItems[index],
        [field]: value,
        originalTotal: field === "originalUnitPrice" 
          ? value * newItems[index].quantity 
          : field === "quantity"
          ? newItems[index].originalUnitPrice * value
          : newItems[index].originalTotal
      };
      return newItems;
    });
  };

  const handleImageUpload = async (index: number, files: FileList | null) => {
    if (!files?.length) return;

    try {
      // TODO: Implement image upload to storage service
      const imageUrls = ["placeholder-url"]; // Replace with actual upload logic
      setItems(prev => {
        const newItems = [...prev];
        newItems[index] = {
          ...newItems[index],
          images: [...(newItems[index].images || []), ...imageUrls]
        };
        return newItems;
      });
    } catch (error) {
      console.error("Failed to upload images:", error);
      showToast("Failed to upload images", "error");
    }
  };

  const handleSubmit = async () => {
    if (!items.length) return;

    // Validate required fields
    const isValid = items.every(item => 
      item.quantity > 0 && 
      item.reason && 
      item.originalUnitPrice > 0 &&
      item.details
    );

    if (!isValid) {
      showToast("Please fill in all required fields", "error");
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({
        orderId,
        invoiceId,
        supplier,
        items,
        notes
      });
      showToast("Credit request submitted successfully", "success");
      onCancel();
    } catch (error) {
      console.error("Failed to submit credit request:", error);
      showToast("Failed to submit credit request", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Credit Request</h2>
        <div className="text-sm text-gray-500">
          <div>Order: {orderId}</div>
          <div>Invoice: {invoiceId}</div>
          <div>Supplier: {supplier}</div>
        </div>
      </div>

      <div className="space-y-4">
        {items.map((item, index) => (
          <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product
                </label>
                <div className="text-sm">{item.productName}</div>
                <div className="text-xs text-gray-500">{item.pipCode}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quantity
                </label>
                <input
                  type="number"
                  value={item.quantity}
                  onChange={(e) => handleItemUpdate(index, "quantity", parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md"
                  min="1"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reason
                </label>
                <Select
                  value={item.reason}
                  onValueChange={(value: CreditRequestItem["reason"]) => 
                    handleItemUpdate(index, "reason", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="missing">Missing</SelectItem>
                    <SelectItem value="damaged">Damaged</SelectItem>
                    <SelectItem value="incorrect">Incorrect</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Unit Price
                </label>
                <input
                  type="number"
                  value={item.originalUnitPrice}
                  onChange={(e) => handleItemUpdate(index, "originalUnitPrice", parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md"
                  step="0.01"
                  min="0"
                />
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Details
              </label>
              <textarea
                value={item.details}
                onChange={(e) => handleItemUpdate(index, "details", e.target.value)}
                className="w-full px-3 py-2 border border-gray-200 rounded-md"
                rows={2}
                placeholder="Provide details about the issue..."
              />
            </div>

            {item.reason === "damaged" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Images
                </label>
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleImageUpload(index, e.target.files)}
                  className="w-full"
                />
                {item.images?.length ? (
                  <div className="mt-2 flex gap-2">
                    {item.images.map((url, imgIndex) => (
                      <div key={imgIndex} className="relative w-20 h-20">
                        {/* Replace with actual image component */}
                        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                          IMG
                        </div>
                      </div>
                    ))}
                  </div>
                ) : null}
              </div>
            )}

            <div className="mt-2 text-sm text-gray-500">
              Total: £{item.originalTotal.toFixed(2)}
            </div>
          </div>
        ))}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Additional Notes
        </label>
        <textarea
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          className="w-full px-3 py-2 border border-gray-200 rounded-md"
          rows={3}
          placeholder="Any additional information for the supplier..."
        />
      </div>

      <div className="flex justify-end gap-4">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Submitting..." : "Submit Credit Request"}
        </Button>
      </div>

      {ToastComponent}
    </div>
  );
}
