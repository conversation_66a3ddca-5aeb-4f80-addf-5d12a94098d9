"use client";

import { Statement, StatementItem } from "@/types/statement";
import { useState, useMemo } from "react";
import { ArrowUpDown } from 'lucide-react';

interface StatementDetailProps {
  statement: Statement;
}

// Define SortField and SortOrder types for StatementItem
type SortFieldStatementItem = 'invoiceId' | 'date' | 'type' | 'amount';
type SortOrder = 'asc' | 'desc';


const formatNumber = (num: number | null | undefined): string => {
  if (typeof num !== 'number') {
    return 'N/A'; // Or format as £0.00, depending on preference
  }
  return num.toLocaleString('en-GB', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const formatDateSafely = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) {
    // console.log('formatDateSafely: Received null or undefined input');
    return 'N/A';
  }
  try {
    // If it's already a Date object, use it directly. Otherwise, try to parse.
    // Date objects passed from Server Components to Client Components are serialized to strings.
    const dateObj = (typeof dateInput === 'string' || typeof dateInput === 'number') ? new Date(dateInput) : dateInput;
    
    if (dateObj instanceof Date && !isNaN(dateObj.getTime())) {
      return dateObj.toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' });
    } else {
      console.warn('formatDateSafely: Input resulted in an Invalid Date:', dateInput, 'Parsed as:', dateObj);
      return 'Invalid Date';
    }
  } catch (e: any) {
    console.error('formatDateSafely: Error formatting date:', dateInput, e.message);
    return 'Date Error';
  }
};

// Reusable parseDate function (similar to statements-table)
const parseDate = (dateInput: string | Date | undefined | null): Date => {
  if (dateInput instanceof Date) {
    return !isNaN(dateInput.getTime()) ? dateInput : new Date(0);
  }
  if (!dateInput) {
    return new Date(0);
  }
  if (typeof dateInput === 'string') {
    const parts = dateInput.trim().split('/');
    if (parts.length === 3) {
      const dayStr = parts[0];
      const monthStr = parts[1];
      const yearStr = parts[2];
      const numDay = parseInt(dayStr, 10);
      const numMonth = parseInt(monthStr, 10);
      const numYear = parseInt(yearStr, 10);
      if (
        !isNaN(numDay) && !isNaN(numMonth) && !isNaN(numYear) &&
        numYear > 1000 && numYear < 3000 &&
        numMonth >= 1 && numMonth <= 12 &&
        numDay >= 1 && numDay <= 31
      ) {
        const parsed = new Date(numYear, numMonth - 1, numDay);
        if (
          !isNaN(parsed.getTime()) &&
          parsed.getFullYear() === numYear &&
          parsed.getMonth() === numMonth - 1 &&
          parsed.getDate() === numDay
        ) {
          return parsed;
        }
      }
    }
  }
  return new Date(0);
};

// Reusable getSortIcon helper
const getSortIcon = (currentSortField: SortFieldStatementItem, field: SortFieldStatementItem, currentSortOrder: SortOrder) => {
  if (currentSortField !== field) return null;
  return (
    <ArrowUpDown
      className="ml-1 h-4 w-4 text-gray-500" // Adjusted color for context
      style={{
        transform: currentSortOrder === 'asc' ? 'rotate(180deg)' : 'none'
      }}
    />
  );
};


// Helper component for rendering invoice rows
const InvoiceRows = ({ invoices }: { invoices: StatementItem[] }) => { // Use StatementItem[]
  if (!invoices || invoices.length === 0) {
    return (
      <tr>
        <td colSpan={4} className="px-6 py-4 text-center text-gray-500">No invoices listed on this statement.</td>
      </tr>
    );
  }
  return (
    <>
      {invoices.map((item, index) => ( // Renamed to item for clarity
        <tr key={`${item.invoiceId || 'item'}-${index}`} className="bg-white border-b hover:bg-gray-50 transition-colors">
          <td className="px-6 py-4 text-sm text-gray-900">{item.invoiceId || 'N/A'}</td>
          <td className="px-6 py-4 text-sm text-gray-600">{formatDateSafely(item.date)}</td>
          <td className="px-6 py-4 text-sm text-gray-500">
            {item.type ? item.type.charAt(0).toUpperCase() + item.type.slice(1) : 'N/A'}
          </td>
          <td className="px-6 py-4 text-sm text-gray-900 text-right font-medium">£{formatNumber(item.amount)}</td>
        </tr>
      ))}
    </>
  );
};

export function StatementDetail({ statement }: StatementDetailProps) {
  const [sortField, setSortField] = useState<SortFieldStatementItem>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');

  // Guard against statement itself being null or undefined
  if (!statement) {
    return <div className="p-4 text-red-500">Error: Statement data is missing.</div>;
  }

  const handleSort = (field: SortFieldStatementItem) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('desc'); // Default to descending for a new field
    }
  };

  const sortedStatementInvoices = useMemo(() => {
    if (!statement.invoices) return [];
    return [...statement.invoices].sort((a, b) => {
      let comparison = 0;
      switch (sortField) {
        case 'invoiceId':
          comparison = (a.invoiceId || '').localeCompare(b.invoiceId || '');
          break;
        case 'date':
          comparison = parseDate(a.date).getTime() - parseDate(b.date).getTime();
          break;
        case 'type':
          comparison = (a.type || '').localeCompare(b.type || '');
          break;
        case 'amount':
          comparison = (a.amount || 0) - (b.amount || 0);
          break;
      }
      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }, [statement.invoices, sortField, sortOrder]);


  return (
    <div className="space-y-6">
      {/* Header with Statement Details */}
      <div className="flex justify-between items-start bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="space-y-1">
          <h2 className="text-xl font-semibold">{statement.supplier || 'N/A'}</h2>
          <p className="text-sm text-gray-600">
            Statement Date: {formatDateSafely(statement.statementDate)}
          </p>
          {/* Add View PDF Button */}
          {statement.id && ( // Only show button if statement ID exists
            <a
              href={`/api/statements/${statement.id}/pdf`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block mt-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
            >
              View Statement PDF
            </a>
          )}
        </div>
        <div className="text-right space-y-2">
          <div className="text-sm text-gray-600">
            <div>Created: {formatDateSafely(statement.createdAt)}</div>
            <div>Updated: {formatDateSafely(statement.updatedAt)}</div>
          </div>
          <div className="text-lg font-bold mt-4">
            Total Due: £{formatNumber(statement.totalAmount)}
          </div>
        </div>
      </div>

      {/* Invoices List */}
      <div>
        <h3 className="font-semibold mb-4">Included Invoices/Items</h3>
        <div className="relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100">
          <table className="w-full text-sm text-left">
            <thead className="bg-gray-50 border-b border-gray-100">
              <tr>
                <th scope="col" className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600" onClick={() => handleSort('invoiceId')}>
                  <div className="flex items-center">
                    <span>Invoice/Item ID</span>
                    {getSortIcon(sortField, 'invoiceId', sortOrder)}
                  </div>
                </th>
                <th scope="col" className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600" onClick={() => handleSort('date')}>
                  <div className="flex items-center">
                    <span>Date</span>
                    {getSortIcon(sortField, 'date', sortOrder)}
                  </div>
                </th>
                <th scope="col" className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600" onClick={() => handleSort('type')}>
                  <div className="flex items-center">
                    <span>Type</span> {/* Changed header from Status to Type for clarity */}
                    {getSortIcon(sortField, 'type', sortOrder)}
                  </div>
                </th>
                <th scope="col" className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600 text-right" onClick={() => handleSort('amount')}>
                  <div className="flex items-center justify-end">
                    <span>Amount</span>
                    {getSortIcon(sortField, 'amount', sortOrder)}
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <InvoiceRows invoices={sortedStatementInvoices} />
              {/* CreditRows call is removed */}
              <tr className="bg-gray-50">
                <td colSpan={3} className="px-6 py-4 text-sm text-gray-900 font-semibold">Total Amount</td>
                <td className="px-6 py-4 text-sm text-gray-900 text-right font-semibold">£{formatNumber(statement.totalAmount)}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
