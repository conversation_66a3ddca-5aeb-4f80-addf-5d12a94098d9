"use client";

import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from "date-fns";
import { CalendarIcon, CheckCircle } from "lucide-react";
import { Order } from "@/lib/csv-utils";
import { ChecklistItem } from "@/types/checklist";
import { ordersToChecklistItems, filterOrders } from "@/lib/order-to-checklist";
import { useToast } from "@/components/ui/toast";

interface ChecklistGeneratorProps {
  orders: Order[];
  onGenerate: (items: ChecklistItem[]) => Promise<void>;
  userId: string;
}

export function ChecklistGenerator({ orders, onGenerate, userId }: ChecklistGeneratorProps) {
  const { showToast, ToastComponent } = useToast();
  const [supplierFilter, setSupplierFilter] = useState<string>("allSuppliers");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());

  const uniqueSuppliers = useMemo(() => {
    console.log('All orders for supplier extraction:', orders);
    const suppliers = Array.from(new Set(orders.map(order => order.supplier ?? ''))).sort();
    console.log('Unique suppliers found:', suppliers);
    return suppliers;
  }, [orders]);

  const cleanedSuppliers = useMemo(() => {
    const cleaned = uniqueSuppliers.map((supplier) =>
      !supplier || supplier.trim() === "" ? "N/A" : supplier.trim()
    );
    console.log('Cleaned suppliers:', cleaned);
    return cleaned;
  }, [uniqueSuppliers]);

  const filteredOrders = useMemo(() => {
    console.log('Filtering with:', {
      supplierFilter,
      startDate: startDate?.toISOString(),
      endDate: endDate?.toISOString()
    });
    
    const filtered = filterOrders(
      orders,
      supplierFilter !== "allSuppliers" ? supplierFilter : undefined,
      startDate,
      endDate
    );
    
    console.log('Filtered orders count:', filtered.length);
    return filtered;
  }, [orders, supplierFilter, startDate, endDate]);

  const handleSelectAll = () => {
    if (selectedOrders.size === filteredOrders.length) {
      setSelectedOrders(new Set());
    } else {
      setSelectedOrders(new Set(filteredOrders.map(order => `${order.orderNo}:${order.pipCode}`)));
    }
  };

  const handleGenerateChecklist = async () => {
    if (selectedOrders.size === 0) {
      showToast("Please select at least one order", "error");
      return;
    }

    setIsGenerating(true);
    try {
      const selectedOrdersArray = filteredOrders.filter(order =>
        selectedOrders.has(`${order.orderNo}:${order.pipCode}`)
      );
      
      const checklistItems = ordersToChecklistItems(selectedOrdersArray, userId);
      await onGenerate(checklistItems);
      
      showToast(`Successfully generated ${checklistItems.length} checklist items`, "success");
      setSelectedOrders(new Set());
    } catch (error) {
      console.error("Failed to generate checklist items:", error);
      showToast("Failed to generate checklist items", "error");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="w-full max-w-[1400px] mx-auto px-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">Generate Checklist</h2>
        <Button
          onClick={handleGenerateChecklist}
          disabled={isGenerating || selectedOrders.size === 0}
          className="flex items-center gap-2"
        >
          {isGenerating ? "Generating..." : `Generate Checklist (${selectedOrders.size})`}
        </Button>
      </div>

      <div className="flex gap-4 mb-6 items-center">
        <div className="flex gap-2 items-center">
          <span className="text-sm text-gray-600">From:</span>
          <Popover>
            <PopoverTrigger asChild className="border border-gray-200">
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50" align="start">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={(date) => {
                  console.log('Start date selected:', date?.toISOString());
                  setStartDate(date);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <span className="text-sm text-gray-600">To:</span>
          <Popover>
            <PopoverTrigger asChild className="border border-gray-200">
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50" align="start">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={(date) => {
                  console.log('End date selected:', date?.toISOString());
                  setEndDate(date);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <Select
          value={supplierFilter}
          onValueChange={(value) => {
            console.log('Supplier filter changed to:', value);
            setSupplierFilter(value);
          }}
        >
          <SelectTrigger className="w-[180px] border-gray-200">
            <SelectValue placeholder="All Suppliers" />
          </SelectTrigger>
          <SelectContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
            <SelectItem value="allSuppliers">All Suppliers</SelectItem>
            {cleanedSuppliers
              .filter(supplier => supplier && supplier !== "N/A")
              .map(supplier => {
                console.log('Rendering supplier option:', supplier);
                return (
                  <SelectItem key={supplier} value={supplier}>
                    {supplier}
                  </SelectItem>
                );
              })
            }
          </SelectContent>
        </Select>

        <Button
          variant="outline"
          size="sm"
          onClick={handleSelectAll}
          className="ml-auto"
        >
          {selectedOrders.size === filteredOrders.length ? "Deselect All" : "Select All"}
        </Button>
      </div>

      <div className="relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100">
        <table className="w-full text-sm text-left">
          <thead className="bg-gray-50 border-b border-gray-100">
            <tr>
              <th scope="col" className="px-6 py-4">
                <input
                  type="checkbox"
                  checked={filteredOrders.length > 0 && selectedOrders.size === filteredOrders.length}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300"
                />
              </th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Supplier</th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Date/Time</th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Details</th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600 text-right">Quantity</th>
            </tr>
          </thead>
          <tbody>
            {filteredOrders.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                  No orders found matching your filters
                </td>
              </tr>
            ) : (
              filteredOrders.map((order, index) => {
                const orderKey = `${order.orderNo}:${order.pipCode}`;
                const uniqueKey = `${orderKey}-${index}`;
                const isSelected = selectedOrders.has(orderKey);
                
                console.log('Rendering order row:', { orderKey, uniqueKey, order });
                
                return (
                  <tr
                    key={uniqueKey}
                    className={`border-b hover:bg-gray-50 transition-colors ${isSelected ? 'bg-blue-50' : 'bg-white'}`}
                  >
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => {
                          const newSelected = new Set(selectedOrders);
                          if (isSelected) {
                            newSelected.delete(orderKey);
                          } else {
                            newSelected.add(orderKey);
                          }
                          setSelectedOrders(newSelected);
                        }}
                        className="rounded border-gray-300"
                      />
                    </td>
                    <td className="px-6 py-4 text-gray-600">{order.supplier || 'N/A'}</td>
                    <td className="px-6 py-4 text-gray-600">{order.dateTime || ''}</td>
                    <td className="px-6 py-4">
                      <div className="space-y-1.5">
                        <div className="font-medium text-gray-900">{order.description || 'N/A'}</div>
                        <div className="text-xs text-gray-500">{order.orderNo || ''}</div>
                        <div className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded inline-block">
                          {order.pipCode || ''}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-right text-gray-600">
                      <span className="font-medium">{order.approvedQty}</span>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
      
      {ToastComponent}
    </div>
  );
}