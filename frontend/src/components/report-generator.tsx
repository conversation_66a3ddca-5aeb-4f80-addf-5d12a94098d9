"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Report, ReportTemplate } from "@/types/report";

interface ReportGeneratorProps {
  templates: ReportTemplate[];
  onGenerate: (config: ReportConfig) => Promise<void>;
}

interface ReportConfig {
  templateId: string;
  dateRange: {
    start: Date;
    end: Date;
  };
  customization: ReportTemplate["customization"];
  fields: Record<string, any>;
}

export function ReportGenerator({ templates, onGenerate }: ReportGeneratorProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [startDate, setStartDate] = useState<Date | undefined>();
  const [endDate, setEndDate] = useState<Date | undefined>();
  const [fields, setFields] = useState<Record<string, any>>({});
  const [customization, setCustomization] = useState<ReportTemplate["customization"]>({
    includeCharts: true,
    includeSummary: true,
    detailLevel: "detailed"
  });
  const [isGenerating, setIsGenerating] = useState(false);

  const handleTemplateChange = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
      // Reset fields when template changes
      setFields({});
    }
  };

  const handleFieldChange = (fieldName: string, value: any) => {
    setFields(prev => ({ ...prev, [fieldName]: value }));
  };

  const handleCustomizationChange = (key: keyof ReportTemplate["customization"], value: any) => {
    setCustomization(prev => ({ ...prev, [key]: value }));
  };

  const handleGenerate = async () => {
    if (!selectedTemplate || !startDate || !endDate) return;

    setIsGenerating(true);
    try {
      await onGenerate({
        templateId: selectedTemplate.id,
        dateRange: {
          start: startDate,
          end: endDate
        },
        customization,
        fields
      });
    } catch (error) {
      console.error("Failed to generate report:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const isValid = selectedTemplate && startDate && endDate && 
    selectedTemplate.fields.every(field => 
      !field.required || fields[field.name] !== undefined
    );

  return (
    <div className="space-y-6 bg-white p-6 rounded-lg shadow-sm border border-gray-100">
      <div>
        <h3 className="text-lg font-medium mb-4">Generate Report</h3>
        
        {/* Template Selection */}
        <div className="space-y-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Report Template
            </label>
            <Select
              value={selectedTemplate?.id}
              onValueChange={handleTemplateChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a template" />
              </SelectTrigger>
              <SelectContent>
                {templates.map(template => (
                  <SelectItem key={template.id} value={template.id}>
                    {template.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedTemplate && (
              <p className="mt-1 text-sm text-gray-500">{selectedTemplate.description}</p>
            )}
          </div>

          {/* Date Range */}
          <div className="flex gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        {/* Template Fields */}
        {selectedTemplate && (
          <div className="space-y-4 mb-6">
            <h4 className="text-sm font-medium text-gray-700">Fields</h4>
            {selectedTemplate.fields.map(field => (
              <div key={field.name}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {field.name} {field.required && <span className="text-red-500">*</span>}
                </label>
                {field.type === 'boolean' ? (
                  <Select
                    value={fields[field.name]?.toString()}
                    onValueChange={(value) => handleFieldChange(field.name, value === 'true')}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">Yes</SelectItem>
                      <SelectItem value="false">No</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <input
                    type={field.type === 'number' ? 'number' : 'text'}
                    value={fields[field.name] || ''}
                    onChange={(e) => handleFieldChange(field.name, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md"
                    required={field.required}
                  />
                )}
              </div>
            ))}
          </div>
        )}

        {/* Customization Options */}
        {selectedTemplate && (
          <div className="space-y-4 mb-6">
            <h4 className="text-sm font-medium text-gray-700">Customization</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Include Charts
                </label>
                <Select
                  value={customization.includeCharts.toString()}
                  onValueChange={(value) => 
                    handleCustomizationChange('includeCharts', value === 'true')
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">Yes</SelectItem>
                    <SelectItem value="false">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Include Summary
                </label>
                <Select
                  value={customization.includeSummary.toString()}
                  onValueChange={(value) => 
                    handleCustomizationChange('includeSummary', value === 'true')
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">Yes</SelectItem>
                    <SelectItem value="false">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Detail Level
                </label>
                <Select
                  value={customization.detailLevel}
                  onValueChange={(value: ReportTemplate["customization"]["detailLevel"]) => 
                    handleCustomizationChange('detailLevel', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basic">Basic</SelectItem>
                    <SelectItem value="detailed">Detailed</SelectItem>
                    <SelectItem value="comprehensive">Comprehensive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}

        {/* Generate Button */}
        <Button
          className="w-full"
          onClick={handleGenerate}
          disabled={!isValid || isGenerating}
        >
          {isGenerating ? "Generating..." : "Generate Report"}
        </Button>
      </div>
    </div>
  );
}
