import { useRouter } from "next/navigation";
import { useState, useMemo, useEffect } from 'react';
import { ArrowUpDown, Calendar as CalendarIcon, CheckCircle, XCircle, Trash2, Filter, Search, Download } from 'lucide-react';
import { Invoice } from "@/types/api";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { getSupplierLogoUrl } from "@/lib/csv-utils";
import { DeleteConfirmationModal } from "./ui/delete-confirmation-modal";
import { invoicesApi } from "@/lib/api";

interface InvoicesTableProps {
  invoices: Invoice[];
}

type SortField = 'date' | 'dueDate' | 'total' | 'items';
type SortOrder = 'asc' | 'desc';
type StatusFilter = 'all' | 'orderReconciled' | 'orderUnreconciled' | 'statementReconciled' | 'statementUnreconciled';

const getSortIcon = (currentSortField: SortField, field: SortField, currentSortOrder: SortOrder) => {
  if (currentSortField !== field) return null;
  return (
    <ArrowUpDown
      className="ml-1 h-4 w-4 text-muted-foreground"
      style={{
        transform: currentSortOrder === 'asc' ? 'rotate(180deg)' : 'none'
      }}
    />
  );
};

const formatNumber = (num: number | undefined): string => {
  if (num === undefined || isNaN(num)) return '0.00';
  return num.toLocaleString('en-GB', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const parseDate = (dateStr: string | undefined): Date => {
  if (!dateStr) return new Date();
  const [day, month, year] = (dateStr || '').split('/');
  const parsedDate = new Date(Number(year), Number(month) - 1, Number(day));
  return isNaN(parsedDate.getTime()) ? new Date() : parsedDate;
};

export function InvoicesTable({ invoices }: InvoicesTableProps) {
  const router = useRouter();
  const [selectedInvoices, setSelectedInvoices] = useState<Set<string>>(new Set());
  const [isDeleting, setIsDeleting] = useState(false);
  const [vendorFilter, setVendorFilter] = useState<string>("allVendors");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [sortField, setSortField] = useState<SortField>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const handleSelectInvoice = (event: React.ChangeEvent<HTMLInputElement>, id: string) => {
    event.stopPropagation();
    setSelectedInvoices(prev => {
      const newSelection = new Set(Array.from(prev));
      if (event.target.checked) {
        newSelection.add(id);
      } else {
        newSelection.delete(id);
      }
      return newSelection;
    });
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelectedInvoices(new Set(paginatedInvoices.map(invoice => invoice.id)));
    } else {
      setSelectedInvoices(new Set());
    }
  };

  const handleDelete = async () => {
    try {
      await invoicesApi.deleteInvoices(Array.from(selectedInvoices));
      setIsDeleting(false);
      setSelectedInvoices(new Set());
      router.refresh();
    } catch (error) {
      console.error('Error deleting invoices:', error);
    }
  };

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [vendorFilter, statusFilter, startDate, endDate, searchQuery]);

  const uniqueVendors = useMemo(() => {
    return Array.from(new Set(invoices.map(invoice => invoice.VendorName || 'Unknown'))).sort();
  }, [invoices]);

  const isDateInRange = (dateStr: string | undefined) => {
    if (!startDate && !endDate) return true;
    if (!dateStr) return false;
    
    const invoiceDate = parseDate(dateStr);
    
    if (startDate) {
      const startOfDay = new Date(startDate);
      startOfDay.setHours(0, 0, 0, 0);
      if (invoiceDate < startOfDay) return false;
    }
    
    if (endDate) {
      const endOfDay = new Date(endDate);
      endOfDay.setHours(23, 59, 59, 999);
      if (invoiceDate > endOfDay) return false;
    }
    
    return true;
  };

  const filteredInvoices = useMemo(() => {
    return invoices.filter(invoice => {
      // Search query filter
      const matchesSearch = !searchQuery || 
        (invoice.InvoiceId?.toLowerCase().includes(searchQuery.toLowerCase()) || 
         invoice.VendorName?.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesVendor = !vendorFilter || vendorFilter === "allVendors" || invoice.VendorName === vendorFilter;
      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'orderReconciled' && invoice.orderReconciled) ||
        (statusFilter === 'orderUnreconciled' && !invoice.orderReconciled) ||
        (statusFilter === 'statementReconciled' && invoice.statementReconciled) ||
        (statusFilter === 'statementUnreconciled' && !invoice.statementReconciled);
      const matchesDate = isDateInRange(invoice.InvoiceDate);
      
      return matchesSearch && matchesVendor && matchesStatus && matchesDate;
    });
  }, [invoices, vendorFilter, statusFilter, startDate, endDate, searchQuery]);

  const sortedInvoices = useMemo(() => {
    return [...filteredInvoices].sort((a, b) => {
      let comparison = 0;
      switch (sortField) {
        case 'date':
          comparison = parseDate(a.InvoiceDate).getTime() - parseDate(b.InvoiceDate).getTime();
          break;
        case 'dueDate':
          comparison = parseDate(a.DueDate).getTime() - parseDate(b.DueDate).getTime();
          break;
        case 'items':
          comparison = (a.Items?.length || 0) - (b.Items?.length || 0);
          break;
        case 'total':
          comparison = (a.InvoiceTotal || 0) - (b.InvoiceTotal || 0);
          break;
      }
      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }, [filteredInvoices, sortField, sortOrder]);

  const totalAmount = useMemo(() => {
    return filteredInvoices.reduce((sum, invoice) => sum + (invoice.InvoiceTotal || 0), 0);
  }, [filteredInvoices]);

  // Calculate pagination
  const paginatedInvoices = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedInvoices.slice(startIndex, endIndex);
  }, [sortedInvoices, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(sortedInvoices.length / itemsPerPage);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('desc');
    }
  };

  const handleRowClick = (e: React.MouseEvent<HTMLTableRowElement>, id: string) => {
    const target = e.target as HTMLElement;
    if (!target.closest('input[type="checkbox"]')) {
      router.push(`/invoices/${id}`);
    }
  };

  const activeFiltersCount = [
    vendorFilter !== "allVendors",
    statusFilter !== "all",
    !!startDate,
    !!endDate,
    !!searchQuery
  ].filter(Boolean).length;

  return (
    <div className="w-full max-w-[1400px] mx-auto px-8 py-6">
      {/* Header with title and actions */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-medium text-foreground">
            Billing & Invoice
          </h2>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search invoices..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 pr-4 py-2 h-9 w-[200px] text-sm border border-border rounded-md focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary"
            />
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="w-4 h-4" />
            Filters
            {activeFiltersCount > 0 && (
              <span className="flex items-center justify-center bg-primary text-white rounded-full w-5 h-5 text-xs">
                {activeFiltersCount}
              </span>
            )}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Export
          </Button>
          
          {selectedInvoices.size > 0 && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setIsDeleting(true)}
              className="flex items-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Delete ({selectedInvoices.size})
            </Button>
          )}
        </div>
      </div>

      <DeleteConfirmationModal
        isOpen={isDeleting}
        onClose={() => setIsDeleting(false)}
        onConfirm={handleDelete}
        itemCount={selectedInvoices.size}
      />

      {/* Filters panel */}
      {showFilters && (
        <div className="bg-white rounded-lg shadow-sm border border-border/40 p-4 mb-6">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex gap-2 items-center">
              <span className="text-sm text-muted-foreground">From:</span>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent align="start">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>

              <span className="text-sm text-muted-foreground">To:</span>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent align="start">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <Select value={vendorFilter} onValueChange={(value) => setVendorFilter(value === "" ? "allVendors" : value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Vendors" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="allVendors">All Vendors</SelectItem>
                {uniqueVendors.map(vendor => (
                  <SelectItem key={vendor} value={vendor}>
                    {vendor}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Reconciliation Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Invoices</SelectItem>
                <SelectItem value="orderReconciled">Order Reconciled</SelectItem>
                <SelectItem value="orderUnreconciled">Order Unreconciled</SelectItem>
                <SelectItem value="statementReconciled">Statement Reconciled</SelectItem>
                <SelectItem value="statementUnreconciled">Statement Unreconciled</SelectItem>
              </SelectContent>
            </Select>
            
            <Button 
              variant="subtle" 
              size="sm" 
              onClick={() => {
                setVendorFilter("allVendors");
                setStatusFilter("all");
                setStartDate(undefined);
                setEndDate(undefined);
                setSearchQuery("");
              }}
              className="ml-auto"
            >
              Reset Filters
            </Button>
          </div>
        </div>
      )}

      {/* Summary card */}
      <div className="bg-white rounded-lg shadow-sm border border-border/40 p-4 mb-6">
        <div className="text-lg font-medium text-foreground">
          Total: <span className="text-primary">£{formatNumber(totalAmount)}</span>
        </div>
      </div>

      {/* Table */}
      <div className="relative overflow-hidden bg-white rounded-lg shadow-sm border border-border/40">
        <table className="w-full text-sm text-left">
          <thead className="bg-white border-b border-border/40">
            <tr>
              <th className="px-6 py-3 w-10">
                <input
                  type="checkbox"
                  onChange={handleSelectAll}
                  checked={selectedInvoices.size === paginatedInvoices.length && paginatedInvoices.length > 0}
                  className="w-4 h-4 rounded border-input text-primary focus:ring-1 focus:ring-primary/20"
                />
              </th>
              <th className="px-6 py-3 text-xs font-medium text-muted-foreground">Invoice</th>
              <th
                className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground"
                onClick={() => handleSort('date')}
              >
                <div className="flex items-center">
                  <span>Date</span>
                  {getSortIcon(sortField, 'date', sortOrder)}
                </div>
              </th>
              <th className="px-6 py-3 text-xs font-medium text-muted-foreground">Customer name</th>
              <th className="px-6 py-3 text-xs font-medium text-muted-foreground">Status</th>
              <th
                className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground text-right"
                onClick={() => handleSort('total')}
              >
                <div className="flex items-center justify-end">
                  <span>Amount</span>
                  {getSortIcon(sortField, 'total', sortOrder)}
                </div>
              </th>
              <th className="px-6 py-3 w-8"></th>
            </tr>
          </thead>
          <tbody>
            {sortedInvoices.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-6 py-8 text-center text-muted-foreground">
                  No invoices found matching your filters
                </td>
              </tr>
            ) : (
              paginatedInvoices.map((invoice) => (
                <tr
                  key={invoice.id}
                  onClick={(e) => handleRowClick(e, invoice.id)}
                  className={cn(
                    "border-b border-border/40 hover:bg-muted/30 transition-colors cursor-pointer",
                    selectedInvoices.has(invoice.id) && "bg-primary/5"
                  )}
                >
                  <td className="px-6 py-4 w-10">
                    <input
                      type="checkbox"
                      checked={selectedInvoices.has(invoice.id)}
                      onChange={(e) => handleSelectInvoice(e, invoice.id)}
                      className="w-4 h-4 rounded border-input text-primary focus:ring-1 focus:ring-primary/20"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      {(() => {
                          const logo = getSupplierLogoUrl(invoice.VendorName);
                          return logo ? (
                            <div className="relative w-8 h-8 flex-shrink-0">
                              <Image
                                src={logo}
                                alt={invoice.VendorName || 'Unknown Vendor'}
                                fill
                                sizes="32px"
                                className="rounded-md object-contain"
                                quality={85}
                                priority
                              />
                            </div>
                          ) : null;
                      })()}
                      <div>
                        <div className="font-medium text-foreground">{invoice.InvoiceId || 'N/A'}</div>
                        <div className="text-xs text-muted-foreground">{invoice.VendorName || 'Unknown Vendor'}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-foreground">{invoice.InvoiceDate || 'N/A'}</td>
                  <td className="px-6 py-4 text-sm text-foreground">Customer Name</td>
                  <td className="px-6 py-4">
                    {invoice.orderReconciled ? (
                      <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success/10 text-success">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Paid
                      </div>
                    ) : (
                      <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-warning/10 text-warning">
                        <XCircle className="w-3 h-3 mr-1" />
                        Pending
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 text-sm text-right font-medium text-foreground">
                    £{formatNumber(invoice.InvoiceTotal)}
                  </td>
                  <td className="px-6 py-4 text-right">
                    <button className="text-muted-foreground hover:text-primary">
                      <svg width="16" height="4" viewBox="0 0 16 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 0C6.9 0 6 0.9 6 2C6 3.1 6.9 4 8 4C9.1 4 10 3.1 10 2C10 0.9 9.1 0 8 0ZM14 0C12.9 0 12 0.9 12 2C12 3.1 12.9 4 14 4C15.1 4 16 3.1 16 2C16 0.9 15.1 0 14 0ZM2 0C0.9 0 0 0.9 0 2C0 3.1 0.9 4 2 4C3.1 4 4 3.1 4 2C4 0.9 3.1 0 2 0Z" fill="currentColor"/>
                      </svg>
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {sortedInvoices.length > 0 && (
        <div className="flex items-center justify-between p-4 mt-4">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium text-foreground">{((currentPage - 1) * itemsPerPage) + 1}</span> to{' '}
            <span className="font-medium text-foreground">
              {Math.min(currentPage * itemsPerPage, sortedInvoices.length)}
            </span> of{' '}
            <span className="font-medium text-foreground">{sortedInvoices.length}</span> results
          </div>
          
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <div className="flex items-center justify-center px-3 h-9 text-sm font-medium text-foreground min-w-[80px]">
              Page {currentPage} of {totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}