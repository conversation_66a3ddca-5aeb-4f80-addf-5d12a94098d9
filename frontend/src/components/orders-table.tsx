"use client";

import Image from 'next/image';
import { CheckCircle, XCircle, ArrowUpDown, Calendar as CalendarIcon, RefreshCw } from 'lucide-react';
import { getSupplierLogoUrl, isOrderSuccessful, type Order } from '@/lib/csv-utils';
import { ordersApi } from '@/lib/api';
import { useState, useMemo, useEffect, useRef } from 'react';
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from "date-fns";
import { DateRangePickerDialog } from "@/components/date-range-picker-dialog";
import { cn } from "@/lib/utils";
import { unique } from 'next/dist/build/utils';

interface OrdersTableProps {
  orders: Order[];
}

type SortField = 'dateTime' | 'approvedQty' | 'price' | 'subTotal';
type SortOrder = 'asc' | 'desc';
type StatusFilter = 'all' | 'approved' | 'rejected';

const getSortIcon = (currentSortField: SortField, field: SortField, currentSortOrder: SortOrder) => {
  if (currentSortField !== field) return null;
  return (
    <ArrowUpDown
      className="ml-1 h-4 w-4 text-gray-900"
      style={{
        transform: currentSortOrder === 'asc' ? 'rotate(180deg)' : 'none'
      }}
    />
  );
};

const formatNumber = (num: number): string => {
  return num.toLocaleString('en-GB', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

export function OrdersTable({ orders: initialOrders }: OrdersTableProps) {
  const [orders, setOrders] = useState<Order[]>(initialOrders);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [supplierFilter, setSupplierFilter] = useState<string>("allSuppliers");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [sortField, setSortField] = useState<SortField>('dateTime');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [isRetrieving, setIsRetrieving] = useState(false);
  const [retrieveError, setRetrieveError] = useState<string | null>(null);

  const handleRetrieveOrders = async (startDate: Date, endDate: Date) => {
    setIsRetrieving(true);
    setRetrieveError(null);
    
    try {
      // Format dates consistently with backend expectation (YYYY-MM-DD HH:mm)
      const formattedStartDate = format(startDate, "yyyy-MM-dd") + " 00:00";
      const formattedEndDate = format(endDate, "yyyy-MM-dd") + " 23:59";

      console.log('Sending request to Next.js API with dates:', { formattedStartDate, formattedEndDate });
      
      const response = await fetch('/api/orders/retrieve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          startDate: formattedStartDate,
          endDate: formattedEndDate
        })
      });

      console.log('Response status:', response.status);
      
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));
      
      // Get response text for debugging
      const text = await response.text();
      console.log('Raw response:', text);

      // Validate that we have a response
      if (!text) {
        throw new Error('Empty response from server');
      }

      // Try to parse JSON
      let data;
      try {
        data = text ? JSON.parse(text) : null;
      } catch (err) {
        const jsonError = err as Error;
        console.error('JSON parse error:', jsonError.name, jsonError.message);
        console.error('Failed response text:', text);
        throw new Error('Invalid JSON response from server');
      }

      // Validate parsed data
      if (!data) {
        console.error('Empty data after parsing');
        throw new Error('Empty response from server');
      }

      console.log('Parsed response:', data);

      if (!response.ok) {
        throw new Error(data.error?.message || 'Failed to retrieve orders');
      }

      // Show response message with date information
      // On successful retrieval
      const messageWithDates = data.dates
        ? `${data.message}\nDate Range: ${data.dates.startDate} to ${data.dates.endDate}`
        : data.message || 'Orders retrieved successfully';

      // Try to refresh orders
      const refreshSuccessful = await refreshOrders();
      
      if (!refreshSuccessful) {
        alert(messageWithDates + '\nNote: Please refresh the page to see the latest data.');
      } else {
        alert(messageWithDates + '\nOrders list has been updated.');
      }
    } catch (error: unknown) {
      console.error('Error retrieving orders:', error);
      
      if (error && typeof error === 'object' && 'response' in error) {
        const apiError = error.response as { error?: { message?: string } };
        if (apiError.error?.message) {
          setRetrieveError(apiError.error.message);
          return;
        }
      }
      
      setRetrieveError(error instanceof Error ? error.message : 'Failed to retrieve orders');
    } finally {
      setIsRetrieving(false);
    }
  };

  const refreshOrders = async () => {
    setIsRefreshing(true);
    try {
      const response = await fetch('/api/orders');
      if (!response.ok) {
        throw new Error('Failed to fetch updated orders');
      }
      const updatedOrders = await response.json();
      setOrders(updatedOrders);
      return true;
    } catch (error) {
      console.error('Error refreshing orders:', error);
      return false;
    } finally {
      setIsRefreshing(false);
    }
  };

  const uniqueSuppliers = useMemo(() => {
    return Array.from(new Set(orders.map(order => order.supplier ?? ''))).sort();
  }, [orders]);

  const cleanedSuppliers = uniqueSuppliers.map((supplier) =>
    !supplier || supplier.trim() === "" ? "N/A" : supplier
  );

  const parseOrderDate = (dateStr: string): Date => {
    try {
      // Convert "DD-MM-YYYY HH:mm" to Date object
      const parts = dateStr.split(' ');
      const datePart = parts[0];
      const timePart = parts[1] || '00:00'; // Default to midnight if no time part
      
      const [day, month, year] = datePart.split('-');
      const [hours, minutes] = timePart.split(':');
      
      // Log for debugging
      console.log(`Parsing date: ${dateStr} -> ${year}-${month}-${day} ${hours}:${minutes}`);
      
      return new Date(Number(year), Number(month) - 1, Number(day), Number(hours), Number(minutes));
    } catch (error) {
      console.error(`Error parsing date: ${dateStr}`, error);
      return new Date(); // Fallback to current date
    }
  };

  const isDateInRange = useMemo(() => {
    return (dateStr: string) => {
      if (!startDate && !endDate) return true;
      if (!dateStr) return false;
      
      try {
        const orderDate = parseOrderDate(dateStr);
        const start = startDate;
        const end = endDate;
        
        if (start) {
          const startOfDay = new Date(start);
          startOfDay.setHours(0, 0, 0, 0);
          if (orderDate < startOfDay) return false;
        }
        
        if (end) {
          const endOfDay = new Date(end);
          endOfDay.setHours(23, 59, 59, 999);
          if (orderDate > endOfDay) return false;
        }
        
        return true;
      } catch (error) {
        console.error(`Error checking date range for: ${dateStr}`, error);
        return true; // Include by default if there's an error
      }
    };
  }, [startDate, endDate]);

  // Add a stable reference check to prevent unnecessary re-renders
  const ordersRef = useRef<Order[]>(orders);
  
  // Only update the reference if the orders array has actually changed
  useEffect(() => {
    if (JSON.stringify(ordersRef.current) !== JSON.stringify(orders)) {
      ordersRef.current = orders;
    }
  }, [orders]);
  
  const filteredOrders = useMemo(() => {
    return ordersRef.current.filter((order: Order) => {
      const matchesSupplier = !supplierFilter || supplierFilter === "allSuppliers" || order.supplier === supplierFilter;
      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'approved' && isOrderSuccessful(order)) ||
        (statusFilter === 'rejected' && !isOrderSuccessful(order));
      
      const matchesDate = isDateInRange(order.dateTime);
      
      return matchesSupplier && matchesStatus && matchesDate;
    });
  }, [supplierFilter, statusFilter, isDateInRange, ordersRef]);

  const sortedOrders = useMemo(() => {
    return [...filteredOrders].sort((a, b) => {
      let comparison = 0;
      switch (sortField) {
        case 'dateTime':
          try {
            // Use the parseOrderDate function for consistent date parsing
            const dateA = parseOrderDate(a.dateTime);
            const dateB = parseOrderDate(b.dateTime);
            comparison = dateA.getTime() - dateB.getTime();
          } catch (error) {
            console.error('Error comparing dates:', error);
            comparison = 0;
          }
        break;
        case 'approvedQty':
          comparison = a.approvedQty - b.approvedQty;
        break;
        case 'price':
          comparison = a.price - b.price;
        break;
        case 'subTotal':
          comparison = (a.approvedQty * a.price) - (b.approvedQty * b.price);
        break;
      }
      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }, [filteredOrders, sortField, sortOrder]);

  const totalApprovedSubtotal = useMemo(() => {
    return filteredOrders
      .filter(isOrderSuccessful)
      .reduce((sum: number, order: Order) => sum + (order.approvedQty * order.price), 0);
  }, [filteredOrders]);

  // Reset page when filters change, but with a debounce to prevent excessive updates
  useEffect(() => {
    // Use a timeout to debounce the state update
    const timer = setTimeout(() => {
      setCurrentPage(1);
    }, 100);
    
    // Clean up the timeout
    return () => clearTimeout(timer);
  }, [supplierFilter, statusFilter, startDate, endDate]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('desc');
    }
    // Reset to first page when sorting changes
    setCurrentPage(1);
  };

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Calculate pagination
  const totalPages = Math.ceil(sortedOrders.length / itemsPerPage);
  const paginatedOrders = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedOrders.slice(startIndex, endIndex);
  }, [sortedOrders, currentPage, itemsPerPage]);

  return (
    <div className="w-full max-w-[1400px] mx-auto px-4">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <span className="text-lg text-gray-600">
            Total Approved: £{formatNumber(totalApprovedSubtotal)}
          </span>
          <div className="flex-1">
            <DateRangePickerDialog
              trigger={
                <Button
                  disabled={isRetrieving || isRefreshing}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className={`h-4 w-4 ${isRetrieving || isRefreshing ? 'animate-spin' : ''}`} />
                  {isRetrieving ? 'Retrieving...' : isRefreshing ? 'Refreshing...' : 'Retrieve Orders'}
                </Button>
              }
              onDateRangeSelect={async (start: Date, end: Date) => {
                handleRetrieveOrders(start, end);
              }}
            />
            {retrieveError && (
              <div className="text-sm text-red-500 mt-2">{retrieveError}</div>
            )}
          </div>
        </div>
      </div>

      <div className="flex gap-4 mb-6 items-center">
        <div className="flex gap-2 items-center ">
          <span className="text-sm text-gray-600">From:</span>
          <Popover>
            <PopoverTrigger asChild  className="border border-gray-200">
              <Button
                                variant="outline"
                                
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {startDate ? format(startDate, "PPP") : "Pick a date"}
                              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50" align="start">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={setStartDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <span className="text-sm text-gray-600">To:</span>
          <Popover>
            <PopoverTrigger asChild className="border border-gray-200">
              <Button
                                variant="outline"
                                
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {endDate ? format(endDate, "PPP") : "Pick a date"}
                              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50" align="start">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={setEndDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <Select value={supplierFilter || "allSuppliers"} onValueChange={(value) => setSupplierFilter(value === "" ? "allSuppliers" : value)}>
          <SelectTrigger className="w-[180px] border-gray-200">
            <SelectValue placeholder="All Suppliers" />
          </SelectTrigger>
          <SelectContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
            <SelectItem value="allSuppliers">All Suppliers</SelectItem>
            {cleanedSuppliers.filter(supplier => supplier && supplier !== "N/A").map(supplier => (
              <SelectItem key={supplier} value={supplier}>
                {supplier}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
          <SelectTrigger className="w-[180px] border-gray-200">
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100">
        <table className="w-full text-sm text-left">
          <thead className="bg-gray-50 border-b border-gray-100">
            <tr>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Status</th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Supplier</th>
              <th
                scope="col"
                className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600"
                onClick={() => handleSort('dateTime')}
              >
                <div className="flex items-center">
                  <span>Date/Time</span>
                  {getSortIcon(sortField, 'dateTime', sortOrder)}
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Details</th>
              <th
                scope="col"
                className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600 text-right"
                onClick={() => handleSort('approvedQty')}
              >
                <div className="flex items-center justify-end">
                  <span>Quantity</span>
                  {getSortIcon(sortField, 'approvedQty', sortOrder)}
                </div>
              </th>
              <th
                scope="col"
                className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600 text-right"
                onClick={() => handleSort('price')}
              >
                <div className="flex items-center justify-end">
                  <span>Price</span>
                  {getSortIcon(sortField, 'price', sortOrder)}
                </div>
              </th>
              <th
                scope="col"
                className="group cursor-pointer px-6 py-4 text-xs font-semibold text-gray-600 text-right"
                onClick={() => handleSort('subTotal')}
              >
                <div className="flex items-center justify-end">
                  <span>Subtotal</span>
                  {getSortIcon(sortField, 'subTotal', sortOrder)}
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            {sortedOrders.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-6 py-8 text-center text-gray-500">
                  No orders found matching your filters
                </td>
              </tr>
            ) : (
              paginatedOrders.map((order) => {
                // Generate a stable key for each order that includes all relevant data
                const orderKey = `${order.orderNo || ''}-${order.pipCode || ''}-${order.dateTime || ''}-${order.approvedQty}-${order.price}`;
                return <OrderRow key={orderKey} order={order} />;
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {sortedOrders.length > 0 && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-4 rounded-lg">
          <div className="flex flex-1 items-center justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * itemsPerPage, sortedOrders.length)}
                </span> of{' '}
                <span className="font-medium">{sortedOrders.length}</span> results
              </p>
            </div>
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
                className="min-w-[70px] text-center"
              >
                First
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                disabled={currentPage === 1}
                className="min-w-[70px] text-center"
              >
                Previous
              </Button>
              <div className="flex items-center gap-1 px-2">
                <span className="text-sm text-gray-700 text-center min-w-[100px]">Page {currentPage} of {totalPages}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                disabled={currentPage === totalPages}
                className="min-w-[70px] text-center"
              >
                Next
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
                className="min-w-[70px] text-center"
              >
                Last
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

interface OrderRowProps {
  order: Order;
}

// Move this outside the component to avoid re-creation on each render
const isSuccessfulMemo = (order: Order) => {
  return isOrderSuccessful(order);
};

function OrderRow({ order }: OrderRowProps) {
  // For server-side rendering, we need to use the same values on both server and client
  // Use useMemo for values that depend on props but shouldn't change during hydration
  const successful = useMemo(() => isSuccessfulMemo(order), [order]);
  const supplierName = useMemo(() => order.supplier || '', [order]);
  const logoUrl = useMemo(() => getSupplierLogoUrl(supplierName), [supplierName]);
  
  // Don't use state for the date to avoid hydration mismatches
  // Instead, format it directly during render
  const formattedDate = order.dateTime || '';

  // Use a key that includes all the data that might change to ensure proper re-rendering
  const rowKey = useMemo(() =>
    `${order.orderNo || ''}-${order.pipCode || ''}-${order.dateTime || ''}-${order.approvedQty}-${order.price}`,
    [order]
  );

  return (
    <tr className="bg-white border-b hover:bg-gray-50 transition-colors">
      <td className="px-6 py-4">
        {successful ? (
          <CheckCircle className="w-5 h-5 text-green-500" />
        ) : (
          <XCircle className="w-5 h-5 text-red-500" />
        )}
      </td>
      <td className="px-6 py-4">
        <div className="relative w-12 h-12">
          {logoUrl ? (
            <Image
              src={logoUrl}
              alt={supplierName}
              fill
              sizes="48px"
              className="rounded-lg object-contain"
              quality={85}
              priority
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-lg text-gray-500 text-xs">
              {supplierName}
            </div>
          )}
        </div>
      </td>
      <td className="px-6 py-4 text-gray-600">{formattedDate}</td>
      <td className="px-6 py-4">
        <div className="space-y-1.5">
          <div className="font-medium text-gray-900">{order.description || 'N/A'}</div>
          <div className="text-xs text-gray-500">{order.orderNo || ''}</div>
          <div className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded inline-block">
            {order.pipCode || ''}
          </div>
        </div>
      </td>
      <td className="px-6 py-4 text-right text-gray-600">
        <span className="font-medium">{order.approvedQty}</span>
        <span className="text-gray-400 ml-1">({order.orderQty})</span>
      </td>
      <td className="px-6 py-4 text-right text-gray-600">£{order.price.toFixed(2)}</td>
      <td className="px-6 py-4 text-right font-medium text-gray-900">£{(order.approvedQty * order.price).toFixed(2)}</td>
    </tr>
  );
}

