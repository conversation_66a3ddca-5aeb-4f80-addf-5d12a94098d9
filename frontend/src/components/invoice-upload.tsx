import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Upload } from 'lucide-react';
import { invoicesApi } from '@/lib/api';

interface InvoiceUploadProps {
  onUploadComplete: () => void;
}

interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
}

export function InvoiceUpload({ onUploadComplete }: InvoiceUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);
    const fileList = Array.from(files);
    
    // Initialize progress for all files
    setUploadProgress(fileList.map(file => ({
      fileName: file.name,
      progress: 0,
      status: 'uploading' as const
    })));

    try {
      // Upload files sequentially
      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i];
        
        try {
          await invoicesApi.uploadInvoice(file, (progress) => {
            setUploadProgress(prev => prev.map((item, index) => 
              index === i ? { ...item, progress } : item
            ));
          });
          
          // Mark this file as completed
          setUploadProgress(prev => prev.map((item, index) => 
            index === i ? { ...item, status: 'completed', progress: 100 } : item
          ));
          
        } catch (error) {
          console.error(`Upload error for ${file.name}:`, error);
          setUploadProgress(prev => prev.map((item, index) =>
            index === i ? { ...item, status: 'error' } : item
          ));
          let message = 'Upload failed';
          if (error && typeof error === 'object' && 'message' in error) {
            message = (error as Error).message;
          }
          setErrorMessage(`Error uploading ${file.name}: ${message}`);
        }
      }

      console.log('All uploads completed');
      onUploadComplete();
    } finally {
      setIsUploading(false);
      if (event.target) {
        event.target.value = '';
      }
      // Clear progress after a delay
      setTimeout(() => {
                setUploadProgress([]);
              }, 3000);
    }
  };

  return (
    <div className="relative space-y-4">
      <input
        type="file"
        accept=".pdf"
        multiple
        onChange={handleFileChange}
        style={{ display: 'none' }}
        id="invoice-upload"
        disabled={isUploading}
      />
      <label htmlFor="invoice-upload">
        <Button
          variant="outline"
          disabled={isUploading}
          className={`cursor-pointer ${isUploading ? 'opacity-50' : ''}`}
          asChild
        >
          <span>
            <Upload className="w-4 h-4 mr-2" />
            {isUploading ? 'Uploading...' : 'Upload Invoices'}
          </span>
        </Button>
      </label>
      
      {/* Progress bars */}
      {uploadProgress.length > 0 && (
        <div className="space-y-2">
          {uploadProgress.map((item, index) => (
            <div key={index} className="space-y-1">
              <div className="text-sm font-medium text-gray-700 flex justify-between">
                <span>{item.fileName}</span>
                <span>{item.progress}%</span>
              </div>
              <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className={`h-full transition-all duration-300 rounded-full ${
                    item.status === 'error' 
                      ? 'bg-red-500' 
                      : item.status === 'completed'
                      ? 'bg-green-500'
                      : 'bg-blue-500'
                  }`}
                  style={{ width: `${item.progress}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}