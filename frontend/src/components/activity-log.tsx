"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from "date-fns";
import { CalendarIcon, User, Package, FileText, CreditCard, Filter } from "lucide-react";

export type ActivityType = "order" | "checklist" | "invoice" | "statement" | "credit-request" | "user";

export interface Activity {
  id: string;
  type: ActivityType;
  action: string;
  description: string;
  timestamp: Date;
  user: {
    id: string;
    name: string;
  };
  details?: {
    [key: string]: any;
  };
  entityId?: string;
  entityType?: string;
}

interface ActivityLogProps {
  activities: Activity[];
}

export function ActivityLog({ activities }: ActivityLogProps) {
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [typeFilter, setTypeFilter] = useState<ActivityType | "all">("all");
  const [userFilter, setUserFilter] = useState<string>("all");

  const uniqueUsers = Array.from(
    new Set(activities.map((activity) => activity.user.id))
  ).map((userId) => {
    const activity = activities.find((a) => a.user.id === userId);
    return {
      id: userId,
      name: activity?.user.name || "Unknown User",
    };
  });

  const filteredActivities = activities.filter((activity) => {
    // Filter by date range
    if (startDate && activity.timestamp < startDate) {
      return false;
    }
    if (endDate) {
      const endOfDay = new Date(endDate);
      endOfDay.setHours(23, 59, 59, 999);
      if (activity.timestamp > endOfDay) {
        return false;
      }
    }

    // Filter by type
    if (typeFilter !== "all" && activity.type !== typeFilter) {
      return false;
    }

    // Filter by user
    if (userFilter !== "all" && activity.user.id !== userFilter) {
      return false;
    }

    return true;
  });

  const getActivityIcon = (type: ActivityType) => {
    switch (type) {
      case "order":
        return <Package className="h-5 w-5 text-blue-500" />;
      case "checklist":
        return <FileText className="h-5 w-5 text-green-500" />;
      case "invoice":
        return <FileText className="h-5 w-5 text-yellow-500" />;
      case "statement":
        return <FileText className="h-5 w-5 text-purple-500" />;
      case "credit-request":
        return <CreditCard className="h-5 w-5 text-red-500" />;
      case "user":
        return <User className="h-5 w-5 text-gray-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className="w-full max-w-[1400px] mx-auto px-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">Activity Log</h2>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6 items-center">
        <div className="flex gap-2 items-center">
          <span className="text-sm text-gray-600">From:</span>
          <Popover>
            <PopoverTrigger asChild className="border border-gray-200">
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50" align="start">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={setStartDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <span className="text-sm text-gray-600">To:</span>
          <Popover>
            <PopoverTrigger asChild className="border border-gray-200">
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50" align="start">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={setEndDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-600">Filter by:</span>
          
          <Select value={typeFilter} onValueChange={(value: ActivityType | "all") => setTypeFilter(value)}>
            <SelectTrigger className="w-[150px] border-gray-200">
              <SelectValue placeholder="All Types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="order">Orders</SelectItem>
              <SelectItem value="checklist">Checklist</SelectItem>
              <SelectItem value="invoice">Invoices</SelectItem>
              <SelectItem value="statement">Statements</SelectItem>
              <SelectItem value="credit-request">Credit Requests</SelectItem>
              <SelectItem value="user">User Actions</SelectItem>
            </SelectContent>
          </Select>

          <Select value={userFilter} onValueChange={(value) => setUserFilter(value)}>
            <SelectTrigger className="w-[150px] border-gray-200">
              <SelectValue placeholder="All Users" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Users</SelectItem>
              {uniqueUsers.map((user) => (
                <SelectItem key={user.id} value={user.id}>
                  {user.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Activity Timeline */}
      <div className="bg-white shadow-sm rounded-xl border border-gray-100 p-6">
        {filteredActivities.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No activities found matching your filters
          </div>
        ) : (
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>

            <div className="space-y-8">
              {filteredActivities.map((activity) => (
                <div key={activity.id} className="relative pl-12">
                  {/* Timeline dot */}
                  <div className="absolute left-0 top-0 w-12 flex items-center justify-center">
                    <div className="bg-white p-1 rounded-full z-10">
                      {getActivityIcon(activity.type)}
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-base font-medium text-gray-900">
                          {activity.action}
                        </h3>
                        <p className="text-sm text-gray-600 mt-0.5">
                          {activity.description}
                        </p>
                      </div>
                      <div className="text-xs text-gray-500">
                        {format(activity.timestamp, "PPp")}
                      </div>
                    </div>

                    <div className="mt-2 flex items-center text-xs text-gray-500">
                      <User className="h-3 w-3 mr-1" />
                      <span>{activity.user.name}</span>
                    </div>

                    {activity.details && Object.keys(activity.details).length > 0 && (
                      <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                        <div className="font-medium text-gray-700 mb-1">Details:</div>
                        <div className="space-y-1">
                          {Object.entries(activity.details).map(([key, value]) => (
                            <div key={key} className="flex">
                              <span className="font-medium text-gray-600 mr-2">{key}:</span>
                              <span className="text-gray-600">{String(value)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}