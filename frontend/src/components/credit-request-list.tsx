"use client";

import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Mail, Filter, ChevronDown } from "lucide-react";
import { format } from "date-fns";
import { CreditRequest } from "@/types/credit-request";

interface CreditRequestListProps {
  requests: CreditRequest[];
  onResend: (request: CreditRequest) => Promise<void>;
  onViewDetails: (request: CreditRequest) => void;
}

interface RequestFilters {
  supplier?: string;
  status?: CreditRequest["status"];
  startDate?: Date;
  endDate?: Date;
}

export function CreditRequestList({ requests, onResend, onViewDetails }: CreditRequestListProps) {
  const [filters, setFilters] = useState<RequestFilters>({});
  const [expandedRequestId, setExpandedRequestId] = useState<string | null>(null);

  const suppliers = useMemo(() => 
    Array.from(new Set(requests.map(r => r.supplier))).sort(),
    [requests]
  );

  const filteredRequests = useMemo(() => 
    requests.filter(request => {
      if (filters.supplier && request.supplier !== filters.supplier) return false;
      if (filters.status && request.status !== filters.status) return false;
      if (filters.startDate && request.requestDate < filters.startDate) return false;
      if (filters.endDate && request.requestDate > filters.endDate) return false;
      return true;
    }),
    [requests, filters]
  );

  const getStatusColor = (status: CreditRequest["status"]) => {
    switch (status) {
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const totalValue = (request: CreditRequest) =>
    request.items.reduce((sum, item) => sum + item.originalTotal, 0);

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Credit Requests</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setFilters({})}
          >
            Clear Filters
          </Button>
        </div>

        <div className="grid grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Supplier
            </label>
            <Select
              value={filters.supplier}
              onValueChange={(value: string) => 
                setFilters(f => ({ ...f, supplier: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="All Suppliers" />
              </SelectTrigger>
              <SelectContent>
                {suppliers.map(supplier => (
                  <SelectItem key={supplier} value={supplier}>
                    {supplier}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <Select
              value={filters.status}
              onValueChange={(value: CreditRequest["status"]) => 
                setFilters(f => ({ ...f, status: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    {filters.startDate ? format(filters.startDate, "PPP") : "Start date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={filters.startDate}
                    onSelect={(date) => setFilters(f => ({ ...f, startDate: date }))}
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                To
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    {filters.endDate ? format(filters.endDate, "PPP") : "End date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={filters.endDate}
                    onSelect={(date) => setFilters(f => ({ ...f, endDate: date }))}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
      </div>

      {/* Requests List */}
      <div className="space-y-4">
        {filteredRequests.map((request) => (
          <div 
            key={request.id}
            className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden"
          >
            {/* Header */}
            <div 
              className="p-4 cursor-pointer hover:bg-gray-50"
              onClick={() => setExpandedRequestId(
                expandedRequestId === request.id ? null : request.id
              )}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <ChevronDown 
                    className={`h-5 w-5 transform transition-transform ${
                      expandedRequestId === request.id ? 'rotate-180' : ''
                    }`}
                  />
                  <div>
                    <div className="font-medium">{request.supplier}</div>
                    <div className="text-sm text-gray-500">
                      Order: {request.orderId} | Invoice: {request.invoiceId}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    getStatusColor(request.status)
                  }`}>
                    {request.status}
                  </span>
                  <div className="text-right">
                    <div className="font-medium">£{totalValue(request).toFixed(2)}</div>
                    <div className="text-sm text-gray-500">
                      {format(request.requestDate, "PPP")}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Expanded Content */}
            {expandedRequestId === request.id && (
              <div className="border-t border-gray-100 p-4">
                <div className="space-y-4">
                  {/* Items */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Items</h4>
                    <table className="w-full text-sm">
                      <thead className="text-xs text-gray-700 bg-gray-50">
                        <tr>
                          <th className="px-4 py-2 text-left">Product</th>
                          <th className="px-4 py-2 text-left">Reason</th>
                          <th className="px-4 py-2 text-right">Qty</th>
                          <th className="px-4 py-2 text-right">Unit Price</th>
                          <th className="px-4 py-2 text-right">Total</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-100">
                        {request.items.map((item, index) => (
                          <tr key={index}>
                            <td className="px-4 py-2">
                              <div>{item.productName}</div>
                              <div className="text-xs text-gray-500">{item.pipCode}</div>
                            </td>
                            <td className="px-4 py-2">{item.reason}</td>
                            <td className="px-4 py-2 text-right">{item.quantity}</td>
                            <td className="px-4 py-2 text-right">
                              £{item.originalUnitPrice.toFixed(2)}
                            </td>
                            <td className="px-4 py-2 text-right">
                              £{item.originalTotal.toFixed(2)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Email History */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Email History</h4>
                    <div className="space-y-2">
                      {request.emailHistory.map((email) => (
                        <div 
                          key={email.id}
                          className="text-sm bg-gray-50 p-2 rounded"
                        >
                          <div className="flex justify-between">
                            <span>
                              To: {email.to.join(", ")}
                            </span>
                            <span className="text-gray-500">
                              {format(email.sentDate, "PPp")}
                            </span>
                          </div>
                          <div className="text-gray-700">{email.subject}</div>
                          {email.response && (
                            <div className="mt-2 text-sm text-gray-600 border-l-2 border-gray-300 pl-2">
                              Response ({format(email.response.date, "PPp")}):
                              <br />
                              {email.response.content}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-4">
                    <Button
                      variant="outline"
                      onClick={() => onViewDetails(request)}
                    >
                      View Details
                    </Button>
                    {request.status !== 'approved' && (
                      <Button
                        onClick={() => onResend(request)}
                      >
                        <Mail className="h-4 w-4 mr-2" />
                        Resend Request
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}

        {filteredRequests.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No credit requests found matching your filters
          </div>
        )}
      </div>
    </div>
  );
}
