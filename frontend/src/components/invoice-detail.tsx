"use client";

import Image from "next/image";
import { Invoice } from "@/types/api";
import { InvoicePDFViewer } from "./invoice-pdf-viewer";
import { getSupplierLogoUrl } from "@/lib/csv-utils";

interface InvoiceDetailProps {
  invoice: Invoice;
}

export function InvoiceDetail({ invoice }: InvoiceDetailProps) {
  return (
    <div className="grid grid-cols-12 gap-6">
      <div className="col-span-7 space-y-6">
        {/* Header with Logo and Invoice Details */}
        <div className="flex justify-between items-start">
          <div>
            {(() => {
              const logo = getSupplierLogoUrl(invoice.VendorName);
              return logo ? (
                <Image
                  src={logo}
                  alt={`${invoice.VendorName} logo`}
                  width={120}
                  height={60}
                />
              ) : (
                <div className="text-lg font-semibold">
                  {invoice.VendorName}
                </div>
              );
            })()}
          </div>
          <div className="text-right space-y-2">
            {/* <div className="text-lg font-semibold">Invoice #{invoice.InvoiceId}</div> */}
            <div className="text-sm text-gray-600">
              <div>Date: {invoice.InvoiceDate}</div>
              <div>Due Date: {invoice.DueDate}</div>
            </div>
            <div className="text-lg font-bold mt-4">
              Total Due: £{invoice.InvoiceTotal.toFixed(2)}
            </div>
          </div>
        </div>

        {/* Line Items */}
        <div className="mt-8">
          <h3 className="font-semibold mb-4">Line Items</h3>
          <div className="relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100">
            <table className="w-full text-sm text-left">
              <thead className="bg-gray-50 border-b border-gray-100">
                <tr>
                  <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Description</th>
                  <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">PIP Code</th>
                  <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Pack Size</th>
                  <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600 text-right">Unit Price</th>
                  <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600 text-right">Quantity</th>
                  <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600 text-right">Amount</th>
                </tr>
              </thead>
              <tbody>
                {invoice.Items.map((item) => (
                  <tr key={item.itemId} className="bg-white border-b hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 text-sm text-gray-900">{item.Description}</td>
                    <td className="px-6 py-4 text-sm text-gray-600">{item.pipCode}</td>
                    <td className="px-6 py-4 text-sm text-gray-600">{item.packSize}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 text-right">£{item.unitPrice.toFixed(2)}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 text-right">{item.Quantity}</td>
                    <td className="px-6 py-4 text-sm text-gray-900 text-right font-medium">£{item.Amount.toFixed(2)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="col-span-5">
        <InvoicePDFViewer invoiceId={invoice.id} />
      </div>
    </div>
  );
}