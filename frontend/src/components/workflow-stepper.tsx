"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, ChevronRight, HelpCircle } from "lucide-react";
import Link from "next/link";

export type WorkflowStep = {
  id: string;
  title: string;
  description: string;
  status: "not-started" | "in-progress" | "completed" | "skipped";
  link: string;
  helpText?: string;
};

interface WorkflowStepperProps {
  steps: WorkflowStep[];
  currentStepId: string;
  onStepComplete: (stepId: string) => Promise<void>;
  onStepSkip: (stepId: string) => Promise<void>;
}

export function WorkflowStepper({
  steps,
  currentStepId,
  onStepComplete,
  onStepSkip,
}: WorkflowStepperProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [showHelp, setShowHelp] = useState<string | null>(null);

  const currentStepIndex = steps.findIndex((step) => step.id === currentStepId);
  const currentStep = steps[currentStepIndex];
  const nextStep = steps[currentStepIndex + 1];

  const handleComplete = async () => {
    setIsProcessing(true);
    try {
      await onStepComplete(currentStepId);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSkip = async () => {
    setIsProcessing(true);
    try {
      await onStepSkip(currentStepId);
    } finally {
      setIsProcessing(false);
    }
  };

  const getStepStatusColor = (status: WorkflowStep["status"]) => {
    switch (status) {
      case "completed":
        return "bg-green-500";
      case "in-progress":
        return "bg-blue-500";
      case "skipped":
        return "bg-yellow-500";
      default:
        return "bg-gray-300";
    }
  };

  return (
    <div className="w-full max-w-[1400px] mx-auto px-4">
      {/* Stepper Header */}
      <div className="flex items-center mb-8 overflow-x-auto pb-2">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className="flex flex-col items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-white ${getStepStatusColor(
                  step.status
                )}`}
              >
                {step.status === "completed" ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  index + 1
                )}
              </div>
              <div
                className={`text-xs mt-1 ${
                  step.id === currentStepId
                    ? "font-semibold text-gray-900"
                    : "text-gray-500"
                }`}
              >
                {step.title}
              </div>
            </div>
            {index < steps.length - 1 && (
              <div className="w-12 h-0.5 mx-1 bg-gray-200"></div>
            )}
          </div>
        ))}
      </div>

      {/* Current Step */}
      <div className="bg-white shadow-sm rounded-xl border border-gray-100 p-6 mb-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {currentStep.title}
            </h2>
            <p className="text-gray-600 mt-1">{currentStep.description}</p>
          </div>
          {currentStep.helpText && (
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setShowHelp(showHelp === currentStep.id ? null : currentStep.id)
              }
              className="flex items-center gap-1"
            >
              <HelpCircle className="h-4 w-4" />
              Help
            </Button>
          )}
        </div>

        {showHelp === currentStep.id && currentStep.helpText && (
          <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-4 text-sm text-blue-800">
            {currentStep.helpText}
          </div>
        )}

        <div className="flex justify-between items-center">
          <div className="flex gap-2">
            <Button asChild>
              <Link href={currentStep.link}>Go to {currentStep.title}</Link>
            </Button>
            {currentStep.status === "in-progress" && (
              <Button
                variant="outline"
                onClick={handleComplete}
                disabled={isProcessing}
              >
                {isProcessing ? "Processing..." : "Mark as Complete"}
              </Button>
            )}
          </div>
          {currentStep.status === "in-progress" && (
            <Button
              variant="ghost"
              onClick={handleSkip}
              disabled={isProcessing}
              className="text-gray-500"
            >
              Skip this step
            </Button>
          )}
        </div>
      </div>

      {/* Next Step Preview */}
      {nextStep && (
        <div className="bg-gray-50 shadow-sm rounded-xl border border-gray-100 p-6">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Next: {nextStep.title}</h3>
              <p className="text-gray-600 text-sm mt-1">{nextStep.description}</p>
            </div>
            {nextStep.status === "not-started" && currentStep.status === "completed" && (
              <Button asChild>
                <Link href={nextStep.link} className="flex items-center gap-1">
                  Continue <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}