"use client";

import { useState, useEffect } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

interface InvoicePDFViewerProps {
  invoiceId: string;
}

export function InvoicePDFViewer({ invoiceId }: InvoicePDFViewerProps) {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    pdfjs.GlobalWorkerOptions.workerSrc = "/pdf.worker.min.mjs";
  }, []);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setLoading(false);
  };

  const onDocumentLoadError = (error: Error) => {
    setError("Failed to load PDF. Please try again later.");
    setLoading(false);
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="mb-4 flex justify-between items-center">
        <h3 className="font-semibold">Invoice PDF</h3>
        <a
          href={`/api/invoices/${invoiceId}/pdf`}
          download={`invoice-${invoiceId}.pdf`}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Download PDF
        </a>
      </div>
      <div className="border rounded-lg h-[600px] bg-gray-50 overflow-auto">
        {loading && (
          <div className="h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )}
        {error && (
          <div className="h-full flex items-center justify-center text-red-600">
            {error}
          </div>
        )}
        <Document
          file={`/api/invoices/${invoiceId}/pdf`}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          loading={null}
        >
          <Page
            pageNumber={pageNumber}
            width={undefined}
            height={560}
            className="flex justify-center"
            loading={null}
          />
        </Document>
        {numPages && numPages > 1 && (
          <div className="flex justify-center items-center gap-4 py-2 border-t">
            <button
              onClick={() => setPageNumber(p => Math.max(1, p - 1))}
              disabled={pageNumber <= 1}
              className="px-2 py-1 text-sm rounded hover:bg-gray-100 disabled:opacity-50"
            >
              Previous
            </button>
            <span className="text-sm">
              Page {pageNumber} of {numPages}
            </span>
            <button
              onClick={() => setPageNumber(p => Math.min(numPages, p + 1))}
              disabled={pageNumber >= numPages}
              className="px-2 py-1 text-sm rounded hover:bg-gray-100 disabled:opacity-50"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
}