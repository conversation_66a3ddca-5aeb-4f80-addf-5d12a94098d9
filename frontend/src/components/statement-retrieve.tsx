import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Mail, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { processStatementsFromEmail } from '@/lib/api/statement';

interface StatementRetrieveProps {
  onRetrieveComplete: () => void;
}

export function StatementRetrieve({ onRetrieveComplete }: StatementRetrieveProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  const handleProcessEmail = async () => {
    setIsProcessing(true);
    setError(null);
    setResult(null);
    try {
      const response = await processStatementsFromEmail();
      console.log('Email processing response:', response); // Debug log
      setResult(response);
      
      if (response.success) {
        onRetrieveComplete();
      }
    } catch (error: any) {
      console.error('Error processing email for statements:', error);
      const errorMessage = error?.response?.data?.error || error.message || 'Failed to process emails';
      const suggestion = error?.response?.data?.details?.suggestion || '';
      setError(suggestion ? `${errorMessage}. ${suggestion}` : errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="relative space-y-2">
      <Button
        variant="secondary"
        onClick={handleProcessEmail}
        disabled={isProcessing}
        className={isProcessing ? 'opacity-50' : ''}
      >
        <Mail className="w-4 h-4 mr-2" />
        {isProcessing ? 'Processing...' : 'Fetch Statements from Email'}
      </Button>
      
      {error && (
        <div className="flex items-center text-sm text-red-500 mt-2">
          <AlertCircle className="w-4 h-4 mr-1" />
          <span>{error}</span>
        </div>
      )}

      {!error && result && (
        <div className={`flex items-center text-sm mt-2 ${result.success ? 'text-green-500' : 'text-blue-500'}`}>
          {result.success ? (
            <CheckCircle className="w-4 h-4 mr-1" />
          ) : (
            <Info className="w-4 h-4 mr-1" />
          )}
          <span>{result.message}</span>
        </div>
      )}
    </div>
  );
}