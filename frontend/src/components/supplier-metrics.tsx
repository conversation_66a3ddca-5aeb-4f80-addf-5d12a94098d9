import Image from 'next/image';
import { getSupplierLogoUrl, type Order } from '@/lib/csv-utils';

interface SupplierMetric {
  approvedOrders: number;
  totalSpend: number;
}

interface SupplierMetricsProps {
  orders: Order[];
}

export function SupplierMetrics({ orders }: SupplierMetricsProps) {
  // Calculate metrics for each supplier
  const supplierMetrics: Record<string, SupplierMetric> = {};

  orders.forEach(order => {
    const supplier = order.supplier;
    if (!supplier) return;

    if (!supplierMetrics[supplier]) {
      supplierMetrics[supplier] = { approvedOrders: 0, totalSpend: 0 };
    }

    supplierMetrics[supplier].approvedOrders += order.approvedQty;
    supplierMetrics[supplier].totalSpend += order.approvedQty * order.price;
  });

  const supplierKeys = Object.keys(supplierMetrics);
  const numBoxes = Math.min(supplierKeys.length, 10);

  return (
    <div className="w-full max-w-[1400px] mx-auto">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {supplierKeys.slice(0, numBoxes).map((supplier) => (
          <SupplierBox
            key={supplier}
            supplier={supplier}
            metrics={supplierMetrics[supplier]}
          />
        ))}
      </div>
    </div>
  );
}

interface SupplierBoxProps {
  supplier: string;
  metrics: SupplierMetric;
}

function SupplierBox({ supplier, metrics }: SupplierBoxProps) {
  const logoUrl = getSupplierLogoUrl(supplier);

  return (
    <div className="w-full p-6 bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md hover:border-gray-200 transition-all">
      <div className="flex flex-col items-center">
        <div className="relative w-24 h-24 mb-4">
          {logoUrl ? (
            <Image
              src={logoUrl}
              alt={supplier}
              fill
              sizes="96px"
              className="object-contain"
              quality={85}
              priority
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg text-gray-500 font-medium">
              {supplier}
            </div>
          )}
        </div>
        <div className="w-full space-y-4">
          <div className="flex flex-col items-center">
            <span className="text-sm font-medium text-gray-600 mb-1">Approved Orders</span>
            <span className="text-2xl font-semibold text-gray-900">{metrics.approvedOrders}</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-sm font-medium text-gray-600 mb-1">Total Spend</span>
            <span className="text-2xl font-semibold text-gray-900">£{metrics.totalSpend.toFixed(2)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}