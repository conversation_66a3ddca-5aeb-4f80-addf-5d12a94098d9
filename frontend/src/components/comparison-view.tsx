"use client";

import { ComparisonResult } from "@/types/comparison";

interface ComparisonViewProps {
  orderId: string;
  invoiceId: string;
  comparison: ComparisonResult;
}

export function ComparisonView({ orderId, invoiceId, comparison }: ComparisonViewProps) {
  return (
    <div className="w-full max-w-[1400px] mx-auto px-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">
          Order vs Invoice Comparison
        </h2>
        <div className="text-sm text-gray-600">
          <span>Order: {orderId}</span>
          <span className="mx-2">|</span>
          <span>Invoice: {invoiceId}</span>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        {/* Status Header */}
        <div className={`px-6 py-4 border-b border-gray-100 ${
          comparison.matchStatus === 'exact' ? 'bg-green-50' :
          comparison.matchStatus === 'partial' ? 'bg-yellow-50' :
          'bg-red-50'
        }`}>
          <div className="flex justify-between items-center">
            <div className="text-sm font-medium">
              {comparison.matchStatus === 'exact' ? 'Exact Match' :
               comparison.matchStatus === 'partial' ? 'Partial Match' :
               'Mismatch'}
            </div>
            <div className="text-sm text-gray-600">
              {comparison.discrepancies.length} discrepancies found
            </div>
          </div>
        </div>

        {/* Discrepancies List */}
        <div className="divide-y divide-gray-100">
          {comparison.discrepancies.map((discrepancy, index) => (
            <div key={index} className="px-6 py-4 hover:bg-gray-50">
              <div className="mb-2 font-medium text-gray-900">
                {discrepancy.field}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-xs text-gray-500 mb-1">Order Value</div>
                  <div className="text-sm px-2 py-1 rounded bg-red-50 text-red-700">
                    {discrepancy.orderValue}
                  </div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 mb-1">Invoice Value</div>
                  <div className="text-sm px-2 py-1 rounded bg-red-50 text-red-700">
                    {discrepancy.invoiceValue}
                  </div>
                </div>
              </div>
              <div className="mt-2 space-y-2">
                <div className="text-sm text-red-600 flex items-center gap-2">
                  <span className="inline-flex items-center justify-center rounded-full bg-red-100 px-2 py-1 text-xs">
                    {typeof discrepancy.difference === 'number' 
                      ? discrepancy.difference > 0 
                        ? `+${discrepancy.difference}` 
                        : discrepancy.difference
                      : discrepancy.difference}
                  </span>
                  <span>difference found</span>
                </div>
                <div className="text-sm text-gray-500 italic">
                  {discrepancy.explanation}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
