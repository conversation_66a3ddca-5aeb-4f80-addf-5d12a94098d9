import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";

interface DateRangePickerDialogProps {
  trigger?: React.ReactNode;
  onDateRangeSelect: (startDate: Date, endDate: Date) => void;
}

export function DateRangePickerDialog({ trigger, onDateRangeSelect }: DateRangePickerDialogProps) {
  const [open, setOpen] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  });

  const handleConfirm = () => {
    if (dateRange?.from && dateRange?.to) {
      onDateRangeSelect(dateRange.from, dateRange.to);
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || <Button>Select Date Range</Button>}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] p-0">
        <DialogHeader className="px-4 pt-4">
          <DialogTitle>Select Date Range</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-4 p-4">
          <div className="border rounded-md">
            <Calendar
              mode="range"
              selected={dateRange}
              onSelect={setDateRange}
              numberOfMonths={2}
              className="rounded-md"
              defaultMonth={dateRange?.from}
              showOutsideDays={false}
            />
          </div>
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              {dateRange?.from ? (
                dateRange.to ? (
                  <>
                    From {format(dateRange.from, "dd/MM/yyyy")} to {format(dateRange.to, "dd/MM/yyyy")}
                  </>
                ) : (
                  <>Selected {format(dateRange.from, "dd/MM/yyyy")}</>
                )
              ) : (
                <span>Select a date range</span>
              )}
            </div>
            <Button
              onClick={handleConfirm}
              disabled={!dateRange?.from || !dateRange?.to}
            >
              Confirm
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
