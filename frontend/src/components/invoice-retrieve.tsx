import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Mail, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { invoicesApi } from '@/lib/api';
import type { EmailProcessingResult } from '@/types/api';

interface InvoiceRetrieveProps {
  onRetrieveComplete: () => void;
}

export function InvoiceRetrieve({ onRetrieveComplete }: InvoiceRetrieveProps) {
  const [isProcessingEmail, setIsProcessingEmail] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<EmailProcessingResult | null>(null);

  const handleProcessEmail = async () => {
    setIsProcessingEmail(true);
    setError(null);
    setResult(null);
    try {
      const response = await invoicesApi.processEmail();
      console.log('Email processing response:', response); // Debug log
      setResult(response);
      
      const processedAttachments = response.details?.reduce((count, detail) => {
        if (detail.status === 'processed' && detail.attachments) {
          return count + detail.attachments.filter(att => att.status === 'success').length;
        }
        return count;
      }, 0) || 0;

      if (processedAttachments > 0) {
        onRetrieveComplete();
      }
    } catch (error: any) {
      console.error('Error processing email:', error);
      const errorMessage = error?.response?.data?.error || error.message || 'Failed to process emails';
      const suggestion = error?.response?.data?.details?.suggestion || '';
      setError(suggestion ? `${errorMessage}. ${suggestion}` : errorMessage);
    } finally {
      setIsProcessingEmail(false);
    }
  };

  const getResultMessage = () => {
    if (!result) return null;

    if (result.message?.includes('No emails found')) {
      return {
        icon: <Info className="w-4 h-4 mr-1 text-blue-500" />,
        message: 'No new emails found in the last 24 hours',
        className: 'text-blue-500'
      };
    }

    const processedEmails = result.details?.filter(d => d.status === 'processed').length || 0;
    const skippedEmails = result.details?.filter(d => d.status === 'skipped').length || 0;
    const successfulAttachments = result.details?.reduce((count, detail) => {
      if (detail.status === 'processed' && detail.attachments) {
        return count + detail.attachments.filter(att => att.status === 'success').length;
      }
      return count;
    }, 0) || 0;

    if (processedEmails === 0 && skippedEmails > 0) {
      return {
        icon: <Info className="w-4 h-4 mr-1 text-blue-500" />,
        message: `No new files to process (${skippedEmails} already processed)`,
        className: 'text-blue-500'
      };
    }

    if (successfulAttachments === 0) {
      return {
        icon: <Info className="w-4 h-4 mr-1 text-blue-500" />,
        message: 'No new invoices found in emails',
        className: 'text-blue-500'
      };
    }

    return {
      icon: <CheckCircle className="w-4 h-4 mr-1 text-green-500" />,
      message: `Processed ${successfulAttachments} file${successfulAttachments !== 1 ? 's' : ''} from ${processedEmails} email${processedEmails !== 1 ? 's' : ''}`,
      className: 'text-green-500'
    };
  };

  return (
    <div className="relative space-y-2">
      <Button
        variant="secondary"
        onClick={handleProcessEmail}
        disabled={isProcessingEmail}
        className={isProcessingEmail ? 'opacity-50' : ''}
      >
        <Mail className="w-4 h-4 mr-2" />
        {isProcessingEmail ? 'Processing...' : 'Process Email Invoices'}
      </Button>
      
      {error && (
        <div className="flex items-center text-sm text-red-500 mt-2">
          <AlertCircle className="w-4 h-4 mr-1" />
          <span>{error}</span>
        </div>
      )}

      {!error && result && (
        <div className={`flex items-center text-sm mt-2 ${getResultMessage()?.className}`}>
          {getResultMessage()?.icon}
          <span>{getResultMessage()?.message}</span>
        </div>
      )}
    </div>
  );
}