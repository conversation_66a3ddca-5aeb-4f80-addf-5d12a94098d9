"use client";

import { useState, useMemo, useEffect } from "react";
import { ArrowUpDown, Calendar as CalendarIcon, CheckCircle, XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { ConfirmationModal } from "@/components/ui/confirmation-modal";
import { useToast } from "@/components/ui/toast";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from "date-fns";
import { type ChecklistItem } from "@/types/checklist";
import { checklistApi } from "@/lib/api";

interface ChecklistTableProps {
  items: ChecklistItem[];
}

type SortField = "dateChecked" | "expectedQuantity" | "receivedQuantity";
type SortOrder = "asc" | "desc";
type StatusFilter = "all" | "pending" | "received" | "incorrect" | "missing";

const getSortIcon = (currentSortField: SortField, field: SortField, currentSortOrder: SortOrder) => {
  if (currentSortField !== field) return null;
  return (
    <ArrowUpDown
      className="ml-1 h-4 w-4 text-gray-900"
      style={{
        transform: currentSortOrder === "asc" ? "rotate(180deg)" : "none",
      }}
    />
  );
};

interface UndoState {
  itemId: string;
  previousStatus: ChecklistItem["status"];
}

export function ChecklistTable({ items }: ChecklistTableProps) {
  const { showToast, ToastComponent } = useToast();
  const [undoStack, setUndoStack] = useState<UndoState[]>([]);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("all");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [sortField, setSortField] = useState<SortField>("dateChecked");
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("desc");
    }
    setCurrentPage(1);
  };

  const filteredItems = useMemo(() => {
    return items.filter((item) => {
      const matchesStatus = statusFilter === "all" || item.status === statusFilter;
      const matchesDate = (() => {
        if (!startDate || !endDate || !item.dateChecked) return true;
        const itemDate = new Date(item.dateChecked);
        const start = new Date(startDate);
        const end = new Date(endDate);
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);
        return itemDate >= start && itemDate <= end;
      })();
      const matchesSearch = searchTerm
        ? item.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.pipCode.toLowerCase().includes(searchTerm.toLowerCase())
        : true;

      return matchesStatus && matchesDate && matchesSearch;
    });
  }, [items, statusFilter, startDate, endDate, searchTerm]);

  const sortedItems = useMemo(() => {
    return [...filteredItems].sort((a, b) => {
      let comparison = 0;
      switch (sortField) {
        case "dateChecked":
          const aDate = a.dateChecked ? new Date(a.dateChecked) : null;
          const bDate = b.dateChecked ? new Date(b.dateChecked) : null;
          comparison = (aDate?.getTime() ?? 0) - (bDate?.getTime() ?? 0);
          break;
        case "expectedQuantity":
          comparison = a.expectedQuantity - b.expectedQuantity;
          break;
        case "receivedQuantity":
          comparison = (a.receivedQuantity || 0) - (b.receivedQuantity || 0);
          break;
      }
      return sortOrder === "desc" ? -comparison : comparison;
    });
  }, [filteredItems, sortField, sortOrder]);

  // Pagination
  const totalPages = Math.ceil(sortedItems.length / itemsPerPage);
  const paginatedItems = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedItems.slice(startIndex, endIndex);
  }, [sortedItems, currentPage, itemsPerPage]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [statusFilter, startDate, endDate, searchTerm]);

  // Modal states
  const [statusChangeModal, setStatusChangeModal] = useState<{
    isOpen: boolean;
    itemId: string;
    newStatus: ChecklistItem["status"];
  }>({
    isOpen: false,
    itemId: "",
    newStatus: "pending"
  });

  const [bulkActionModal, setBulkActionModal] = useState<{
    isOpen: boolean;
    newStatus: ChecklistItem["status"];
  }>({
    isOpen: false,
    newStatus: "pending"
  });

  const handleStatusChange = async (itemId: string, newStatus: ChecklistItem["status"]) => {
    setStatusChangeModal({
      isOpen: true,
      itemId,
      newStatus
    });
  };

  const pushToUndoStack = (itemId: string, previousStatus: ChecklistItem["status"]) => {
    setUndoStack((stack) => [...stack, { itemId, previousStatus }]);
  };

  const confirmStatusChange = async () => {
    try {
      const itemId = statusChangeModal.itemId;
      const item = items.find((i) => i.id === itemId);
      if (!item) return;

      // Store the previous state for undo
      pushToUndoStack(itemId, item.status);

      await checklistApi.updateItem(itemId, { 
        status: statusChangeModal.newStatus 
      });

      showToast(
        `Item status updated to ${statusChangeModal.newStatus}`,
        "success",
        async () => {
          // Undo the change
          const previousState = undoStack[undoStack.length - 1];
          if (previousState) {
            await checklistApi.updateItem(itemId, {
              status: previousState.previousStatus,
            });
            setUndoStack((stack) => stack.slice(0, -1));
            showToast("Status change undone", "info");
          }
        }
      );
    } catch (error) {
      console.error("Failed to update status:", error);
      showToast("Failed to update status", "error");
    }
  };

  const handleBulkStatusChange = async (newStatus: ChecklistItem["status"]) => {
    setBulkActionModal({
      isOpen: true,
      newStatus
    });
  };

  const confirmBulkStatusChange = async () => {
    try {
      const selectedItemsList = Array.from(selectedItems);
      const itemsToUpdate = selectedItemsList.map((id) => {
        const item = items.find((i) => i.id === id);
        if (item) {
          pushToUndoStack(id, item.status);
        }
        return {
          id,
          data: { status: bulkActionModal.newStatus },
        };
      });

      await checklistApi.bulkUpdate(itemsToUpdate);
      setSelectedItems(new Set());

      showToast(
        `Updated ${selectedItemsList.length} items to ${bulkActionModal.newStatus}`,
        "success",
        async () => {
          // Undo all changes in reverse order
          const latestChanges = undoStack.slice(-selectedItemsList.length);
          const undoUpdates = latestChanges.map((change) => ({
            id: change.itemId,
            data: { status: change.previousStatus },
          }));

          await checklistApi.bulkUpdate(undoUpdates);
          setUndoStack((stack) => stack.slice(0, -selectedItemsList.length));
          showToast("Bulk status change undone", "info");
        }
      );
    } catch (error) {
      console.error("Failed to update items:", error);
      showToast("Failed to update items", "error");
    }
  };

  return (
    <div className="w-full max-w-[1400px] mx-auto px-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Checklist Items</h1>
        {selectedItems.size > 0 && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkStatusChange("received")}
            >
              Mark Selected as Received
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedItems(new Set())}
            >
              Clear Selection
            </Button>
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="flex gap-4 mb-6 items-center">
        <div className="flex gap-2 items-center">
          <span className="text-sm text-gray-600">From:</span>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={setStartDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <span className="text-sm text-gray-600">To:</span>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={setEndDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <Select value={statusFilter} onValueChange={(value: StatusFilter) => setStatusFilter(value)}>
          <SelectTrigger className="w-[180px] border-gray-200">
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="received">Received</SelectItem>
            <SelectItem value="incorrect">Incorrect</SelectItem>
            <SelectItem value="missing">Missing</SelectItem>
          </SelectContent>
        </Select>

        <input
          type="text"
          placeholder="Search items..."
          className="px-3 py-2 border border-gray-200 rounded-md"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Table */}
      <div className="relative overflow-x-auto bg-white shadow-sm rounded-xl border border-gray-100">
        <table className="w-full text-sm text-left">
          <thead className="bg-gray-50 border-b border-gray-100">
            <tr>
              <th scope="col" className="px-6 py-4">
                <input
                  type="checkbox"
                  checked={selectedItems.size === paginatedItems.length}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedItems(new Set(paginatedItems.map((item) => item.id)));
                    } else {
                      setSelectedItems(new Set());
                    }
                  }}
                  className="rounded border-gray-300"
                />
              </th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Status</th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Product</th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">PIP Code</th>
              <th
                scope="col"
                className="px-6 py-4 text-xs font-semibold text-gray-600 text-right cursor-pointer"
                onClick={() => handleSort("expectedQuantity")}
              >
                <div className="flex items-center justify-end">
                  <span>Expected Qty</span>
                  {getSortIcon(sortField, "expectedQuantity", sortOrder)}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-4 text-xs font-semibold text-gray-600 text-right cursor-pointer"
                onClick={() => handleSort("receivedQuantity")}
              >
                <div className="flex items-center justify-end">
                  <span>Received Qty</span>
                  {getSortIcon(sortField, "receivedQuantity", sortOrder)}
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-xs font-semibold text-gray-600">Notes</th>
            </tr>
          </thead>
          <tbody>
            {paginatedItems.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-6 py-8 text-center text-gray-500">
                  No items found matching your filters
                </td>
              </tr>
            ) : (
              paginatedItems.map((item) => (
                <tr key={item.id} className="bg-white border-b hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedItems.has(item.id)}
                      onChange={(e) => {
                        const newSelected = new Set(selectedItems);
                        if (e.target.checked) {
                          newSelected.add(item.id);
                        } else {
                          newSelected.delete(item.id);
                        }
                        setSelectedItems(newSelected);
                      }}
                      className="rounded border-gray-300"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <Select
                      value={item.status}
                      onValueChange={(value: ChecklistItem["status"]) =>
                        handleStatusChange(item.id, value)
                      }
                    >
                      <SelectTrigger className="w-[120px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="received">Received</SelectItem>
                        <SelectItem value="incorrect">Incorrect</SelectItem>
                        <SelectItem value="missing">Missing</SelectItem>
                      </SelectContent>
                    </Select>
                  </td>
                  <td className="px-6 py-4 text-gray-900">{item.productName}</td>
                  <td className="px-6 py-4">
                    <div className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded inline-block">
                      {item.pipCode}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-right text-gray-600">
                    {item.expectedQuantity}
                  </td>
                  <td className="px-6 py-4 text-right text-gray-600">
                    {item.receivedQuantity || "-"}
                  </td>
                  <td className="px-6 py-4 text-gray-600">{item.notes || "-"}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {sortedItems.length > 0 && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-4 rounded-lg">
          <div className="flex flex-1 items-center justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to{" "}
                <span className="font-medium">
                  {Math.min(currentPage * itemsPerPage, sortedItems.length)}
                </span> of{" "}
                <span className="font-medium">{sortedItems.length}</span> results
              </p>
            </div>
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
                className="min-w-[70px] text-center"
              >
                First
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                disabled={currentPage === 1}
                className="min-w-[70px] text-center"
              >
                Previous
              </Button>
              <div className="flex items-center gap-1 px-2">
                <span className="text-sm text-gray-700 text-center min-w-[100px]">
                  Page {currentPage} of {totalPages}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                disabled={currentPage === totalPages}
                className="min-w-[70px] text-center"
              >
                Next
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
                className="min-w-[70px] text-center"
              >
                Last
              </Button>
            </div>
          </div>
        </div>
      )}
      {/* Confirmation Modals */}
      <ConfirmationModal
        isOpen={statusChangeModal.isOpen}
        onClose={() => setStatusChangeModal({ ...statusChangeModal, isOpen: false })}
        onConfirm={confirmStatusChange}
        title="Update Item Status"
        message={`Are you sure you want to mark this item as ${statusChangeModal.newStatus}?`}
        confirmText="Update Status"
      />

      <ConfirmationModal
        isOpen={bulkActionModal.isOpen}
        onClose={() => setBulkActionModal({ ...bulkActionModal, isOpen: false })}
        onConfirm={confirmBulkStatusChange}
        title="Update Multiple Items"
        message={`Are you sure you want to mark ${selectedItems.size} items as ${bulkActionModal.newStatus}?`}
        confirmText="Update All"
      />
      {ToastComponent}
    </div>
  );
}
