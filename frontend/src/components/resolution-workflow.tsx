"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Resolution, ResolutionComment } from "@/types/resolution";
import { ComparisonResult } from "@/types/comparison";
import { format } from "date-fns";

interface ResolutionWorkflowProps {
  discrepancy: ComparisonResult;
  resolution: Resolution;
  onUpdate: (resolution: Partial<Resolution>) => Promise<void>;
  onComment: (comment: Omit<ResolutionComment, "id" | "createdAt">) => Promise<void>;
}

export function ResolutionWorkflow({ 
  discrepancy, 
  resolution, 
  onUpdate,
  onComment 
}: ResolutionWorkflowProps) {
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleStatusChange = async (newStatus: Resolution["status"]) => {
    try {
      await onUpdate({ status: newStatus });
      await onComment({
        content: `Status changed to ${newStatus}`,
        createdBy: "Current User", // TODO: Get from auth context
        type: "status_change",
        metadata: {
          oldStatus: resolution.status,
          newStatus: newStatus
        }
      });
    } catch (error) {
      console.error("Failed to update status:", error);
    }
  };

  const handleCommentSubmit = async () => {
    if (!comment.trim()) return;
    setIsSubmitting(true);
    try {
      await onComment({
        content: comment,
        createdBy: "Current User", // TODO: Get from auth context
        type: "note"
      });
      setComment("");
    } catch (error) {
      console.error("Failed to add comment:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Status Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Resolution Status</h3>
        <div className="flex items-center gap-4">
          <Select
            value={resolution.status}
            onValueChange={(value: Resolution["status"]) => handleStatusChange(value)}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="escalated">Escalated</SelectItem>
            </SelectContent>
          </Select>
          {resolution.status === "resolved" && (
            <span className="text-green-600 text-sm">
              Resolved on {resolution.resolvedAt ? format(resolution.resolvedAt, "PPP") : "Unknown"}
            </span>
          )}
        </div>
      </div>

      {/* Comments Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-medium mb-4">Resolution Comments</h3>
        
        {/* Comments List */}
        <div className="space-y-4 mb-6">
          {resolution.comments.map((comment) => (
            <div
              key={comment.id}
              className={`p-3 rounded-lg ${
                comment.type === "system" ? "bg-gray-50" :
                comment.type === "status_change" ? "bg-blue-50" :
                "bg-white border border-gray-100"
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <span className="font-medium">{comment.createdBy}</span>
                <span className="text-sm text-gray-500">
                  {format(comment.createdAt, "PPp")}
                </span>
              </div>
              <p className="text-sm text-gray-700">{comment.content}</p>
              {comment.metadata && (
                <div className="mt-2 text-xs text-gray-500">
                  {Object.entries(comment.metadata).map(([key, value]) => (
                    <div key={key}>{`${key}: ${value}`}</div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Add Comment */}
        <div className="space-y-2">
          <textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Add a comment..."
            className="w-full min-h-[100px] p-3 border border-gray-200 rounded-lg"
          />
          <div className="flex justify-end">
            <Button
              onClick={handleCommentSubmit}
              disabled={!comment.trim() || isSubmitting}
            >
              {isSubmitting ? "Adding..." : "Add Comment"}
            </Button>
          </div>
        </div>
      </div>

      {/* Resolution Details */}
      {resolution.status === "resolved" && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium mb-4">Resolution Details</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Resolution Type</label>
              <Select
                value={resolution.resolutionType}
                onValueChange={(value: NonNullable<Resolution["resolutionType"]>) => 
                  onUpdate({ resolutionType: value as Resolution["resolutionType"] })
                }
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="credit_note">Credit Note</SelectItem>
                  <SelectItem value="reorder">Reorder</SelectItem>
                  <SelectItem value="write_off">Write Off</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Details</label>
              <textarea
                value={resolution.resolutionDetails || ""}
                onChange={(e) => onUpdate({ resolutionDetails: e.target.value })}
                className="w-full min-h-[100px] p-3 border border-gray-200 rounded-lg"
                placeholder="Enter resolution details..."
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
