import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
 const { pathname } = request.nextUrl;
 const authToken = request.cookies.get('auth-token');

 // Protect all /admin routes (adjust if your route is actually /(admin))
 if (pathname.startsWith('/(admin)') || pathname.startsWith('/admin')) {
   if (!authToken) {
     const loginUrl = new URL('/login', request.url);
     loginUrl.searchParams.set('redirect', pathname);
     return NextResponse.redirect(loginUrl);
   }
 }

 return NextResponse.next();
}

// Match all routes except static files and API routes
export const config = {
  matcher: [
    // Protect all admin pages
    '/orders/:path*',
    '/analytics/:path*',
    '/cashflow/:path*',
    '/checklist/:path*',
    '/invoices/:path*',
    '/overview/:path*',
    '/reconciliation/:path*',
    '/settings/:path*',
    '/statements/:path*',
    // Add more admin routes as needed
  ],
};