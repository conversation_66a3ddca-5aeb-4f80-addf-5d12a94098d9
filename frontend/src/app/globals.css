@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Lendwise-inspired color palette in HSL */
    --background: 228 100% 97%; /* #edf2ff - Very light blue background */
    --foreground: 222 47% 11%; /* #111827 */

    --card: 0 0% 100%; /* #FFFFFF */
    --card-foreground: 222 47% 11%; /* #111827 */

    --popover: 0 0% 100%; /* #FFFFFF */
    --popover-foreground: 222 47% 11%; /* #111827 */

    --primary: 227 84% 60%; /* #4361ee - Bright blue like Lendwise */
    --primary-foreground: 0 0% 100%; /* #FFFFFF */

    --secondary: 228 100% 97%; /* #edf2ff - Very light blue */
    --secondary-foreground: 227 84% 60%; /* #4361ee - Primary blue */

    --muted: 210 40% 98%; /* #f8fafc - Very light blue-gray */
    --muted-foreground: 215 25% 47%; /* #64748b - Slate */

    --accent: 217 91% 62%; /* #3a86ff - Slightly different blue accent */
    --accent-foreground: 0 0% 100%; /* #FFFFFF */

    --destructive: 0 84% 60%; /* #ef4444 - Modern red */
    --destructive-foreground: 0 0% 100%; /* #FFFFFF */

    --success: 160 84% 39%; /* #10b981 - Green */
    --success-foreground: 0 0% 100%; /* #FFFFFF */

    --warning: 38 92% 50%; /* #f59e0b - Amber */
    --warning-foreground: 0 0% 100%; /* #FFFFFF */

    --border: 220 13% 91%; /* #e5e7eb - Very light gray */
    --input: 220 13% 91%; /* #e5e7eb - Light gray */
    --ring: 227 84% 60%; /* #4361ee - Primary blue */

    --radius: 0.5rem;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 227 84% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 91% 62%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 224 76% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
html, body {
    min-height: 100vh;
    margin: 0;
    padding: 0;
  }
  body {
    @apply bg-secondary text-foreground;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    line-height: 1.5;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply text-foreground font-medium;
    letter-spacing: -0.025em;
  }
  h1 {
    @apply text-3xl;
  }
  h2 {
    @apply text-2xl;
  }
  h3 {
    @apply text-xl;
  }
  a {
    @apply text-primary hover:text-primary/80 transition-colors;
  }
  button {
    @apply transition-all duration-200;
  }
}

/* Lendwise-inspired UI Components */
@layer components {
  /* Card styles similar to Lendwise */
  .card {
    @apply bg-white rounded-lg border border-border/50 shadow-sm transition-shadow;
  }
  
  /* Clean, minimal input style */
  .input-modern {
    @apply px-4 py-2 bg-white border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-colors;
  }
  
  /* Button style matching Lendwise */
  .btn-modern {
    @apply inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
  }
  
  /* Table styles like Lendwise */
  .table-modern {
    @apply w-full text-sm text-left;
  }
  
  .table-modern thead {
    @apply bg-muted/50 text-foreground;
  }
  
  .table-modern th {
    @apply px-6 py-3 font-medium text-muted-foreground;
  }
  
  .table-modern td {
    @apply px-6 py-4 border-b border-border/50;
  }
  
  .table-modern tr:hover {
    @apply bg-muted/30;
  }

  /* Status badges like in Lendwise */
  .badge {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-success/10 text-success;
  }
  
  .badge-warning {
    @apply bg-warning/10 text-warning;
  }
  
  .badge-error {
    @apply bg-destructive/10 text-destructive;
  }
  
  .badge-info {
    @apply bg-primary/10 text-primary;
  }

  /* Clean dashboard stats like Lendwise */
  .stat-card {
    @apply p-6 bg-white rounded-lg border border-border/50;
  }
  
  .stat-value {
    @apply text-3xl font-semibold text-foreground;
  }
  
  .stat-label {
    @apply text-sm text-muted-foreground;
  }
  
  /* Chart container like Lendwise */
  .chart-container {
    @apply p-6 bg-white rounded-lg border border-border/50;
  }
}
