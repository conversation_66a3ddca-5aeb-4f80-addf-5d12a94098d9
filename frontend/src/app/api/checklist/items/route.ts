import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';

const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';

export async function GET(request: NextRequest) {
  try {
    // Get auth token from cookie
    const authToken = request.cookies.get('auth-token')?.value;
    const headersList = await headers();
    const authHeader = await headersList.get('Authorization');

    // Use either the cookie or the Authorization header
    const authValue = authToken ? `Bearer ${authToken}` : authHeader;

    if (!authValue) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const orderId = searchParams.get('orderId');
    const status = searchParams.get('status');
    const supplier = searchParams.get('supplier');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    // Build query string
    const queryParams = new URLSearchParams();
    if (orderId) queryParams.append('orderId', orderId);
    if (status) queryParams.append('status', status);
    if (supplier) queryParams.append('supplier', supplier);
    if (startDate) queryParams.append('startDate', startDate);
    if (endDate) queryParams.append('endDate', endDate);
    
    // Make request to backend API
    const response = await fetch(
      `${backendUrl}/api/checklist/items${queryParams.toString() ? `?${queryParams.toString()}` : ''}`,
      {
        headers: {
          'Authorization': authValue,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const data = await response.json();
    
    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || { message: 'Failed to fetch checklist items' } },
        { status: response.status }
      );
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching checklist items:', error);
    return NextResponse.json(
      { error: { message: 'Internal server error' } },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get auth token from cookie
    const authToken = request.cookies.get('auth-token')?.value;
    const headersList = await headers();
    const authHeader = await headersList.get('Authorization');

    // Use either the cookie or the Authorization header
    const authValue = authToken ? `Bearer ${authToken}` : authHeader;

    if (!authValue) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    
    // Make request to backend API
    const response = await fetch(`${backendUrl}/api/checklist/items`, {
      method: 'POST',
      headers: {
        'Authorization': authValue,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || { message: 'Failed to create checklist items' } },
        { status: response.status }
      );
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating checklist items:', error);
    return NextResponse.json(
      { error: { message: 'Internal server error' } },
      { status: 500 }
    );
  }
}
