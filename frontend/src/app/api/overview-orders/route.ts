import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const cookie = request.cookies.get("auth-token");
  const authToken = cookie?.value;
  if (!authToken) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";
  const res = await fetch(`${backendUrl}/api/scrape/orders`, {
    headers: {
      Authorization: authToken.startsWith("Bearer ") ? authToken : `Bearer ${authToken}`,
      Accept: "application/json",
    },
    cache: "no-store",
  });

  let errorText = "";
  if (!res.ok) {
    try {
      errorText = await res.text();
    } catch {
      errorText = "Unknown backend error";
    }
    return NextResponse.json(
      { error: "Failed to fetch orders", backend: errorText, status: res.status },
      { status: res.status }
    );
  }

  const data = await res.json();
  return NextResponse.json(data);
}