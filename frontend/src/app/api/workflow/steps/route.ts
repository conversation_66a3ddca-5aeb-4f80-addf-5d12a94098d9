import { NextRequest, NextResponse } from 'next/server';
import { mockWorkflowSteps } from '@/lib/mock-workflow';

// In-memory state for development
let workflowSteps = [...mockWorkflowSteps];

export async function GET(request: NextRequest) {
  try {
    // In a real implementation, this would fetch workflow steps from the database
    // and include the current user's progress
    
    // For now, we'll just return the mock workflow steps
    return NextResponse.json(workflowSteps);
  } catch (error) {
    console.error('Error fetching workflow steps:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow steps' },
      { status: 500 }
    );
  }
}