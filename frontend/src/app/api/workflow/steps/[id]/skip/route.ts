import { NextRequest, NextResponse } from 'next/server';
import { mockWorkflowSteps } from '@/lib/mock-workflow';

// In-memory state for development
let workflowSteps = [...mockWorkflowSteps];

export async function POST(request: NextRequest) {
  // Extract the ID from the URL path
  const matches = request.nextUrl.pathname.match(/\/steps\/([^\/]+)\/skip/);
  const id = matches ? matches[1] : null;
  
  try {
    console.log('POST request path id:', id);
    const stepId = id;
    
    // Find the step in our mock database
    const stepIndex = workflowSteps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) {
      return NextResponse.json(
        { error: 'Workflow step not found' },
        { status: 404 }
      );
    }
    
    // Update the step status to 'skipped'
    workflowSteps[stepIndex] = {
      ...workflowSteps[stepIndex],
      status: 'skipped'
    };
    
    // Find the next step and update its status to 'in-progress'
    if (stepIndex < workflowSteps.length - 1) {
      workflowSteps[stepIndex + 1] = {
        ...workflowSteps[stepIndex + 1],
        status: 'in-progress'
      };
    }
    
    return NextResponse.json({
      success: true,
      message: `Workflow step ${stepId} skipped successfully`,
      nextStep: stepIndex < workflowSteps.length - 1 ? workflowSteps[stepIndex + 1] : null
    });
  } catch (error) {
    console.error('Error skipping workflow step:', error);
    return NextResponse.json(
      { error: 'Failed to skip workflow step' },
      { status: 500 }
    );
  }
}