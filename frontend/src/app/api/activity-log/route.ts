import { NextRequest, NextResponse } from 'next/server';
import { mockActivities } from '@/lib/mock-notifications';

// In-memory state for development
let activities = [...mockActivities];

export async function GET(request: NextRequest) {
  try {
    // Parse query parameters for filtering
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type');
    const userId = searchParams.get('userId');
    const startDateStr = searchParams.get('startDate');
    const endDateStr = searchParams.get('endDate');
    
    // Apply filters
    let filteredActivities = [...activities];
    
    if (type && type !== 'all') {
      filteredActivities = filteredActivities.filter(activity => activity.type === type);
    }
    
    if (userId && userId !== 'all') {
      filteredActivities = filteredActivities.filter(activity => activity.user.id === userId);
    }
    
    if (startDateStr) {
      const startDate = new Date(startDateStr);
      filteredActivities = filteredActivities.filter(activity => activity.timestamp >= startDate);
    }
    
    if (endDateStr) {
      const endDate = new Date(endDateStr);
      filteredActivities = filteredActivities.filter(activity => activity.timestamp <= endDate);
    }
    
    // Sort by timestamp (newest first)
    filteredActivities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    
    return NextResponse.json(filteredActivities);
  } catch (error) {
    console.error('Error fetching activity log:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activity log' },
      { status: 500 }
    );
  }
}