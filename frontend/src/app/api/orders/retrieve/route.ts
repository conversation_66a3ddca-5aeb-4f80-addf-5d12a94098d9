import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

export async function POST(request: Request) {
  try {
    // Get request body and validate
    const body = await request.json();
    const { startDate, endDate } = body;

    // Debug logging
    console.log('API route called with dates:', { startDate, endDate });

    // Validate required parameters
    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing date parameters' },
        { status: 400 }
      );
    }

    // Validate date format (YYYY-MM-DD HH:mm)
    const dateRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/;
    if (!dateRegex.test(startDate) || !dateRegex.test(endDate)) {
      return NextResponse.json(
        { error: 'Invalid date format. Expected YYYY-MM-DD HH:mm' },
        { status: 400 }
      );
    }

    // Get auth token from cookies
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');

    // Prepare headers
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    };

    // Add authorization if token exists
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken.value}`;
    }

    // Debug endpoint URL
    const endpoint = `${API_URL}/api/scrape/retrieve`;
    console.log('Making request to:', endpoint);

    // Forward request to backend with date parameters
    const response = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify({ startDate, endDate }),
      cache: 'no-store'
    });

    // Debug response status
    console.log('Response status:', response.status);

    if (!response.ok) {
      // Get error message from response if possible
      let errorMessage = 'Failed to retrieve orders';
      try {
        const contentType = response.headers.get('content-type') || '';
        if (contentType.includes('application/json')) {
          const errorData = await response.json();
          errorMessage = errorData.error?.message || errorData.error || errorMessage;
        } else {
          errorMessage = await response.text() || errorMessage;
        }
      } catch {
        // If parsing fails, use status text
        errorMessage = response.statusText || errorMessage;
      }
      
      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    // Get response text for debugging
    const responseText = await response.text();
    console.log('Response text:', responseText);

    try {
      // Try to parse response as JSON
      const data = responseText ? JSON.parse(responseText) : null;
      if (!data) {
        throw new Error('Empty response');
      }
      return NextResponse.json(data);
    } catch (parseError) {
      console.error('Failed to parse response:', parseError);
      return NextResponse.json(
        { error: 'Invalid response from server' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in /api/orders/retrieve:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}