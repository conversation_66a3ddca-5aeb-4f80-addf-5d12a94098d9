import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    // Get auth token from cookie
    const authToken = request.cookies.get('auth-token')?.value;
    const headersList = await headers();
    const authHeader = await headersList.get('Authorization');

    // Use either the cookie or the Authorization header
    const authValue = authToken ? `Bearer ${authToken}` : authHeader;

    if (!authValue) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use the /api/scrape/orders endpoint that returns orders from Firebase
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';
    const response = await fetch(`${backendUrl}/api/scrape/orders`, {
      headers: {
        'Authorization': authValue,
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to retrieve orders');
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error retrieving orders:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to retrieve orders' },
      { status: 500 }
    );
  }
}