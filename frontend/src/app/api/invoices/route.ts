import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

export async function GET() {
  try {
    // Get auth token from cookies
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');

    // Prepare headers
    const headers: HeadersInit = {
      'Accept': 'application/json'
    };

    // Add authorization if token exists
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken.value}`;
    }

    // Forward request to backend
    const response = await fetch(`${API_URL}/api/invoices`, {
      headers,
      // Add cache: 'no-store' to prevent caching
      cache: 'no-store'
    });

    if (!response.ok) {
      // Get error message from response if possible
      let errorMessage = 'Failed to fetch invoices';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error?.message || errorData.error || errorMessage;
      } catch {
        // If parsing fails, use status text
        errorMessage = response.statusText || errorMessage;
      }
      
      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in /api/invoices:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
export async function POST(request: Request) {
  try {
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');
    const headers: HeadersInit = {};

    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken.value}`;
    }

    // Get the raw form data from the request
    const formData = await request.formData();

    // Prepare a new FormData for the backend
    const backendForm = new FormData();
    for (const [key, value] of formData.entries()) {
      backendForm.append(key, value);
    }

    const response = await fetch(`${API_URL}/api/invoices/process`, {
      method: 'POST',
      headers,
      body: backendForm,
    });

    const contentType = response.headers.get('content-type') || '';
    let data;
    if (contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    if (!response.ok) {
      return NextResponse.json(
        { error: typeof data === 'string' ? data : (data?.error || 'Upload failed') },
        { status: response.status }
      );
    }

    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error proxying invoice upload:', error);
    return NextResponse.json(
      { error: 'Failed to upload invoice' },
      { status: 500 }
    );
  }
}