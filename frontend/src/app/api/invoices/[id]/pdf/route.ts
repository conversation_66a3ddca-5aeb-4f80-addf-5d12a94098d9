import { NextRequest, NextResponse } from "next/server";

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  const { id } = params;
  
  console.log(`[PDF Route] Fetching PDF for invoice ${id}`);
  console.log(`[PDF Route] Headers:`, Object.fromEntries(req.headers.entries()));

  try {
    const authToken = req.headers.get("cookie")?.match(/auth-token=([^;]+)/)?.[1];
    if (!authToken) {
      console.error("[PDF Route] No auth token found in cookies");
      return new NextResponse("Unauthorized - No auth token", { status: 401 });
    }
  
    console.log("[PDF Route] Fetching with auth token:", authToken.substring(0, 20) + "...");
  
    // Forward cookies and headers for authentication
    const backendRes = await fetch(`${API_URL}/api/invoices/${id}/pdf`, {
      method: "GET",
      headers: {
        "Accept": "application/pdf",
        "Authorization": `Bearer ${authToken}`,
        "Cookie": `auth-token=${authToken}`,
      },
      credentials: "include",
    });
  
    console.log("[PDF Route] Backend response status:", backendRes.status);
    console.log("[PDF Route] Backend response headers:", Object.fromEntries(backendRes.headers.entries()));
    
    if (!backendRes.ok) {
      console.error(`[PDF Route] Backend error:`, await backendRes.text());
      return new NextResponse(`Failed to fetch PDF: ${backendRes.statusText}`, {
        status: backendRes.status,
      });
    }

    const data = await backendRes.arrayBuffer();
    const contentType = backendRes.headers.get("content-type") || "application/pdf";
    const contentDisposition = backendRes.headers.get("content-disposition");

    console.log(`[PDF Route] Received PDF, size:`, data.byteLength, 'bytes');
    console.log(`[PDF Route] Content-Type:`, contentType);

    const response = new NextResponse(data, {
      status: 200,
      headers: {
        "content-type": contentType,
        ...(contentDisposition ? { "content-disposition": contentDisposition } : {}),
      },
    });

    return response;
  } catch (error: any) {
    console.error(`[PDF Route] Error:`, error);
    return new NextResponse(`Internal Server Error: ${error?.message || 'Unknown error'}`, {
      status: 500,
    });
  }
}