import { NextRequest, NextResponse } from "next/server";

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

type Context = {
  params: { id: string }
};

export async function GET(
  req: NextRequest,
  { params }: Context
) {
  // DEBUG: Log incoming headers and cookies
  console.log("Next.js API route - incoming headers:", Object.fromEntries(req.headers.entries()));
  const { id } = params;

  // Forward cookies and headers for authentication
  const backendRes = await fetch(`${API_URL}/api/invoices/${id}`, {
    method: "GET",
    headers: {
      "Accept": "application/json",
      // Forward the Authorization header if present
      ...(req.headers.get("authorization") ? { "authorization": req.headers.get("authorization")! } : {}),
      // Forward cookies for backend auth
      ...(req.headers.get("cookie") ? { "cookie": req.headers.get("cookie")! } : {}),
    },
    credentials: "include",
  });

  const data = await backendRes.arrayBuffer();
  const contentType = backendRes.headers.get("content-type") || "application/json";
  const response = new NextResponse(data, {
    status: backendRes.status,
    headers: {
      "content-type": contentType,
    },
  });

  return response;
}