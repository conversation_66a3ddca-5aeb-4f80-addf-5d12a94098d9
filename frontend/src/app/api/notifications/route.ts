import { NextRequest, NextResponse } from 'next/server';
import { mockNotifications } from '@/lib/mock-notifications';

// In-memory state for development
let notifications = [...mockNotifications];

export async function GET(request: NextRequest) {
  try {
    // In a real implementation, this would fetch notifications from the database
    // filtered by the current user
    
    // For now, we'll just return the mock notifications
    return NextResponse.json(notifications);
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
}