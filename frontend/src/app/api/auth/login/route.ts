import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Forward the request to the backend
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';
    console.log('Attempting to connect to backend at:', backendUrl);
    
    let response;
    let data;
    
    try {
      response = await fetch(`${backendUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      console.log('Backend response status:', response.status);
      data = await response.json();
      console.log('Backend response data:', JSON.stringify(data));
    } catch (error) {
      console.error('Fetch error details:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to connect to backend: ${errorMessage}`);
    }
    
    // If login is successful, set the auth token as a cookie
    if (data.success && data.token) {
      const res = NextResponse.json(data, { status: response.status });
      res.cookies.set('auth-token', data.token, {
        path: '/',
        maxAge: 86400,
        httpOnly: true,
        sameSite: 'lax',
      });
      return res;
    }

    return NextResponse.json(data, {
      status: response.status
    });
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { success: false, error: 'Authentication failed' },
      { status: 500 }
    );
  }
}