// Temporary proxy for development: Forwards to backend Express server
import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const email = searchParams.get('email');
  if (!email) {
    return NextResponse.json({ success: false, error: 'Email is required' }, { status: 400 });
  }
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';
  const url = `${backendUrl}/api/auth/dev-get-password?email=${encodeURIComponent(email)}`;
  try {
    const res = await fetch(url, { method: 'GET' });
    const data = await res.json();
    return NextResponse.json(data, { status: res.status });
  } catch (err) {
    return NextResponse.json({ success: false, error: 'Failed to proxy request' }, { status: 500 });
  }
}