import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

export async function POST() {
  try {
    // Get auth token from cookies
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');

    // Prepare headers
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    };

    // Add authorization if token exists
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken.value}`;
    }

    // Forward request to backend
    // Note: This endpoint might not exist yet in the backend and would need to be implemented
    const response = await fetch(`${API_URL}/api/statements/process-email`, {
      method: 'POST',
      headers,
      // Add cache: 'no-store' to prevent caching
      cache: 'no-store'
    });

    if (!response.ok) {
      // Get error message from response if possible
      let errorMessage = 'Failed to process statements from email';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error?.message || errorData.error || errorMessage;
      } catch {
        // If parsing fails, use status text
        errorMessage = response.statusText || errorMessage;
      }
      
      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in /api/statements/process-email:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}