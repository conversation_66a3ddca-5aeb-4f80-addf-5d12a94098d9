import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

export async function GET(request: Request) {
  try {
    // Get auth token from cookies
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const supplier = searchParams.get('supplier');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    // Build query string
    let queryString = '';
    if (supplier) queryString += `supplier=${encodeURIComponent(supplier)}&`;
    if (startDate) queryString += `startDate=${encodeURIComponent(startDate)}&`;
    if (endDate) queryString += `endDate=${encodeURIComponent(endDate)}&`;
    
    // Prepare headers
    const headers: HeadersInit = {
      'Accept': 'application/json'
    };

    // Add authorization if token exists
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken.value}`;
    }

    // Forward request to backend
    const response = await fetch(`${API_URL}/api/statements${queryString ? `?${queryString}` : ''}`, {
      headers,
      // Add cache: 'no-store' to prevent caching
      cache: 'no-store'
    });

    if (!response.ok) {
      // Get error message from response if possible
      let errorMessage = 'Failed to fetch statements';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error?.message || errorData.error || errorMessage;
      } catch {
        // If parsing fails, use status text
        errorMessage = response.statusText || errorMessage;
      }
      
      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data.statements || []);
  } catch (error) {
    console.error('Error in /api/statements:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}