import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";

export async function POST(request: Request) {
  try {
    // Get auth token from cookies
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');

    // Get form data from request
    const formData = await request.formData();
    
    // Prepare headers for the backend request
    const headers: HeadersInit = {};

    // Add authorization if token exists
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken.value}`;
    }

    // Forward request to backend
    const response = await fetch(`${API_URL}/api/statements/upload`, {
      method: 'POST',
      headers,
      body: formData,
    });

    if (!response.ok) {
      // Get error message from response if possible
      let errorMessage = 'Failed to upload statement';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error?.message || errorData.error || errorMessage;
      } catch {
        // If parsing fails, use status text
        errorMessage = response.statusText || errorMessage;
      }
      
      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in /api/statements/upload:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}