import { NextRequest, NextResponse } from 'next/server';
import { mockStatements } from '@/lib/mock-statements';

// In-memory state for development
let statements = [...mockStatements];

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { statementId, invoiceId, resolution } = body;
    
    if (!statementId || !invoiceId) {
      return NextResponse.json(
        { error: 'Statement ID and Invoice ID are required' },
        { status: 400 }
      );
    }
    
    // Find the statement in our mock database
    const statementIndex = statements.findIndex(s => s.id === statementId);
    if (statementIndex === -1) {
      return NextResponse.json(
        { error: 'Statement not found' },
        { status: 404 }
      );
    }
    
    // Find the invoice in the statement
    const statement = statements[statementIndex];
    const invoiceIndex = statement.invoices.findIndex(i => i.invoiceId === invoiceId);
    if (invoiceIndex === -1) {
      return NextResponse.json(
        { error: 'Invoice not found in statement' },
        { status: 404 }
      );
    }
    
    // Update the invoice status to 'matched'
    const updatedInvoices = [...statement.invoices];
    updatedInvoices[invoiceIndex] = {
      ...updatedInvoices[invoiceIndex],
      status: 'matched',
      // In a real implementation, we would store the resolution details
      // For now, we'll just remove the discrepancies
      discrepancies: undefined
    };
    
    // Check if all invoices are now matched
    const allMatched = updatedInvoices.every(invoice => invoice.status === 'matched');
    
    // Update the statement in our mock database
    statements[statementIndex] = {
      ...statement,
      invoices: updatedInvoices,
      reconciled: allMatched,
      updatedAt: new Date()
    };
    
    return NextResponse.json({
      success: true,
      message: `Invoice ${invoiceId} reconciled successfully`,
      statementReconciled: allMatched
    });
  } catch (error) {
    console.error('Error reconciling invoice:', error);
    return NextResponse.json(
      { error: 'Failed to reconcile invoice' },
      { status: 500 }
    );
  }
}