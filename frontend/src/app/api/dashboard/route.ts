import { NextRequest, NextResponse } from 'next/server';
import { mockDashboardStats } from '@/lib/mock-workflow';

export async function GET(request: NextRequest) {
  try {
    // In a real implementation, this would fetch data from various sources
    // and calculate the dashboard statistics
    
    // For now, we'll just return the mock dashboard stats
    return NextResponse.json(mockDashboardStats);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard stats' },
      { status: 500 }
    );
  }
}