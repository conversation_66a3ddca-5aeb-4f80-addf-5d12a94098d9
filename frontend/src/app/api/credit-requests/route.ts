import { NextRequest, NextResponse } from 'next/server';
import { mockCreditRequests } from '@/lib/mock-credit-requests';
import { CreditRequest } from '@/types/credit-request';

// In-memory store for development
let creditRequests = [...mockCreditRequests];

export async function GET() {
  try {
    return NextResponse.json(creditRequests);
  } catch (error) {
    console.error('Error fetching credit requests:', error);
    return NextResponse.json(
      { error: 'Failed to fetch credit requests' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    const newRequest: CreditRequest = {
      ...data,
      id: `CR${String(creditRequests.length + 1).padStart(3, '0')}`,
      status: 'pending',
      requestDate: new Date(),
      emailHistory: []
    };

    creditRequests.push(newRequest);
    return NextResponse.json(newRequest);
  } catch (error) {
    console.error('Error creating credit request:', error);
    return NextResponse.json(
      { error: 'Failed to create credit request' },
      { status: 500 }
    );
  }
}
