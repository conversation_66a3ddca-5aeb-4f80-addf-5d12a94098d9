import "./globals.css";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Pharmaccounts Admin",
  description: "Invoice Management System",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="bg-background">
        <main className="min-h-screen">
          <div className="max-w-[1400px] mx-auto px-8 py-6">
            {children}
          </div>
        </main>
      </body>
    </html>
  );
}
