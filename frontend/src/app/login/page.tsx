'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { authApi } from '@/lib/api';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resetStatus, setResetStatus] = useState<string | null>(null);
  const [resetLoading, setResetLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      const response = await authApi.login(email, password);
      if (response.success) {
        // Simple direct navigation
        window.location.href = '/orders';
      } else {
        setError(response.error || 'Login failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '<PERSON><PERSON> failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="mb-2"
                placeholder="Email address"
              />
            </div>
            <div>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                placeholder="Password"
              />
            </div>
            <div className="flex items-center justify-between mt-2">
              <button
                type="button"
                className="text-sm text-blue-600 hover:underline focus:outline-none"
                onClick={async () => {
                  setResetStatus(null);
                  setResetLoading(true);
                  try {
                    const result = await authApi.resetPassword(email);
                    if (result.success) {
                      setResetStatus("Password reset email sent. Please check your inbox.");
                    } else {
                      setResetStatus(result.error || "Failed to send password reset email.");
                    }
                  } catch (err) {
                    setResetStatus("Failed to send password reset email.");
                  } finally {
                    setResetLoading(false);
                  }
                }}
                disabled={!email || resetLoading}
                aria-disabled={!email || resetLoading}
              >
                {resetLoading ? "Sending reset email..." : "Forgot my password?"}
              </button>
            </div>
            {resetStatus && (
              <div className="text-sm text-gray-700 mt-2 text-center">
                {resetStatus}
              </div>
            )}
          </div>

          {error && (
            <div className="text-red-500 text-sm text-center">{error}</div>
          )}

          <div>
            <Button
              type="submit"
              className="w-full"
              disabled={loading}
            >
              {loading ? 'Signing in...' : 'Sign in'}
            </Button>
          </div>
        </form>
        {/* Forgot password logic */}
        {/* Removed duplicate dev password logic */}
      </div>
    </div>
  );
}