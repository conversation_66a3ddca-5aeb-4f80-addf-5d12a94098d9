import { StatementDetail } from '@/components/statement-detail';
import { getStatementById } from '@/lib/api/statement';
import { Statement } from '@/types/statement';
import { notFound } from 'next/navigation';
import React from 'react';
import { cookies } from 'next/headers'; // Import cookies (Ensure only one import)

interface StatementPageProps {
  params: {
    statementId: string;
  };
}

// Helper function to safely parse statement data, especially dates
const parseStatementData = (data: any): Statement | null => {
  try {
    // data.period.start, data.createdAt etc. are already Date objects or null from the API lib layer
    const periodStart = data.period?.start || null;
    const periodEnd = data.period?.end || null;
    const createdAtDate = data.createdAt || null;
    const updatedAtDate = data.updatedAt || null;
    const statementDateValue = data.statementDate || null; // statementDate is now parsed in API lib

    // data.invoices items have inv.Date (uppercase) as ISO string from backend via API lib
    console.log("Frontend: parseStatementData - raw data.period:", data.period);
    console.log("Frontend: parseStatementData - raw data.createdAt:", data.createdAt, "raw data.updatedAt:", data.updatedAt);
    console.log("Frontend: parseStatementData - raw data.statementDate (should be Date object or null):", data.statementDate);

    const parsedInvoices = (data.invoices || []).map((inv: any, index: number) => {
      // The backend sends 'Date' and 'Amount' (uppercase) for invoice items
      // The StatementItem type expects 'date' and 'amount' (lowercase)
      console.log(`Frontend: parseStatementData - invoice item ${index} - raw inv.Date:`, inv.Date, `(type: ${typeof inv.Date})`);
      const itemDate = inv.Date ? new Date(inv.Date) : null;
      console.log(`Frontend: parseStatementData - invoice item ${index} - parsed itemDate:`, itemDate);
      return {
        ...inv, // Spread other potential fields like invoiceId
        invoiceId: inv.invoiceId || inv.InvoiceNumber, // Handle potential legacy InvoiceNumber
        date: itemDate,
        amount: typeof inv.Amount === 'number' ? inv.Amount : (inv.Amount ? parseFloat(inv.Amount) : null),
        type: inv.Type || inv.type || 'invoice', // Map 'Type' (from backend) or 'type' to 'type', default
        // status is not typically part of raw statement items from backend, will default in component
      };
    });

    const result = {
      ...data,
      period: {
        start: periodStart,
        end: periodEnd,
      },
      invoices: parsedInvoices,
      createdAt: createdAtDate,
      updatedAt: updatedAtDate,
      statementDate: statementDateValue,
    };
    console.log("Frontend: parseStatementData - final parsed period:", result.period);
    console.log("Frontend: parseStatementData - final parsed createdAt:", result.createdAt, "final parsed updatedAt:", result.updatedAt);
    console.log("Frontend: parseStatementData - final parsed statementDate:", result.statementDate);
    return result;
  } catch (error) {
    console.error("Error parsing statement data:", error);
    return null; // Return null if parsing fails
  }
};


export default async function StatementPage(props: StatementPageProps) {
  // Explicitly access params from props.
  // While { params } destructuring in signature is common, let's try this
  // to see if it resolves the specific runtime warning/error.
  const params = props.params;
  const statementId = params.statementId;

  if (!statementId) {
    notFound(); // Should not happen with dynamic routes, but good practice
  }

  let statementData: Statement | null = null;
  let error: string | null = null;

  try {
    // Read the auth token from cookies
    const cookieStore = await cookies(); // Add await here
    const token = cookieStore.get('auth-token')?.value;

    if (!token) {
        // Handle case where token is missing - maybe redirect to login or show error
        // For now, let's throw an error that the page can display
        throw new Error("Authentication token is missing. Please log in.");
    }

    // Fetch statement data from the backend API, passing the token
    const rawStatementData = await getStatementById(statementId, token);

    if (!rawStatementData) {
      notFound(); // Statement not found by API
    }
    
    // Parse the raw data, converting date strings to Date objects
    statementData = parseStatementData(rawStatementData);
    
    if (!statementData) {
        throw new Error("Failed to parse statement data, potentially invalid date format.");
    }

  } catch (err: any) {
    console.error(`Error fetching statement ${statementId}:`, err);
    // Handle specific errors if needed, e.g., 403 Forbidden
    if (err.message?.includes('404') || err.message?.includes('not found')) {
        notFound();
    }
    error = `Failed to load statement: ${err.message || 'Unknown error'}`;
  }

  if (error) {
    return <div className="p-6 text-red-600 bg-red-100 border border-red-300 rounded-md">{error}</div>;
  }

  if (!statementData) {
     // This case handles scenarios where parsing failed or data is unexpectedly null post-fetch
    return <div className="p-6 text-orange-600 bg-orange-100 border border-orange-300 rounded-md">Statement data could not be loaded or parsed correctly.</div>;
  }

  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      <h1 className="text-2xl font-bold mb-6">Statement Details</h1>
      <StatementDetail statement={statementData} />
      {/* Potentially add InvoicePDFViewer here if needed later */}
    </div>
  );
}
