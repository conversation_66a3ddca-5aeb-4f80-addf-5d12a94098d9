'use client';

import { useState, useEffect } from 'react';
import { StatementsTable } from "@/components/statements-table";
import { StatementUpload } from "@/components/statement-upload";
import { StatementRetrieve } from "@/components/statement-retrieve";
import { Statement } from "@/types/statement";
import { getStatements } from '@/lib/api/statement';

export default function StatementsPage() {
  const [statements, setStatements] = useState<Statement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchStatements = async () => {
    if (loading && statements.length > 0) return; // Prevent multiple simultaneous requests if we already have data
    
    try {
      setLoading(true);
      const data = await getStatements();
      setError(null);
      setStatements(data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching statements:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch statements');
      return false;
    } finally {
      setLoading(false);
    }
    return true;
  };

  useEffect(() => {
    // Initial fetch
    fetchStatements();
  }, []); // Only run on mount

  const handleUploadComplete = async () => {
    try {
      setLoading(true);
      await fetchStatements();
    } catch (err) {
      console.error('Error refreshing statements after upload:', err);
    } finally {
      setLoading(false);
    }
  };

  if (error) {
    return (
      <div className="py-8 max-w-[1400px] mx-auto px-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-red-800 text-lg font-semibold mb-2">Error Loading Statements</h2>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchStatements}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Statements</h1>
          <div className="text-sm text-gray-500 mt-1">
            {lastUpdated && (
              <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
            )}
          </div>
        </div>
        <div className="flex items-center gap-4">
          <StatementUpload onUploadComplete={handleUploadComplete} />
          <StatementRetrieve onRetrieveComplete={handleUploadComplete} />
        </div>
      </div>
      
      {loading && statements.length === 0 ? (
        <div className="py-8 flex items-center justify-center text-gray-500">
          <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          Loading statements...
        </div>
      ) : (
        <StatementsTable statements={statements} />
      )}
    </div>
  );
}