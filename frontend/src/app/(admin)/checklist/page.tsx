'use client';

import { useState, useEffect } from 'react';
import { ChecklistTable } from '@/components/checklist-table';
import { ChecklistGenerator } from '@/components/checklist-generator';
import { Button } from '@/components/ui/button';
import { Order } from '@/lib/csv-utils';
import { ChecklistItem } from '@/types/checklist';
import { getOrders } from '@/lib/api/orders';
import { getChecklistItems, createChecklistItems } from '@/lib/api/checklist';

export default function ChecklistPage() {
  const [view, setView] = useState<'list' | 'generate'>('list');
  const [items, setItems] = useState<ChecklistItem[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const userId = 'user-1'; // Mock user ID

  // Load checklist items
  useEffect(() => {
    const loadItems = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const items = await getChecklistItems();
        setItems(items);
      } catch (error) {
        console.error('Failed to load checklist items:', error);
        setError('Failed to load checklist items. Please make sure you are logged in.');
      } finally {
        setIsLoading(false);
      }
    };

    loadItems();
  }, []);

  // Load orders
  useEffect(() => {
    const loadOrders = async () => {
      try {
        const data = await getOrders();
        setOrders(data);
      } catch (error) {
        console.error('Failed to load orders:', error);
        if (!error) {
          setError('Failed to load orders. Please make sure you are logged in.');
        }
      }
    };

    loadOrders();
  }, []);

  const handleGenerateChecklist = async (newItems: ChecklistItem[]) => {
    setError(null);
    try {
      const savedItems = await createChecklistItems(newItems);
      setItems(prev => [...savedItems, ...prev]);
      setView('list');
    } catch (error) {
      console.error('Failed to generate checklist:', error);
      setError('Failed to generate checklist. Please make sure you are logged in.');
      throw error;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="text-red-500 mb-4">{error}</div>
        <Button onClick={() => window.location.href = '/login'}>
          Go to Login
        </Button>
      </div>
    );
  }

  return view === 'list' ? (
    <>
      <div className="mb-6 flex justify-end">
        <Button onClick={() => setView('generate')}>
          Generate New Checklist
        </Button>
      </div>
      <ChecklistTable items={items} />
    </>
  ) : (
    <>
      <div className="mb-6 flex justify-end">
        <Button onClick={() => setView('list')}>
          View Checklist Items
        </Button>
      </div>
      <ChecklistGenerator
        orders={orders}
        onGenerate={handleGenerateChecklist}
        userId={userId}
      />
    </>
  );
}