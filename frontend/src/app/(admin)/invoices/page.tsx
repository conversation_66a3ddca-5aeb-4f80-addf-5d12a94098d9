'use client';

import { useState, useEffect } from 'react';
import { InvoicesTable } from "@/components/invoices-table";
import { InvoiceUpload } from "@/components/invoice-upload";
import { InvoiceRetrieve } from "@/components/invoice-retrieve";
import { Invoice } from "@/types/api";
import { invoicesApi } from '@/lib/api';

const POLLING_INTERVAL = 5000; // Poll every 5 seconds

export default function InvoicesPage() {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchInvoices = async () => {
    // Only prevent refetching if we're already fetching and it's not the initial load
    if (loading && invoices.length > 0) return;
    
    try {
      setLoading(true);
      const data = await invoicesApi.getInvoices();
      if (!data || (Array.isArray(data) && data.length === 0)) {
        setError('No invoices found or failed to fetch invoices');
        return false;
      }
      setError(null);
      setInvoices(data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching invoices:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch invoices');
      // Clear polling when error occurs
      return false;
    } finally {
      setLoading(false);
    }
    return true;
  };

  useEffect(() => {
    // Initial fetch
    fetchInvoices();

    // Set up polling
    const pollInterval = setInterval(async () => {
      const success = await fetchInvoices();
      // If fetch failed, clear the polling
      if (!success) {
        clearInterval(pollInterval);
      }
    }, POLLING_INTERVAL);

    // Cleanup
    return () => clearInterval(pollInterval);
  }, []); // Only run on mount

  const handleUploadComplete = async () => {
    try {
      setLoading(true);
      await fetchInvoices();
    } catch (err) {
      console.error('Error refreshing invoices after upload:', err);
    } finally {
      setLoading(false);
    }
  };

  // Show error message above main content, but do not block UI
  return (
    <div>
      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-red-800 text-lg font-semibold mb-2">Error Loading Invoices</h2>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchInvoices}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      )}
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Invoices</h1>
          <div className="text-sm text-gray-500 mt-1">
            {lastUpdated && (
              <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
            )}
          </div>
        </div>
        <div className="flex items-center gap-4">
          <InvoiceUpload onUploadComplete={handleUploadComplete} />
          <InvoiceRetrieve onRetrieveComplete={handleUploadComplete} />
        </div>
      </div>
      
      {loading && invoices.length === 0 ? (
        <div className="py-8 flex items-center justify-center text-gray-500">
          <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          Loading invoices...
        </div>
      ) : (
        <InvoicesTable invoices={invoices} />
      )}
    </div>
  );
}