"use client";

import { notFound } from "next/navigation";
import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { invoicesApi, refreshAuthToken } from "@/lib/api";
import { InvoiceDetail } from "@/components/invoice-detail";
import { Button } from "@/components/ui/button";
import { Invoice } from "@/types/api";

export default function InvoicePage() {
  const router = useRouter();
  const { invoiceId } = useParams() as { invoiceId: string };
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInvoice = async () => {
      try {
        setIsLoading(true);
        setError(null);
        await refreshAuthToken();
        const data = await invoicesApi.getInvoice(invoiceId);
        setInvoice(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch invoice');
        console.error('Error fetching invoice:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoice();
  }, [invoiceId]);

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[400px]">
        <div className="text-gray-500">Loading invoice...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            className="flex items-center gap-2"
            onClick={() => router.push('/invoices')}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="m15 18-6-6 6-6"/>
            </svg>
            Back to Invoices
          </Button>
        </div>
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  if (!invoice) {
    notFound();
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <Button
          variant="ghost"
          className="flex items-center gap-2"
          onClick={() => router.push('/invoices')}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="m15 18-6-6 6-6"/>
          </svg>
          Back to Invoices
        </Button>
        <h1 className="text-2xl font-semibold">Invoice {invoice.InvoiceId}</h1>
      </div>
      
      <div className="bg-white rounded-lg shadow p-6">
        <InvoiceDetail invoice={invoice} />
      </div>
    </div>
  );
}