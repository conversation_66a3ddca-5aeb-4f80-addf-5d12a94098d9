"use client";
import { useEffect, useState } from "react";
import { SupplierMetrics } from "@/components/supplier-metrics";
import { type Order } from "@/lib/csv-utils";

export default function OverviewPage() {
  return <OverviewClient />;
}

function OverviewClient() {
  const [orders, setOrders] = useState<Order[] | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetch("/api/overview-orders")
      .then(async (res) => {
        if (!res.ok) throw new Error("Failed to fetch orders");
        return res.json();
      })
      .then(setOrders)
      .catch((err) => setError(err.message));
  }, []);

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Failed to load overview</h1>
        <p className="text-gray-700">{error}</p>
      </div>
    );
  }

  if (!orders) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Overview</h1>
      </div>
      <SupplierMetrics orders={orders} />
    </div>
  );
}