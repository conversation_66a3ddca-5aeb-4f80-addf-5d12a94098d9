import { OrdersTable } from '@/components/orders-table';
import { type Order } from '@/lib/csv-utils';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

// Use ISR with a revalidation period instead of force-dynamic
export const revalidate = 60; // Revalidate every 60 seconds

async function getOrders(): Promise<Order[]> {
  const cookieStore = await cookies();
  const authToken = cookieStore.get('auth-token')?.value;

  if (!authToken) {
    redirect('/login');
  }

  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';
  
  // Log the fetch request for debugging
  console.log('Fetching orders from:', `${backendUrl}/api/scrape/orders`);
  
  const res = await fetch(`${backendUrl}/api/scrape/orders`, {
    // Use next.js cache with a shorter revalidation time to ensure fresher data
    next: { revalidate: 30 },
    headers: {
      'Authorization': authToken.startsWith('Bearer ') ? authToken : `Bearer ${authToken}`,
      'Accept': 'application/json'
    }
  });
  
  if (!res.ok) {
    const errorData = await res.json();
    console.error('Orders fetch error:', {
      status: res.status,
      statusText: res.statusText,
      errorData
    });
    throw new Error(errorData.error?.message || 'Failed to fetch orders');
  }
  
  const data = await res.json();
  
  // Log the received data for debugging
  console.log('Orders fetch successful:', {
    count: data.length,
    sampleDateTimes: data.slice(0, 3).map((order: Order) => order.dateTime)
  });
  
  return data;
}

export default async function OrdersPage() {
  const orders = await getOrders();

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Orders</h1>
      </div>
      <OrdersTable orders={orders} />
    </div>
  );
}