'use client';

import { useState, useEffect, useMemo } from 'react'; // Added useMemo
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AlertCircle, CheckCircle, XCircle, MinusCircle, ArrowUpDown } from 'lucide-react'; // Added ArrowUpDown

// --- Sorting Types ---
type SortOrder = 'asc' | 'desc';

// For Order vs. Invoice Table
type SortFieldOrderInvoice = 'orderNo' | 'orderDate' | 'orderSupplier' | 'invoiceNo' | 'orderAmount';
// For Invoice vs. Statement Table
type SortFieldInvoiceStatement = 'invoiceNo' | 'invoiceDate' | 'invoiceSupplier' | 'invoiceAmount';


// --- Data Types ---
interface OrderInfo {
  id: string;
  orderNo?: string;
  dateTime?: string;
  supplier?: string;
  subTotal?: number | null;
}

interface InvoiceInfo {
   id: string;
   InvoiceId?: string; // Invoice Number
   InvoiceDate?: string;
   VendorName?: string;
   InvoiceTotal?: number | null;
}

interface ItemDiscrepancyDetail {
  type: 'MISSING_FROM_INVOICE' | 'EXTRA_ON_INVOICE' | 'QUANTITY_MISMATCH' | 'PRICE_MISMATCH';
  itemId: string;
  description: string;
  orderQty?: number;
  invoiceQty?: number;
  orderPrice?: number | null;
  invoicePrice?: number | null;
}

interface OrderInvoiceDiscrepancy {
  type: 'MISSING_INVOICE' | 'ITEM_MISMATCH';
  order: OrderInfo;
  invoice?: InvoiceInfo; // Present for ITEM_MISMATCH
  details: string | ItemDiscrepancyDetail[]; // String for MISSING_INVOICE, array for ITEM_MISMATCH
}

// Updated interface to handle both missing and amount mismatch
interface InvoiceStatementDiscrepancy {
   type: 'MISSING_FROM_STATEMENT' | 'AMOUNT_MISMATCH_STATEMENT';
   invoice: InvoiceInfo;
   statementAmount?: number | null; // Added for amount mismatch
   details: string;
}

interface ReconciliationDiscrepancies {
  orderInvoice: OrderInvoiceDiscrepancy[];
  invoiceStatement: InvoiceStatementDiscrepancy[];
}

// Helper to format currency
const formatNumber = (num: number | undefined | null): string => {
  if (num == null || isNaN(num)) return 'N/A';
  return num.toLocaleString('en-GB', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// --- Reusable Helper Functions for Sorting ---
const parseDate = (dateStr: string | undefined | null): Date => {
  if (!dateStr) return new Date(0); // Treat null/undefined as very early date
  // Assuming dates are in a format parsable by New Date, or DD/MM/YYYY
  const parts = String(dateStr).split('/'); // Convert to string before split
  if (parts.length === 3) {
    const [day, month, year] = parts;
    // Ensure parts are valid numbers before creating Date
    const numDay = parseInt(day, 10);
    const numMonth = parseInt(month, 10); // month is 1-indexed
    const numYear = parseInt(year, 10);
    if (!isNaN(numDay) && !isNaN(numMonth) && !isNaN(numYear)) {
        const parsed = new Date(numYear, numMonth - 1, numDay); // JS month is 0-indexed
        if (!isNaN(parsed.getTime())) return parsed;
    }
  }
  const directParse = new Date(dateStr); // Try direct parsing for ISO etc.
  return !isNaN(directParse.getTime()) ? directParse : new Date(0);
};

const getSortIcon = (currentSortField: string, field: string, currentSortOrder: SortOrder) => {
  if (currentSortField !== field) return null;
  return (
    <ArrowUpDown
      className="ml-1 h-4 w-4 text-muted-foreground"
      style={{
        transform: currentSortOrder === 'asc' ? 'rotate(180deg)' : 'none'
      }}
    />
  );
};


// Helper to render item discrepancy details
const renderItemDiscrepancyDetails = (details: ItemDiscrepancyDetail[]) => {
  return (
    <ul className="list-disc list-inside text-xs space-y-1">
      {details.map((detail, index) => (
        <li key={index} className="flex items-start">
           <span className="mr-1.5 mt-0.5 flex-shrink-0">
             {detail.type === 'MISSING_FROM_INVOICE' && <XCircle className="w-3 h-3 text-red-500" />}
             {detail.type === 'EXTRA_ON_INVOICE' && <MinusCircle className="w-3 h-3 text-orange-500" />}
             {detail.type === 'QUANTITY_MISMATCH' && <AlertCircle className="w-3 h-3 text-yellow-600" />}
             {detail.type === 'PRICE_MISMATCH' && <AlertCircle className="w-3 h-3 text-blue-500" />}
           </span>
           <span>
             <span className="font-medium">{detail.description || detail.itemId}</span>:
             {detail.type === 'MISSING_FROM_INVOICE' && ` Missing (Order Qty: ${detail.orderQty ?? 'N/A'})`}
             {detail.type === 'EXTRA_ON_INVOICE' && ` Extra (Invoice Qty: ${detail.invoiceQty ?? 'N/A'})`}
             {detail.type === 'QUANTITY_MISMATCH' && ` Qty mismatch (Order: ${detail.orderQty ?? 'N/A'}, Invoice: ${detail.invoiceQty ?? 'N/A'})`}
              {detail.type === 'PRICE_MISMATCH' && ` Price mismatch (Order: £${formatNumber(detail.orderPrice)}, Invoice: £${formatNumber(detail.invoicePrice)})`}
           </span>
        </li>
      ))}
    </ul>
  );
};


export default function ReconciliationPage() {
  // State now holds the structured discrepancies
  const [discrepancies, setDiscrepancies] = useState<ReconciliationDiscrepancies | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for Order vs Invoice sorting
  const [sortFieldOrderInvoice, setSortFieldOrderInvoice] = useState<SortFieldOrderInvoice>('orderDate');
  const [sortOrderOrderInvoice, setSortOrderOrderInvoice] = useState<SortOrder>('desc');

  // State for Invoice vs Statement sorting
  const [sortFieldInvoiceStatement, setSortFieldInvoiceStatement] = useState<SortFieldInvoiceStatement>('invoiceDate');
  const [sortOrderInvoiceStatement, setSortOrderInvoiceStatement] = useState<SortOrder>('desc');


  useEffect(() => {
    setIsLoading(true);
    fetch('/api/reconciliation/summary') // Endpoint still called summary, but returns detailed structure
      .then(res => {
        if (!res.ok) {
          return res.json().then(errData => {
            throw new Error(errData?.error?.message || 'Failed to fetch reconciliation data');
          }).catch(() => {
            throw new Error(`Failed to fetch reconciliation data (Status: ${res.status})`);
          });
        }
        return res.json();
      })
      .then(data => {
         // Expect the new structure: { success: true, discrepancies: { orderInvoice: [], invoiceStatement: [] } }
         if (data && data.success && data.discrepancies) {
            setDiscrepancies(data.discrepancies);
         } else {
             console.error("Unexpected API response structure:", data);
            throw new Error('Invalid discrepancy data received from server');
         }
        setIsLoading(false);
      })
      .catch(err => {
        console.error("Reconciliation fetch error:", err);
        setError(err.message);
        setIsLoading(false);
      });
  }, []);

  // --- Memoized Sorted Data (Moved Before Early Returns) ---
  const sortedOrderInvoiceDiscrepancies = useMemo(() => {
    if (!discrepancies?.orderInvoice) return [];
    return [...discrepancies.orderInvoice].sort((a, b) => {
      let comparison = 0;
      switch (sortFieldOrderInvoice) {
        case 'orderNo':
          comparison = (a.order.orderNo || '').localeCompare(b.order.orderNo || '');
          break;
        case 'orderDate':
          comparison = parseDate(a.order.dateTime).getTime() - parseDate(b.order.dateTime).getTime();
          break;
        case 'orderSupplier':
          comparison = (a.order.supplier || '').localeCompare(b.order.supplier || '');
          break;
        case 'invoiceNo':
          comparison = (a.invoice?.InvoiceId || '').localeCompare(b.invoice?.InvoiceId || '');
          break;
        case 'orderAmount':
          comparison = (a.order.subTotal || 0) - (b.order.subTotal || 0);
          break;
      }
      return sortOrderOrderInvoice === 'desc' ? -comparison : comparison;
    });
  }, [discrepancies?.orderInvoice, sortFieldOrderInvoice, sortOrderOrderInvoice]);

  const sortedInvoiceStatementDiscrepancies = useMemo(() => {
    if (!discrepancies?.invoiceStatement) return [];
    return [...discrepancies.invoiceStatement].sort((a, b) => {
      let comparison = 0;
      switch (sortFieldInvoiceStatement) {
        case 'invoiceNo':
          comparison = (a.invoice.InvoiceId || '').localeCompare(b.invoice.InvoiceId || '');
          break;
        case 'invoiceDate':
          comparison = parseDate(a.invoice.InvoiceDate).getTime() - parseDate(b.invoice.InvoiceDate).getTime();
          break;
        case 'invoiceSupplier':
          comparison = (a.invoice.VendorName || '').localeCompare(b.invoice.VendorName || '');
          break;
        case 'invoiceAmount':
          comparison = (a.invoice.InvoiceTotal || 0) - (b.invoice.InvoiceTotal || 0);
          break;
      }
      return sortOrderInvoiceStatement === 'desc' ? -comparison : comparison;
    });
  }, [discrepancies?.invoiceStatement, sortFieldInvoiceStatement, sortOrderInvoiceStatement]);

  // --- Loading State ---
  if (isLoading) {
     return (
       <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">Reconciliation Center</h1>
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
       </div>
     );
  }

  // --- Error State ---
  if (error) {
     return (
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">Reconciliation Center</h1>
          <div className="text-red-600 p-4 border border-red-300 bg-red-50 rounded">
            <p className="font-semibold">Error loading reconciliation data:</p>
            <p>{error}</p>
          </div>
        </div>
     );
  }

  // --- No Data State ---
   // Check if discrepancies object exists and has the expected arrays
  if (!discrepancies || !discrepancies.orderInvoice || !discrepancies.invoiceStatement) {
     return (
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">Reconciliation Center</h1>
          <div className="text-gray-600 p-4">
            No reconciliation discrepancy data available or unexpected format.
          </div>
        </div>
     );
  }

  // --- Sorting Handlers ---
  const handleSortOrderInvoice = (field: SortFieldOrderInvoice) => {
    if (sortFieldOrderInvoice === field) {
      setSortOrderOrderInvoice(sortOrderOrderInvoice === 'asc' ? 'desc' : 'asc');
    } else {
      setSortFieldOrderInvoice(field);
      setSortOrderOrderInvoice('desc');
    }
  };

  const handleSortInvoiceStatement = (field: SortFieldInvoiceStatement) => {
    if (sortFieldInvoiceStatement === field) {
      setSortOrderInvoiceStatement(sortOrderInvoiceStatement === 'asc' ? 'desc' : 'asc');
    } else {
      setSortFieldInvoiceStatement(field);
      setSortOrderInvoiceStatement('desc');
    }
  };

  // --- Main Content ---
  // Note: sortedOrderInvoiceDiscrepancies and sortedInvoiceStatementDiscrepancies are now defined above the early returns
  return (
    <div className="w-full max-w-[1400px] mx-auto px-8 py-6">
      <h1 className="text-2xl font-medium text-foreground mb-6">Reconciliation Center</h1>

      <Tabs defaultValue="order-invoice" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-4 border-b">
           <TabsTrigger value="order-invoice" className="py-3 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary">
             Order vs. Invoice ({sortedOrderInvoiceDiscrepancies.length})
           </TabsTrigger>
           <TabsTrigger value="invoice-statement" className="py-3 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary">
             Invoice vs. Statement ({sortedInvoiceStatementDiscrepancies.length})
           </TabsTrigger>
        </TabsList>

        {/* Tab 1: Order vs. Invoice Discrepancies */}
        <TabsContent value="order-invoice" className="mt-6">
          <div className="relative overflow-hidden bg-white rounded-lg shadow-sm border border-border/40">
            <table className="w-full text-sm text-left">
              <caption className="p-4 text-sm text-muted-foreground text-left">Discrepancies found between orders and their corresponding invoices.</caption>
              <thead className="bg-white border-b border-border/40">
                <tr>
                  <th className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground" onClick={() => handleSortOrderInvoice('orderNo')}>
                    <div className="flex items-center">Order Number {getSortIcon(sortFieldOrderInvoice, 'orderNo', sortOrderOrderInvoice)}</div>
                  </th>
                  <th className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground" onClick={() => handleSortOrderInvoice('orderDate')}>
                    <div className="flex items-center">Order Date {getSortIcon(sortFieldOrderInvoice, 'orderDate', sortOrderOrderInvoice)}</div>
                  </th>
                  <th className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground" onClick={() => handleSortOrderInvoice('orderSupplier')}>
                    <div className="flex items-center">Supplier {getSortIcon(sortFieldOrderInvoice, 'orderSupplier', sortOrderOrderInvoice)}</div>
                  </th>
                  <th className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground" onClick={() => handleSortOrderInvoice('invoiceNo')}>
                     <div className="flex items-center">Invoice Number {getSortIcon(sortFieldOrderInvoice, 'invoiceNo', sortOrderOrderInvoice)}</div>
                  </th>
                  <th className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground text-right" onClick={() => handleSortOrderInvoice('orderAmount')}>
                    <div className="flex items-center justify-end">Amount {getSortIcon(sortFieldOrderInvoice, 'orderAmount', sortOrderOrderInvoice)}</div>
                  </th>
                  <th className="px-6 py-3 text-xs font-medium text-muted-foreground">Discrepancy Details</th>
                  <th className="px-6 py-3 text-xs font-medium text-muted-foreground text-right">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sortedOrderInvoiceDiscrepancies.length > 0 ? (
                  sortedOrderInvoiceDiscrepancies.map((disc: OrderInvoiceDiscrepancy) => (
                    <tr key={disc.order.id} className="border-b border-border/40 hover:bg-muted/30 transition-colors">
                      <td className="px-6 py-4 font-medium text-foreground">{disc.order.orderNo || 'N/A'}</td>
                      <td className="px-6 py-4 text-foreground">{disc.order.dateTime ? parseDate(disc.order.dateTime).toLocaleDateString('en-GB') : 'N/A'}</td>
                      <td className="px-6 py-4 text-foreground">{disc.order.supplier || 'N/A'}</td>
                      <td className="px-6 py-4 text-foreground">{disc.invoice?.InvoiceId || (disc.type === 'MISSING_INVOICE' ? <span className="italic text-muted-foreground">None Found</span> : 'N/A')}</td>
                      <td className="px-6 py-4 text-right font-medium text-foreground">£{formatNumber(disc.order.subTotal)}</td>
                      <td className="px-6 py-4 text-foreground">
                         {typeof disc.details === 'string' ? (
                            <span className="flex items-center text-xs text-red-600"> <XCircle className="w-3 h-3 mr-1.5 flex-shrink-0"/> {disc.details}</span>
                         ) : (
                            renderItemDiscrepancyDetails(disc.details)
                         )}
                      </td>
                      <td className="px-6 py-4 text-right">
                        <Button variant="outline" size="sm" onClick={() => alert(`Resolve Order ${disc.order.orderNo || disc.order.id}`)}>Resolve</Button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="px-6 py-8 text-center text-muted-foreground">
                      No Order vs. Invoice discrepancies found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>

        {/* Tab 2: Invoice vs. Statement Discrepancies */}
        <TabsContent value="invoice-statement" className="mt-6">
           <div className="relative overflow-hidden bg-white rounded-lg shadow-sm border border-border/40">
            <table className="w-full text-sm text-left">
               {/* Updated caption */}
               <caption className="p-4 text-sm text-muted-foreground text-left">Discrepancies found between invoices and processed statements.</caption>
              <thead className="bg-white border-b border-border/40">
                <tr>
                  <th className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground" onClick={() => handleSortInvoiceStatement('invoiceNo')}>
                    <div className="flex items-center">Invoice Number {getSortIcon(sortFieldInvoiceStatement, 'invoiceNo', sortOrderInvoiceStatement)}</div>
                  </th>
                  <th className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground" onClick={() => handleSortInvoiceStatement('invoiceDate')}>
                    <div className="flex items-center">Invoice Date {getSortIcon(sortFieldInvoiceStatement, 'invoiceDate', sortOrderInvoiceStatement)}</div>
                  </th>
                  <th className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground" onClick={() => handleSortInvoiceStatement('invoiceSupplier')}>
                    <div className="flex items-center">Supplier {getSortIcon(sortFieldInvoiceStatement, 'invoiceSupplier', sortOrderInvoiceStatement)}</div>
                  </th>
                  <th className="group cursor-pointer px-6 py-3 text-xs font-medium text-muted-foreground text-right" onClick={() => handleSortInvoiceStatement('invoiceAmount')}>
                    <div className="flex items-center justify-end">Amount {getSortIcon(sortFieldInvoiceStatement, 'invoiceAmount', sortOrderInvoiceStatement)}</div>
                  </th>
                  <th className="px-6 py-3 text-xs font-medium text-muted-foreground">Discrepancy Details</th>
                  <th className="px-6 py-3 text-xs font-medium text-muted-foreground text-right">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sortedInvoiceStatementDiscrepancies.length > 0 ? (
                  sortedInvoiceStatementDiscrepancies.map((disc: InvoiceStatementDiscrepancy) => (
                    <tr key={disc.invoice.id} className="border-b border-border/40 hover:bg-muted/30 transition-colors">
                      <td className="px-6 py-4 font-medium text-foreground">{disc.invoice.InvoiceId || 'N/A'}</td>
                      <td className="px-6 py-4 text-foreground">{disc.invoice.InvoiceDate ? parseDate(disc.invoice.InvoiceDate).toLocaleDateString('en-GB') : 'N/A'}</td>
                      <td className="px-6 py-4 text-foreground">{disc.invoice.VendorName || 'N/A'}</td>
                      <td className="px-6 py-4 text-right font-medium text-foreground">£{formatNumber(disc.invoice.InvoiceTotal)}</td>
                       {/* Updated Discrepancy Details rendering */}
                       <td className="px-6 py-4 text-foreground">
                         {disc.type === 'MISSING_FROM_STATEMENT' ? (
                           <span className="flex items-center text-xs text-orange-600">
                             <MinusCircle className="w-3 h-3 mr-1.5 flex-shrink-0"/> {disc.details}
                           </span>
                         ) : disc.type === 'AMOUNT_MISMATCH_STATEMENT' ? (
                           <span className="flex items-center text-xs text-yellow-700"> {/* Different color/icon */}
                             <AlertCircle className="w-3 h-3 mr-1.5 flex-shrink-0"/>
                             {`Amount Mismatch: Invoice £${formatNumber(disc.invoice.InvoiceTotal)}, Statement £${formatNumber(disc.statementAmount)}`}
                           </span>
                         ) : (
                           <span className="text-xs text-muted-foreground">{disc.details}</span> // Fallback
                         )}
                       </td>
                      <td className="px-6 py-4 text-right">
                        <Button variant="outline" size="sm" onClick={() => alert(`Resolve Invoice ${disc.invoice.InvoiceId || disc.invoice.id}`)}>Resolve</Button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-8 text-center text-muted-foreground">
                      No Invoice vs. Statement discrepancies found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
