{"name": "nextjs-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.0.7", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.3.1", "lucide-react": "^0.331.0", "next": "^15.2.1-canary.4", "pdfjs-dist": "^4.8.69", "react": "^18", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.0", "react-dom": "^18", "react-pdf": "^9.2.1", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}