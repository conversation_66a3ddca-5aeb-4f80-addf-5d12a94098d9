/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        primary: {
          DEFAULT: '#4361ee',         // Bright blue like Lendwise
          foreground: '#ffffff',      // White text on primary
        },
        secondary: {
          DEFAULT: '#edf2ff',         // Very light blue for secondary elements
          foreground: '#4361ee',      // Primary blue for text on secondary
        },
        accent: {
          DEFAULT: '#3a86ff',         // Slightly different blue accent
          foreground: '#ffffff',
        },
        destructive: {
          DEFAULT: '#ef4444',         // Modern red
          foreground: '#ffffff',
        },
        muted: {
          DEFAULT: '#f8fafc',         // Very light blue-gray for muted backgrounds
          foreground: '#64748b',      // Slate for muted text
        },
        border: "#e5e7eb",            // Very light gray for subtle borders
        input: "#e5e7eb",             // Light gray for inputs
        ring: "#4361ee",              // Primary blue for focus rings
        background: "#ffffff",        // White background
        foreground: "#111827",        // Very dark gray for text
        popover: {
          DEFAULT: "white",
          foreground: "#111827",
        },
        card: {
          DEFAULT: "white",
          foreground: "#111827",
        },
        success: {
          DEFAULT: '#10b981',         // Green for success states
          foreground: '#ffffff',
        },
        warning: {
          DEFAULT: '#f59e0b',         // Amber for warning states
          foreground: '#ffffff',
        },
      },
      borderRadius: {
        lg: "0.5rem",
        md: "0.375rem",
        sm: "0.25rem",
      },
      boxShadow: {
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.03)',
        'DEFAULT': '0 1px 3px 0 rgba(0, 0, 0, 0.05), 0 1px 2px 0 rgba(0, 0, 0, 0.03)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.03)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.05), 0 10px 10px -5px rgba(0, 0, 0, 0.02)',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        xs: ['0.75rem', { lineHeight: '1rem' }],
        sm: ['0.875rem', { lineHeight: '1.25rem' }],
        base: ['1rem', { lineHeight: '1.5rem' }],
        lg: ['1.125rem', { lineHeight: '1.75rem' }],
        xl: ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' }
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "shimmer": "shimmer 2s infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}