# Reconciliation Plan

## Overview
This plan covers data ingestion validation, statement parsing, backend comparison endpoints, and frontend reconciliation UI.

```mermaid
flowchart TD
  A[Data Ingestion] --> B[Order API]
  A --> C[Invoice Upload / Email]
  A --> D[Statement Upload / Email]
  B --> E[orders → Firestore]
  C --> F[invoices → Firestore]
  D --> G[statements → Firestore]
  
  subgraph Compare
    E --> H[compareInvoiceWithOrder]
    F --> H
    F --> I[compareInvoiceWithStatement]
    G --> I
  end
  
  subgraph Missing_Pieces
    M1[Statement parsing & line-item extraction]
    M2[Link parsed invoices → statement record]
    M3[Reconciliation endpoint & UI]
    M4[Frontend page & components]
    M5[End-to-end tests]
  end
  
  G --> M1
  F --> M2
  H --> M3
  I --> M3
  M3 --> M4
  M4 --> M5
```

## Backend Steps

1. **Verify Ingestion & Storage**  
   - Confirm `scrapeOrders` writes all fields (quantities, prices, dates).  
   - Confirm `processInvoice` extracts invoice number, line items, totals into Firestore.  
   - Confirm `uploadStatement` saves PDF and initializes placeholders (`invoices`, `totalAmount`, `discrepancies`).  

2. **Statement Parsing**  
   - Implement parsing functions (general text + line items) mirroring invoice parsing using OpenAI.  
   - Extract invoice references, amounts, and aggregate `totalAmount`.  
   - Store parsed details in `statements` collection.  

3. **Link Invoices to Statements**  
   - Detect invoice IDs in parsed statement items.  
   - Update each statement document’s `invoices` array with linked invoice IDs.  

4. **Reconciliation Endpoints**  
   - Add new backend endpoints:  
     - `GET /reconciliation/orders-statements`: summary of unmatched records.  
     - `POST /reconciliation/run`: trigger full reconciliation job.  
   - Leverage Firestore queries and existing comparison logic.  

## Frontend Implementation

1. **Pages & Routes**  
   - `src/app/(admin)/reconciliation/page.tsx`: overview dashboard.  
   - `src/app/(admin)/reconciliation/[statementId]/page.tsx`: statement detail view.  

2. **API Hooks**  
   - `useReconciliation()` for summary data.  
   - `useStatementReconciliation(statementId)` for detail data.  

3. **UI Components**  
   - `ReconciliationDashboard`: displays counts, charts (LineChart).  
   - `StatementComparisonTable`: lists invoices vs orders vs statement amounts.  
   - `DiscrepancyModal`: resolves mismatches with notes.  

4. **State Management**  
   - Use React Query for data fetching/mutations.  
   - Context for current statement/filter state.  

## Testing

1. **Unit Tests** for parsing utilities.  
2. **Integration Tests** for backend endpoints.  
3. **E2E Tests** simulating CSV and PDF uploads; validate Firestore and UI outcomes.