const axios = require('axios');
const FormData = require('form-data');
const Imap = require('node-imap');
const { simpleParser } = require('mailparser');
const admin = require('firebase-admin');
const { getFirestore } = require('firebase-admin/firestore');

admin.initializeApp({
  storageBucket: 'invoscan.appspot.com'
});

const imapConfig = {
  user: '<EMAIL>',
  password: '4iTDptS6TwY-p6A',
  host: 'imappro.zoho.eu',
  port: 993,
  tls: true,
  connTimeout: 30000, // Connection timeout in milliseconds, e.g., 30000 for 30 seconds
  authTimeout: 30000, // Authentication timeout in milliseconds, e.g., 30000 for 30 seconds
};

const bucket = admin.storage().bucket();

function checkEmail() {
  const imap = new Imap(imapConfig);

  function openInbox(cb) {
    imap.openBox('INBOX', false, cb);
  }

  imap.once('ready', function () {
    openInbox(function (err, box) {
      if (err) throw err;
      imap.search(['UNSEEN'], function (err, results) {
        if (err || !results.length) {
          console.log('No unread mails');
          imap.end();
          return;
        }
        const fetch = imap.fetch(results, { bodies: '', struct: true, markSeen: true });
        fetch.on('message', function (msg, seqno) {
          msg.on('body', function (stream, info) {
            simpleParser(stream, async (err, mail) => {
              if (err) {
                console.error('Error parsing mail', err);
                return;
              }

              // Processing toAddresses to find the one ending with "@invoscanmail.co.uk"
              let toAddresses = mail.to.value.map(to => to.address.toLowerCase());
              let targetEmail = toAddresses.find(email => email.endsWith("@invoscanmail.co.uk"));

              if (!targetEmail) {
                console.log('No @invoscanmail.co.uk email address found in To field:', toAddresses.join(", "));
                return; // Skip further processing if no target email is found
              }

              let senderEmail = mail.from.value[0].address.toLowerCase();
              let bodyText = mail.text.toLowerCase();

              let supplier = await determineSupplier(senderEmail, bodyText);

              if (mail.attachments && mail.attachments.length > 0) {
                await handleAttachments(mail.attachments, mail, toAddresses, senderEmail, supplier);
              }

              // console.log('Attempting to mark as read:', seqno);
              // imap.setFlags(seqno, '\\Seen', function (err) {
              //   if (err) {
              //     console.error('Failed to mark as read:', err);
              //   } else {
              //     console.log('Successfully marked as read:', seqno);
              //   }
              // });
            });
          });
        });
        fetch.once('end', function () {
          imap.expunge(function (err) {
            if (err) console.error('Failed to expunge mailbox:', err);
            imap.end();
          });
        });        
      });
    });
  });

  imap.once('error', function (err) {
    console.error('IMAP error:', err);
    imap.end(); // Ensure connection is closed even on error
  });

  imap.once('end', function () {
    console.log('Connection ended');
  });

  imap.connect();
}

async function handleAttachments(attachments, mail, toAddresses, senderEmail, supplier) {
  for (const attachment of attachments) {
    // Check if the attachment is a PDF by examining its content type
    if (attachment.contentType === 'application/pdf') {
      const file = bucket.file(`attachments/${attachment.filename}`);
      await file.save(attachment.content, { metadata: { contentType: attachment.contentType } });
      console.log(`Attachment ${attachment.filename} saved to Firebase Storage.`);

      const docRef = admin.firestore().collection('emails').doc();
      await docRef.set({
        to: toAddresses,
        from: senderEmail,
        subject: mail.subject,
        supplier: supplier,
        attachment: `attachments/${attachment.filename}`
      });
      console.log('Firestore document created with email details.');
      // Call the OCR API function here
      await callAPIBySupplier(supplier.id, toAddresses, attachment);
    } else {
      console.log(`Skipping attachment ${attachment.filename} as it is not a PDF.`);
    }
  }
}

async function determineSupplier(senderEmail, bodyText) {
  const db = getFirestore();
  const suppliersRef = db.collection('suppliers');
  const snapshot = await suppliersRef.get();  // Fetch all supplier documents

  let matchedSupplier = null;
  try {
    snapshot.forEach(doc => {
      const supplierData = doc.data();
      // Check if senderEmail or bodyText includes the supplier's email
      if (senderEmail.includes(supplierData.email) || bodyText.includes(supplierData.email)) {
        matchedSupplier = {
          name: supplierData.name,  // Return the supplier's name
          id: supplierData.id       // Return the supplier's ID
        };
        // Exit the loop if a match is found since only one match is expected
        return false;
      }
    });
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    return null;
  }

  return matchedSupplier;  // Return null if no match is found
}

async function callAPIBySupplier(supplierId, toEmail, attachment) {
  // Extract the client ID from the "To" email address
  const clientId = toEmail;

  // Construct the API endpoint with query parameters
  const apiUrl = `http://************:4000/api/form/analyzeemail?clientId=${encodeURIComponent(clientId)}&supplierId=${encodeURIComponent(supplierId)}&type=invoice`;

  // Create a form and append the PDF file
  const form = new FormData();
  form.append('files[]', attachment.content, { filename: attachment.filename, contentType: attachment.contentType });

  try {
    // Make the POST request
    const response = await axios.post(apiUrl, form, {
      headers: {
        ...form.getHeaders(),
      },
    });
    console.log('API Response:', response.data);
  } catch (error) {
    console.error('Failed to send API request:', error);
  }
}

module.exports = checkEmail;
