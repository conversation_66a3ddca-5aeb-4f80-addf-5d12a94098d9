import { firebaseInitializationPromise } from './src/config/firebase.js';

async function writeTestInvoice() {
  try {
    // Wait for Firebase to be initialized
    const { db } = await firebaseInitializationPromise;
    
    console.log('Firebase initialized, checking if testInvoice exists...');
    const testInvoiceRef = db.collection('invoices').doc("INV-2024-001");
    const doc = await testInvoiceRef.get();

    if (doc.exists) {
      console.log('Current testInvoice data:', doc.data());
    } else {
      console.log('testInvoice does not exist, creating it...');
      
      const mockItems = [
        {
          Amount: 250.00,
          unitPrice: 2.50,
          Description: "Paracetamol 500mg Tablets",
          Quantity: 100,
          itemId: 1,
          pipCode: "PAR500",
          packSize: "100 tablets"
        },
        {
          Amount: 237.50,
          unitPrice: 4.75,
          Description: "Amoxicillin 250mg Capsules",
          Quantity: 50,
          itemId: 2,
          pipCode: "AMO250",
          packSize: "50 capsules"
        },
        {
          Amount: 243.75,
          unitPrice: 3.25,
          Description: "Ibuprofen 200mg Tablets",
          Quantity: 75,
          itemId: 3,
          pipCode: "IBU200",
          packSize: "75 tablets"
        }
      ];

      const totalAmount = mockItems.reduce((sum, item) => sum + item.Amount, 0);

      await testInvoiceRef.set({
        InvoiceNumber: "INV-2024-001",
        InvoiceId: "INV-2024-001", // Added to match interface
        InvoiceDate: new Date(),
        DueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        InvoiceTotal: totalAmount,
        orderReconciled: false,
        statementReconciled: false,
        Items: mockItems,
        // Added required fields from interface
        VendorName: "Phoenix",
        CustomerName: "St Mary's Pharmacy",
        CustomerAddress: "123 High Street, London, SW1A 1AA",
        CustomerAddressRecipient: "St Mary's Pharmacy Ltd",
        CustomerId: "SMP123",
        ShippingAddress: "123 High Street, London, SW1A 1AA",
        ShippingAddressRecipient: "St Mary's Pharmacy",
        VendorAddress: "Phoenix House, Forgewood Industrial Estate, Motherwell, ML1 4YT",
        VendorAddressRecipient: "Phoenix Healthcare Distribution Ltd",
        VendorTaxId: "GB123456789",
        processedAt: new Date(), // Added processedAt field
        filePath: `invoices/${Date.now()}-test-invoice.pdf`,
        PaymentTerms: "Net 30",
        Status: "Pending",
        Notes: "Test invoice with mock pharmaceutical items",
        VAT: totalAmount * 0.20, // 20% VAT
        Currency: "GBP"
      });
      
      console.log('✅ Test invoice written successfully.');
    }

    // Now get all documents in the invoices collection to verify
    console.log('\nChecking all documents in invoices collection:');
    const querySnapshot = await db.collection('invoices').get();
    console.log(`Total documents found: ${querySnapshot.size}`);
    querySnapshot.forEach(doc => {
      console.log('Document ID:', doc.id);
      console.log('Document data:', doc.data());
    });

  } catch (error) {
    console.error('Error:', error);
  }
}

writeTestInvoice();
