const fs = require('fs');
const pdf = require('pdf-parse');
const { PDFDocument } = require('pdf-lib');
const axios = require('axios');
const { log } = require('console');
const path = require('path');

// const filePath = 'files/72975-6623b58a3cc4c.pdf';  // Update this path
const openAIKey = '';

async function readPdfFile(filePath) {
    try {
        const dataBuffer = await fs.promises.readFile(filePath);
        const pdfDoc = await PDFDocument.load(dataBuffer, { ignoreEncryption: true });
        const numPages = pdfDoc.getPageCount();
        let pages = [];

        for (let i = 0; i < numPages; i++) {
            try {
                const subDocument = await PDFDocument.create();
                const [copiedPage] = await subDocument.copyPages(pdfDoc, [i]);
                subDocument.addPage(copiedPage);
                const pdfBytes = await subDocument.save();
                const data = await pdf(pdfBytes);
                pages.push(data.text);
                
            } catch (error) {
                console.error(`Error processing page ${currentPage}: ${error.message}`);
                break; // Exit the loop in case of an error
            }
        }


        return pages; // Array of text content from each page
    } catch (error) {
        console.error('Failed to read PDF file:', error);
        throw error;
    }
}

async function parseGeneralTextWithOpenAI(text) {
    const endpoint = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${openAIKey}`
    };
    const data = {
        model: "gpt-4o-mini",
        messages: [{
            "role": "user", "content": `strictly return the json-style format of the statement of invoices hereafter using the following rules:
            - Do not start the file with "3 back-ticks json". 
            - Give full output, do not skip any lines and do not abridge.
            - Disregard the lineitems
            - here is the output format: 
        {
        "data": {
                "CustomerName": ,
                "CustomerAddress": ,
                "InvoiceDate": dd/mm/yyyy, (this is the date the statment was issued)
                "DueDate": ,
                "TotalTax": (this will be the sum of the statments without Tax),
                "TotalValue": (this will be the sum of the statments with Tax)
            }
        }
                ` + text
        }],
        temperature: 0.0
    };

    try {
        const response = await axios.post(endpoint, data, { headers });
        return response.data;
    } catch (error) {
        console.error('Error calling OpenAI API:', error.response ? error.response.data : error.message);
        return null;
    }
}

async function parseLineItemWithOpenAI(text) {
    const endpoint = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${openAIKey}`
    };
    const data = {
        model: "gpt-4o-mini",
        messages: [{
            "role": "user", "content": `strictly return a json-style of the statement of invoices hereafter using the following rules:
            - Do not start the file with "3 back-ticks json".
            - Only read line items 
            - Give full output, do not skip any lines and do not abridge.
            - here is the output format: 
        {
            "Items": [ (these are the invoice details in the line items, this should match the number of dates)
                {
                    "Amount": (net value without tax, no double quotes),
                    "InvoiceNumber": (string, with double quotes),
                    "Date": dd/mm/YYYY 
                }
            ]
        }
                ` + text
        }],
        temperature: 0.0
    };

    try {
        const response = await axios.post(endpoint, data, { headers });
        return response.data;
    } catch (error) {
        console.error('Error calling OpenAI API:', error.response ? error.response.data : error.message);
        return null;
    }
}

async function processInvoice(filePath) {
    try {
        const pages = await readPdfFile(filePath);
        const results = [];
        const fileHeader = await parseGeneralTextWithOpenAI(pages[0]);

        for (let pageText of pages) {
            const response = await parseLineItemWithOpenAI(pageText);
            if (response && response.choices && response.choices.length > 0) {
                const parsedContent = JSON.parse(response.choices[0].message.content);
                results.push(...parsedContent.Items);
            }
        }
        finalResults = await combineResults(fileHeader.choices[0].message.content, results)
        const outputFilePath = path.join(__dirname, 'files', 'out', 'statements', `${path.basename(filePath)}.txt`);
        fs.writeFileSync(outputFilePath, JSON.stringify(finalResults, null, 4), { encoding: 'utf8' })
        return finalResults;
    } catch (error) {
        console.error('Failed to process invoice:', error);
    }
}


// Path to the invoices directory
const invoicesDir = path.join(__dirname, 'files', 'in', 'statements');

fs.readdir(invoicesDir, (err, files) => {
    if (err) {
        return console.error('Failed to read directory:', err);
    }

    files.forEach(file => {
        const filePath = path.join(invoicesDir, file);
        processInvoice(filePath).then((parsedData) => {
            console.log('Final Parsed Data:', file);
        }).catch((error) => {
            console.error('An error occurred:', error);
        });
    });
});

async function combineResults(fileHeader, pageResults) {
    const combinedResult = JSON.parse(fileHeader);
    combinedResult.data.Items = [];
    combinedResult.data.Items.push(...pageResults);
    return combinedResult;
}
