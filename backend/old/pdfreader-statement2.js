const fs = require('fs');
const pdf = require('pdf-parse');
const axios = require('axios');
const { log } = require('console');
const path = require('path');

const openAIKey = '';

async function readPdfFile(filePath) {
    try {
        const dataBuffer = await fs.promises.readFile(filePath);
        return pdf(dataBuffer);
    } catch (error) {
        console.error('Failed to read PDF file:', error);
        throw error;
    }
}

async function parseTextWithOpenAI(text) {
    const endpoint = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${openAIKey}`
    };
    const data = {
        model: "gpt-3.5-turbo",
        messages: [{
            "role": "user", "content": `strictly return a json-style of the statement of invoices hereafter using the following rules:
            - Do not start the file with "3 back-ticks json". 
            - Give full output, do not skip any lines and do not abridge.
            - here is the output format: 
        {
        "data": {
                "CustomerName": ,
                "CustomerAddress": ,
                "Items": [ (these are the invoice details in the line items, this should match the number of dates, also note they sometimes go over more than one page)
                    {
                        "Amount": ,
                        "InvoiceNumber": ,
                        "Date": dd/mm/yyyy 
                    }
                ],
                "InvoiceDate": dd/mm/yyyy, (this is the date the statment was issued)
                "DueDate": ,
                "TotalTax": (this will be the sum of the statments without Tax),
                "TotalValue": (this will be the sum of the statments with Tax)
            }
        }
                ` + text
        }],
        temperature: 0.0
    };

    try {
        const response = await axios.post(endpoint, data, { headers });
        return response.data;
    } catch (error) {
        console.error('Error calling OpenAI API:', error.response ? error.response.data : error.message);
        return null;
    }
}

async function processInvoice(filePath) {
    try {
        const pdfData = await readPdfFile(filePath);
        const openAIResponse = await parseTextWithOpenAI(pdfData.text);
        if (openAIResponse && openAIResponse.choices && openAIResponse.choices.length > 0) {
            const outputFilePath = path.join(__dirname, 'files', 'out', 'statements', `${path.basename(filePath)}.txt`);
            fs.writeFileSync(outputFilePath, openAIResponse.choices[0].message.content, { encoding: 'utf8' })
            return JSON.parse(openAIResponse.choices[0].message.content);
        }
    } catch (error) {
        console.error('Failed to process invoice:', error);
    }
}


// Path to the invoices directory
const invoicesDir = path.join(__dirname, 'files', 'in', 'statements');

fs.readdir(invoicesDir, (err, files) => {
    if (err) {
        return console.error('Failed to read directory:', err);
    }

    files.forEach(file => {
        const filePath = path.join(invoicesDir, file);
        processInvoice(filePath).then((parsedData) => {
            console.log('Final Parsed Data:', parsedData);
        }).catch((error) => {
            console.error('An error occurred:', error);
        });
    });
});