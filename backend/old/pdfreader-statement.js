const { PDFDocument } = require('pdf-lib');
const fs = require('fs-extra');
const { convert } = require('pdf-poppler');
const axios = require('axios');
const path = require('path');
const sharp = require('sharp');

const openAIKey = '';

async function deleteAllFilesInFolder(directory) {
    try {
        // Read the contents of the directory
        const files = await fs.promises.readdir(directory);

        // Create an array of promises for file deletions
        const deletePromises = files.map(file => {
            const filePath = path.join(directory, file);
            return fs.promises.stat(filePath).then(stats => {
                if (stats.isFile()) {
                    return fs.promises.unlink(filePath); // Delete file
                }
            });
        });

        // Wait for all delete operations to complete
        await Promise.all(deletePromises);
        console.log('All files have been deleted.');
    } catch (error) {
        console.error('Error deleting files:', error);
    }
}

// Function to convert Base64 JPEG to Base64 WEBP
async function convertJpegToWebp(base64Jpeg) {
    // Strip off the data URL header from the base64 string if present
    const base64Data = base64Jpeg.replace(/^data:image\/\w+;base64,/, '');

    // Convert the base64 string to a Buffer
    const inputBuffer = Buffer.from(base64Data, 'base64');

    try {
        // Convert the image to WEBP using sharp
        const outputBuffer = await sharp(inputBuffer)
            .webp({ quality: 80 }) // Adjust quality as needed
            .toBuffer();

        // Convert the output Buffer back to a Base64 string
        const base64Webp = outputBuffer.toString('base64');

        // Create the WEBP image URL
        return `data:image/webp;base64,${base64Webp}`;
    } catch (error) {
        console.error('Error converting image:', error);
        return null;  // Return null or handle error appropriately
    }
}

async function getPdfInfo(filePath) {
    const pdfBuffer = await fs.readFile(filePath);
    const pdfDoc = await PDFDocument.load(pdfBuffer, { ignoreEncryption: true });
    const pageCount = pdfDoc.getPageCount()
    return pageCount;
}

async function convertAndProcessPdf(filePath) {
    const pageCount = await getPdfInfo(filePath);
    console.log(pageCount)
    let imageUrls = [];

    for (let i = 1; i <= pageCount; i++) {
        let opts = {
            format: 'jpg',
            out_dir: 'files/tmp',
            out_prefix: `page`,
            page: i
        };

        await convert(filePath, opts).catch(error => console.error('Error converting PDF:', error));
        
        const imagePath = `files/tmp/page-${i}.jpg`;
        const imageBuffer = await fs.readFile(imagePath);
        const base64Image = imageBuffer.toString('base64');
        const imageUrl = `data:image/jpg;base64,${base64Image}`;
        // const convertedImageUrl = await convertJpegToWebp(imageUrl);
        // imageUrls.push(convertedImageUrl);
        imageUrls.push(imageUrl)
    }

    // After all images are processed, prepare and send the API call
    await sendToApi(imageUrls, filePath);
}

async function sendToApi(imageUrls, filePath) {
    const endpoint = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${openAIKey}`
    };

    const prompt = [
        {
            type: "text", text: `strictly return a json-style of the statement of invoices hereafter using the following rules:
            - Do not start the file with "3 back-ticks json". 
            - Give full output, do not skip any lines and do not abridge.
            - Do not give three dots to indicate missing data, in json file add a key saying complete/incomplete.
            - here is the output format: 
        {
        "data": {
                "CustomerName": ,
                "CustomerAddress": ,
                "Items": [ (these are the invoice details in the line items, this should match the number of dates, also note they sometimes go over more than one page)
                    {
                        "Amount": ,
                        "InvoiceNumber": ,
                        "Date": dd/mm/yyyy 
                    }
                ],
                "InvoiceDate": dd/mm/yyyy, (this is the date the statment was issued)
                "DueDate": ,
                "TotalTax":
            }
        }
        ` }]

    imageUrls.forEach(url => {
        prompt.push({
            type: "image_url",
            image_url: { url }
        }
        );
    });
    // console.log(prompt);
    const data = {
        model: "gpt-4o-mini",
        messages: [{
            "role": "user", "content": prompt

        }]
    };

    try {
        const response = await axios.post(endpoint, data, { headers });
        const outputFilePath = path.join(__dirname, 'files', 'out', 'statements', `${path.basename(filePath)}.txt`);
        fs.writeFileSync(outputFilePath, response.data.choices[0].message.content, { encoding: 'utf8' })
        del = await deleteAllFilesInFolder("files/tmp");
        return JSON.parse(response.data.choices[0].message.content);
    } catch (error) {
        console.error('Error calling OpenAI API:', error.response ? error.response.data : error.message);
    }
}

// Path to the invoices directory
const invoicesDir = path.join(__dirname, 'files', 'in', 'statements');

fs.readdir(invoicesDir, async (err, files) => {
    if (err) {
        return console.error('Failed to read directory:', err);
    }

    for (const file of files) {
        const filePath = path.join(invoicesDir, file);
        try {
            const parsedData = await convertAndProcessPdf(filePath);
            console.log(file, 'parsed');
        } catch (error) {
            console.error('An error occurred:', error);
        }
    };
});