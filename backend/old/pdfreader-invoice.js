const fs = require('fs');
const pdf = require('pdf-parse');
const { PDFDocument } = require('pdf-lib');
const axios = require('axios');
const { log } = require('console');
const path = require('path');

// const filePath = 'files/72975-6623b58a3cc4c.pdf';  // Update this path
const openAIKey = '';

async function readPdfFile(filePath) {
    try {
        const dataBuffer = await fs.promises.readFile(filePath);
        const pdfDoc = await PDFDocument.load(dataBuffer);
        const numPages = pdfDoc.getPageCount();
        let pages = [];

        for (let i = 0; i < numPages; i++) {
            try {
                const subDocument = await PDFDocument.create();
                const [copiedPage] = await subDocument.copyPages(pdfDoc, [i]);
                subDocument.addPage(copiedPage);
                const pdfBytes = await subDocument.save();
                const data = await pdf(pdfBytes);
                pages.push(data.text);
                
            } catch (error) {
                console.error(`Error processing page ${currentPage}: ${error.message}`);
                break; // Exit the loop in case of an error
            }
        }


        return pages; // Array of text content from each page
    } catch (error) {
        console.error('Failed to read PDF file:', error);
        throw error;
    }
}

async function parseGeneralTextWithOpenAI(text) {
    const endpoint = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${openAIKey}`
    };
    const data = {
        model: "gpt-4o-mini",
        messages: [{
            "role": "user", "content": `strictly return a json (do not start with 3 back-ticks json) of the invoice hereafter using the following format (disregard line items):
                {
                    "data": {
                        "VendorName": (this is the name of the vendor: AAH, Alliance, BNS, Colorama, Trident, Sigma, etc.),
                        "CustomerName": ,
                        "InvoiceDate": dd/mm/yyyy,
                        "DueDate": ,
                        "InvoiceId": ,
                        "CustomerAddress": ,
                        "CustomerAddressRecipient": ,
                        "CustomerId": ,
                        "ShippingAddress": ,
                        "ShippingAddressRecipient": (this is the name of the pharmacy or person receiving the goods),
                        "VendorAddress": ,
                        "VendorAddressRecipient": ,
                        "VendorTaxId": ,
                        "InvoiceTotal": (this is the net total without tax, no double quotes)
                    }
                }       

                ` + text
        }],
        temperature: 0.0
    };

    try {
        const response = await axios.post(endpoint, data, { headers });
        return response.data;
    } catch (error) {
        console.error('Error calling OpenAI API:', error.response ? error.response.data : error.message);
        return null;
    }
}

async function parseLineItemWithOpenAI(text) {
    const endpoint = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${openAIKey}`
    };
    const data = {
        model: "gpt-4o-mini",
        messages: [{
            "role": "user", "content": `strictly return a json (do not start with 3 back-ticks json) of the line items of the invoice hereafter using the following format:
                {
                    Items: [{
                                "Amount": (this is the total price after tax, 2 decimal digits after points. This is the net total without tax, no double quotes),
                                "unitPrice": (this is the price of each single unit without tax, 2 decimal digits after points. Make sure you don't read the net price -total without tax- by mistake. To double check, you should get the same number if you divide the amount without tax by the quantity. No double quotes),
                                "Description": ,
                                "Quantity": (please double check the quantity by dividing Amount by Unit Price after removing tax. Only double check don't change the values. no double quotes),
                                "itemId": (this is its sequential order on the invoice 1,2,3, etc. no double quotes),
                                "pipCode": (this is the barcode of the product, typically on the column next to it or under it, it is never a float, it doesn't repeat. For each supplier it looks different:
                                - for AAH, it is 8 characters long, first 3 characters and last character are letters, while the other 4 are numbers
                                - for Alliance, it is 4 digits then dash then 3 digits (remove the dash)
                                - for Bestway, it is 6 characters long, first character is a letter, while the remaining 5 are numbers
                                - for BNS, there is no pipcode, so leave blank
                                - for OTC, it is the first 7 numbers in the line item
                                - for Sigma, there is no pipcode, so leave blank
                                - for Trident, it is 8 characters long, first 3 characters and last character are letters, while the other 4 are numbers
                                - for Wardles, there is no pipcode, so leave blank
                                ),
                                "packSize": (this is the size of the pack, it usually is an int with a unit of measurement next to gm, tab, etc)
                            }]
                }       

                ` + text
        }],
        temperature: 0.0
    };

    try {
        const response = await axios.post(endpoint, data, { headers });
        return response.data;
    } catch (error) {
        console.error('Error calling OpenAI API:', error.response ? error.response.data : error.message);
        return null;
    }
}

async function processInvoice(filePath) {
    try {
        const pages = await readPdfFile(filePath);
        const results = [];
        const fileHeader = await parseGeneralTextWithOpenAI(pages[0]);

        for (let pageText of pages) {
            const response = await parseLineItemWithOpenAI(pageText);
            if (response && response.choices && response.choices.length > 0) {
                const parsedContent = JSON.parse(response.choices[0].message.content);
                results.push(...parsedContent.Items);
            }
        }
        finalResults = await combineResults(fileHeader.choices[0].message.content, results)
        const outputFilePath = path.join(__dirname, 'files', 'out', 'invoices', `${path.basename(filePath)}.txt`);
        fs.writeFileSync(outputFilePath, JSON.stringify(finalResults, null, 4), { encoding: 'utf8' })
        return finalResults;
    } catch (error) {
        console.error('Failed to process invoice:', error);
    }
}


// Path to the invoices directory
const invoicesDir = path.join(__dirname, 'files', 'in', 'invoices');

fs.readdir(invoicesDir, (err, files) => {
    if (err) {
        return console.error('Failed to read directory:', err);
    }

    files.forEach(file => {
        const filePath = path.join(invoicesDir, file);
        processInvoice(filePath).then((parsedData) => {
            console.log('Final Parsed Data:', file);
        }).catch((error) => {
            console.error('An error occurred:', error);
        });
    });
});

async function combineResults(fileHeader, pageResults) {
    const combinedResult = JSON.parse(fileHeader);
    combinedResult.data.Items = [];
    combinedResult.data.Items.push(...pageResults);
    return combinedResult;
}
