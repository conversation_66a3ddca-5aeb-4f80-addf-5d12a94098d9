import { firebaseInitializationPromise } from '../config/firebase.js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { checkEmails } from './emailController.js';
import axios from 'axios'; // Import axios
import { readPdfFile } from '../utils/pdfUtils.js'; // Import pdfUtils

// Function to parse statement text using OpenAI
async function parseStatementWithOpenAI(text) {
    const endpoint = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}` // Ensure OPENAI_API_KEY is set
    };
    const data = {
        model: "gpt-4o-mini", // Or your preferred model
        messages: [{
            "role": "user",
            "content": `Strictly return a JSON object (do not start with \`\`\`json) containing all details extracted from the following statement text, nested under a "data" key. Use the specified format. All fields are important.
                - Ensure all amounts are numbers (no currency symbols, no double quotes).
                - All dates must be in "dd/mm/yyyy" format. If a date is missing, ambiguous, or in a different format and cannot be reliably converted, use null for that date field.
                - If any other field is not found, use null for its value.

                Format:
                {
                  "data": {
                    "CustomerName": "string (This is the name of the entity the statement is addressed to, e.g., the pharmacy name)",
                    "CustomerAddress": "string (The full address of the customer/pharmacy)",
                    "Items": [
                      {
                        "Amount": number (Amount of the item listed),
                        "InvoiceNumber": "string (CRITICAL unique ID for invoice/credit. PDF text extraction may not perfectly align columns. CAREFULLY follow the logic in the 'IMPORTANT EXAMPLE for InvoiceNumber extraction' (provided before the 'Statement Text:' section) to determine which column's data to use, especially prioritizing 'Document' headers. SUPPLIER-SPECIFIC PATTERN CHECK: If the 'SupplierName' (extracted elsewhere in this JSON) is identified as 'AAH', the InvoiceNumber for AAH items MUST conform to a pattern of 8 digits followed by a single uppercase letter, e.g., '12345678A'. Please verify this pattern for AAH statement items when extracting the InvoiceNumber. If multiple columns contain potential IDs for an AAH item, this pattern is a key factor in selecting the correct one.)",
                        "Date": "string (Date of the item, dd/mm/yyyy or null)",
                        "Type": "string (Must be 'invoice' if it's an invoice/debit, or 'credit' if it's a credit note/credit. If unsure, default to 'invoice')"
                      }
                    ],
                    "InvoiceDate": "string (This is the date the statement was issued, dd/mm/yyyy or null)",
                    "DueDate": "string (The due date for the statement balance, dd/mm/yyyy or null)",
                    "TotalTax": number (Total tax amount on the statement, if specified, otherwise null),
                    "TotalAmount": number (This is the final total amount due on the statement, or null if not found),
                    "SupplierName": "string (This is the name of the company that issued the statement, e.g., AAH, Alliance, Phoenix)"
                  }
                }

                IMPORTANT EXAMPLE for InvoiceNumber extraction:
                If a table in the statement has columns like "Date", "Document", "Type", "Reference", "Amount", etc.:
                And a row from that table looks like: "01/04/2025 05235018C INV. (DCSMP29837AAH2) 100.00"
                - The value "05235018C" is under the "Document" column.
                - The value "(DCSMP29837AAH2)" is under the "Reference" column.
                In this case, for the "InvoiceNumber" field, YOU MUST EXTRACT "05235018C" because the "Document" column takes precedence over "Reference" for identifying the primary document ID.
                General rule: Prioritize values from columns explicitly labeled 'Document', 'Document No.', 'Doc No.', 'Invoice No.', 'Inv No.', 'Invoice Number', or similar. AVOID using data from a column titled 'Reference' or 'Ref No' for InvoiceNumber IF a more specific document/invoice identifier column (like 'Document') is present.

                Now, process the following full statement text:
                Statement Text:
                ${text}`
        }],
        temperature: 0.0 // Low temperature for deterministic output
    };

    try {
        console.log("Sending statement text to OpenAI for parsing...");
        // Add a timeout (e.g., 60000ms = 60 seconds)
        const response = await axios.post(endpoint, data, { headers, timeout: 60000 });
        console.log("Received response from OpenAI.");
        
        const messageContent = response.data?.choices?.[0]?.message?.content;

        if (messageContent) {
            // Attempt to parse the JSON content
            try {
                // Clean potential markdown ```json ... ```
                const cleanedContent = messageContent.replace(/^```json\s*|\s*```$/g, '');
                console.log("Attempting to parse cleaned OpenAI content:", cleanedContent); // Log before parsing
                const parsedJson = JSON.parse(cleanedContent);
                console.log("Successfully parsed OpenAI response. Parsed JSON:", JSON.stringify(parsedJson, null, 2)); // Log the successfully parsed JSON
                return parsedJson;
            } catch (parseError) {
                console.error('Error parsing JSON from OpenAI response:', parseError.message);
                console.error('Raw OpenAI response content:', messageContent);
                // Throw specific error for parsing failure
                throw new Error(`Failed to parse JSON from OpenAI response: ${parseError.message}`);
            }
        } else {
             // Log the entire response data if structure is invalid
            console.error('Invalid or empty response structure from OpenAI. Full response:', JSON.stringify(response.data, null, 2));
            throw new Error('Invalid or empty content in OpenAI response.');
        }
    } catch (error) {
        // Log more specific error details if available
        const errorMessage = error.response ? JSON.stringify(error.response.data) : error.message;
        const errorDetails = error.isAxiosError ? `AxiosError: ${error.code}` : error.stack;
        console.error(`Error calling OpenAI API for statement parsing: ${errorMessage}`);
        console.error(`Error details: ${errorDetails}`);
        // Re-throw a more informative error
        throw new Error(`OpenAI API request failed: ${errorMessage}`);
    }
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Get all statements
const getAllStatements = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { supplier, startDate, endDate } = req.query;
    
    // Start with base query
    let query = db.collection('statements').where('userId', '==', userId);
    
    // Apply filters if provided
    if (supplier) {
      query = query.where('supplier', '==', supplier);
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const statements = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Apply date filtering in memory
      if (startDate || endDate) {
        const periodStart = data.period?.start?.toDate() || new Date(0);
        
        if (startDate && new Date(startDate) > periodStart) {
          return; // Skip this item
        }
        
        if (endDate && new Date(endDate) < periodStart) {
          return; // Skip this item
        }
      }
      
      statements.push({
        id: doc.id,
        ...data,
        period: {
          start: data.period?.start?.toDate()?.toISOString() || null,
          end: data.period?.end?.toDate()?.toISOString() || null
        }
      });
    });
    
    res.json({
      success: true,
      count: statements.length,
      statements
    });
  } catch (error) {
    console.error('Error getting statements:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get statements',
        details: error.message
      }
    });
  }
};

// Get specific statement
const getStatementById = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const statementId = req.params.id;
    
    // Get the statement
    const statementDoc = await db.collection('statements').doc(statementId).get();
    
    if (!statementDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Statement not found'
        }
      });
    }
    
    const statementData = statementDoc.data();
    
    // Check if the statement belongs to the current user
    if (statementData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }
    
    // Ensure all relevant dates are converted to ISO strings
    const processedStatementData = {
      ...statementData,
      id: statementDoc.id,
      period: {
        // Check for nested period object first, then top-level, then default to null
        start: (statementData.period?.start?.toDate()?.toISOString()) || (statementData.periodStart?.toDate()?.toISOString()) || null,
        end: (statementData.period?.end?.toDate()?.toISOString()) || (statementData.periodEnd?.toDate()?.toISOString()) || null,
      },
      createdAt: statementData.createdAt?.toDate()?.toISOString() || null,
      updatedAt: statementData.updatedAt?.toDate()?.toISOString() || null,
      statementDate: statementData.statementDate?.toDate()?.toISOString() || null,
      dueDate: statementData.dueDate?.toDate()?.toISOString() || null,
      invoices: (statementData.invoices || []).map((inv, index) => {
        console.log(`Backend: Processing invoice item ${index}, raw inv.Date from Firestore:`, inv.Date, `(type: ${typeof inv.Date})`);
        // Check if inv.Date is a Firestore Timestamp by checking for toDate method
        const isTimestamp = inv.Date && typeof inv.Date.toDate === 'function';
        let itemDateISO = null;
        if (isTimestamp) {
          itemDateISO = inv.Date.toDate().toISOString();
        } else if (inv.Date) { // If it's not a Timestamp but exists (e.g., already a string)
          itemDateISO = inv.Date; // Pass it as is, assuming it might be an ISO string
          console.log(`Backend: Invoice item ${index}, inv.Date is not a Timestamp, passing as is:`, inv.Date);
        }
        // If inv.Date was null or undefined, itemDateISO remains null
        console.log(`Backend: Processing invoice item ${index}, final itemDateISO to be sent:`, itemDateISO);
        return {
          ...inv,
          Date: itemDateISO,
          Type: inv.Type || 'invoice' // Ensure Type is passed through
        };
      }),
      // Remove the explicit mapping of statementData.credits,
      // as all items are now in statementData.invoices with a Type.
      // The spread ...statementData will not include 'credits' if it's removed during upload/parsing.
      // If 'credits' might still exist on older documents, explicitly remove it here:
      credits: undefined 
    };
    
    // Clean up credits if it's undefined
    if (processedStatementData.credits === undefined) {
      delete processedStatementData.credits;
    }

    res.json({
      success: true,
      statement: processedStatementData
    });
  } catch (error) {
    console.error('Error getting statement:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get statement',
        details: error.message
      }
    });
  }
};

// Upload statement PDF/CSV
const uploadStatement = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    // Supplier, periodStart, periodEnd will be parsed from PDF, remove from req.body dependency
    // const { supplier, periodStart, periodEnd } = req.body;
    
    // Validation for file presence remains important
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Supplier, period start, and period end are required'
        }
      });
    }
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Statement file is required'
        }
      });
    }

    // Validate PDF header (similar to invoice controller)
    const pdfHeader = req.file.buffer.slice(0, 5).toString();
    if (!pdfHeader.includes('%PDF-')) {
        return res.status(400).json({ success: false, error: { message: 'Invalid PDF file: Missing PDF header' } });
    }

    console.log(`Processing uploaded statement: ${req.file.originalname}`);

    // 1. Read PDF text
    const pages = await readPdfFile(req.file.buffer);
    const fullText = pages.join('\n\n'); // Combine text from all pages

    // 2. Parse text with OpenAI
    console.log("Parsing statement text with OpenAI...");
    const parsedContent = await parseStatementWithOpenAI(fullText); // Renamed for clarity
    console.log("Statement parsing complete. Raw content from parseStatementWithOpenAI:", JSON.stringify(parsedContent, null, 2));


    // Validate parsed data - check for the nested "data" object
    if (!parsedContent || typeof parsedContent !== 'object' || !parsedContent.data || typeof parsedContent.data !== 'object') {
        console.error("Invalid parsed data structure from OpenAI. Expected a 'data' object. Received:", JSON.stringify(parsedContent, null, 2));
        throw new Error('Failed to get valid parsed data structure from OpenAI (missing data object).');
    }
    const parsedData = parsedContent.data; // Use the nested data object
    console.log("Extracted 'parsedData' (parsedContent.data):", JSON.stringify(parsedData, null, 2)); // Log the extracted data object
    
    // Helper to parse date strings safely (dd/mm/yyyy -> Date object)
    const parseDateString = (dateStr) => {
        if (!dateStr || typeof dateStr !== 'string') return null;
        const parts = dateStr.split('/');
        if (parts.length === 3) {
            // Assumes dd/mm/yyyy format
            const [day, month, year] = parts.map(Number);
            // Month is 0-indexed in JS Date
            const date = new Date(Date.UTC(year, month - 1, day));
             // Check if the constructed date is valid
            if (!isNaN(date.getTime()) && date.getUTCDate() === day && date.getUTCMonth() === month - 1 && date.getUTCFullYear() === year) {
                return date;
            }
        }
        console.warn(`Could not parse date string: ${dateStr}`);
        return null; // Return null if format is wrong or date is invalid
    };

    // 3. Save the actual file (locally for now, as per original logic)
    const fileData = req.file.buffer;
    const filename = `${Date.now()}-${req.file.originalname}`;
    const uploadDir = path.join(__dirname, '../../uploads');
    await fs.mkdir(uploadDir, { recursive: true }); // Ensure dir exists
    const localFilePath = path.join(uploadDir, filename);
    await fs.writeFile(localFilePath, fileData);
    console.log(`Statement file saved locally to: ${localFilePath}`);

    // 4. Create Firestore record with parsed data
    const statementRef = db.collection('statements').doc();
    const statement = {
      id: statementRef.id,
      userId,
      // Map from new AI prompt fields
      supplier: parsedData.SupplierName || 'Unknown', // This is who ISSUED the statement
      customerName: parsedData.CustomerName || null, // This is who the statement is FOR
      customerAddress: parsedData.CustomerAddress || null,
      
      statementDate: parseDateString(parsedData.InvoiceDate), // 'InvoiceDate' in prompt is statement issue date
      dueDate: parseDateString(parsedData.DueDate),
      
      // PeriodStart and PeriodEnd are not in the new prompt, set to null or decide if they should be added back
      period: {
        start: null, // Or parse from statement text if consistently available and add to prompt
        end: null   // Or parse from statement text if consistently available and add to prompt
      },
      
      // StatementId is not in the new prompt, can be omitted or AI can be asked for it
      statementId: null, // Or ask AI for a general statement reference number

      filePath: localFilePath,
      fileName: req.file.originalname,
      fileType: req.file.mimetype,
      fileSize: req.file.size,
      
      // 'Items' from prompt are the invoices/credits, now with a 'Type' field
      invoices: (parsedData.Items || []).map(item => ({
          invoiceId: item.InvoiceNumber || null, // Map InvoiceNumber to invoiceId
          Amount: typeof item.Amount === 'number' ? item.Amount : parseFloat(item.Amount || '0'),
          Date: parseDateString(item.Date),
          Type: item.Type || 'invoice' // Store the type, default to 'invoice' if missing
      })),
      
      // credits array is no longer needed as items are typed
      // credits: [], 

      totalTax: typeof parsedData.TotalTax === 'number' ? parsedData.TotalTax : parseFloat(parsedData.TotalTax || '0'),
      totalAmount: typeof parsedData.TotalAmount === 'number' ? parsedData.TotalAmount : parseFloat(parsedData.TotalAmount || '0'),
      
      discrepancies: [],
      status: 'processed',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await statementRef.set(statement);
    console.log(`Statement record created in Firestore with ID: ${statementRef.id}`);

    // No temporary file to clean up when using memory storage (req.file.buffer)

    // Format dates for the response object more robustly
    const formatResponseDate = (date) => {
        // Check if it's a valid Date object before calling toISOString
        if (date instanceof Date && !isNaN(date)) {
            return date.toISOString();
        }
        // Log if the date is invalid or not a Date object, but return null
        if (date !== null && date !== undefined) {
             console.warn(`Invalid date encountered during response formatting: ${date}`);
        }
        return null;
    };

    try {
        console.log("Formatting final response...");
        const responsePayload = {
            success: true,
            message: 'Statement uploaded and processed successfully',
            statement: { // Return the processed statement data, mapping to new structure if needed
                id: statement.id,
                userId: statement.userId,
                supplierName: statement.supplier, // Corresponds to SupplierName in AI output
                customerName: statement.customerName,
                customerAddress: statement.customerAddress,
                statementDate: formatResponseDate(statement.statementDate), // Corresponds to InvoiceDate in AI output
                dueDate: formatResponseDate(statement.dueDate),
                period: { // These might be null if not in AI prompt / parsed
                    start: formatResponseDate(statement.period.start),
                    end: formatResponseDate(statement.period.end)
                },
                statementId: statement.statementId, // This might be null
                fileName: statement.fileName,
                fileType: statement.fileType,
                fileSize: statement.fileSize,
                totalTax: statement.totalTax,
                totalAmount: statement.totalAmount,
                // 'Items' from AI are mapped to 'invoices' in our Firestore model
                invoices: statement.invoices.map(inv => ({
                    invoiceId: inv.invoiceId,
                    Amount: inv.Amount,
                    Date: formatResponseDate(inv.Date),
                    Type: inv.Type // Pass Type to frontend
                })),
                // credits array is no longer part of the response structure
                // credits: [], 
                status: statement.status,
                createdAt: formatResponseDate(statement.createdAt),
                updatedAt: formatResponseDate(statement.updatedAt)
                // Exclude filePath from response for security
            }
        };
        console.log("Sending final response to client.");
        res.json(responsePayload);
    } catch (formatError) {
        console.error("!!! Error formatting or sending final response:", formatError);
        // Attempt to send a generic error if formatting/sending failed
        if (!res.headersSent) {
             res.status(500).json({ success: false, error: { message: 'Internal server error during response formatting.' } });
        } else {
             console.error("Headers already sent, cannot send error response for formatError.");
        }
    }
  } catch (error) {
    // This catches errors from earlier in the function (PDF read, AI parse, DB write)
    console.error('Error during statement upload process:', error.message, error.stack);
    // Ensure response is sent even if headers haven't been set by a specific error handler
    if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: {
            message: 'Failed to upload statement',
            details: error.message || 'Unknown error'
          }
        });
    } else {
         console.error("Headers already sent in main catch block, cannot send error response for general upload error.");
    }
  }
};

// Process statement PDF file
const processStatement = async (req, res) => {
  try {
    const userId = req.user.uid;
    const file = req.file;
    
    if (!file || !file.buffer) {
      return {
        success: false,
        error: {
          message: 'No file provided or file buffer is empty'
        }
      };
    }

    // Create a new statement record
    const { db } = await firebaseInitializationPromise;
    const statementRef = db.collection('statements').doc();
    
    const statement = {
      id: statementRef.id,
      userId,
      fileName: file.originalname,
      fileType: 'application/pdf',
      fileSize: file.buffer.length,
      processed: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await statementRef.set(statement);
    
    return {
      success: true,
      message: 'Statement processed successfully',
      statement: {
        ...statement,
        createdAt: statement.createdAt.toISOString(),
        updatedAt: statement.updatedAt.toISOString()
      }
    };
  } catch (error) {
    console.error('Error processing statement:', error);
    return {
      success: false,
      error: {
        message: 'Failed to process statement',
        details: error.message
      }
    };
  }
};

// Process statements from email
const processStatementsFromEmail = async (req, res) => {
  try {
    // Use the existing email processing functionality
    await checkEmails(req, res);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Get statement file content
const getStatementFile = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const statementId = req.params.id;

    // Get the statement document
    const statementDoc = await db.collection('statements').doc(statementId).get();

    if (!statementDoc.exists) {
      return res.status(404).json({
        success: false,
        error: { message: 'Statement not found' }
      });
    }

    const statementData = statementDoc.data();

    // Check ownership
    if (statementData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: { message: 'Access denied' }
      });
    }

    // Check if filePath exists
    if (!statementData.filePath) {
        console.error(`File path missing for statement ID: ${statementId}`);
        return res.status(500).json({
            success: false,
            error: { message: 'File path not found for this statement.' }
        });
    }

    // Check if the file exists on the server
    try {
      await fs.access(statementData.filePath);
    } catch (err) {
      console.error(`File not found at path: ${statementData.filePath} for statement ID: ${statementId}`, err);
      return res.status(404).json({
        success: false,
        error: { message: 'Statement file not found on server.' }
      });
    }

    // Set headers and send the file
    res.setHeader('Content-Type', statementData.fileType || 'application/octet-stream');
    // Optionally, set Content-Disposition to suggest a filename to the browser
    // res.setHeader('Content-Disposition', `inline; filename="${statementData.fileName}"`);
    
    const fileStream = await fs.readFile(statementData.filePath);
    res.send(fileStream);

  } catch (error) {
    console.error('Error getting statement file:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get statement file',
        details: error.message
      }
    });
  }
};

export {
  getAllStatements,
  getStatementById,
  uploadStatement,
  processStatement,
  processStatementsFromEmail,
  getStatementFile // <-- Add the new function here
};
