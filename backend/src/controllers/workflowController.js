import { firebaseInitializationPromise } from '../config/firebase.js';

// Get dashboard data
const getDashboardData = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get counts from various collections
    const ordersQuery = await db.collection('orders').where('userId', '==', userId).get();
    const invoicesQuery = await db.collection('invoices').where('userId', '==', userId).get();
    const checklistItemsQuery = await db.collection('checklistItems').where('userId', '==', userId).get();
    const comparisonsQuery = await db.collection('comparisons').where('userId', '==', userId).get();
    const creditRequestsQuery = await db.collection('creditRequests').where('userId', '==', userId).get();
    
    // Calculate counts
    const orderCount = ordersQuery.size;
    const invoiceCount = invoicesQuery.size;
    const checklistItemCount = checklistItemsQuery.size;
    const comparisonCount = comparisonsQuery.size;
    const creditRequestCount = creditRequestsQuery.size;
    
    // Calculate status counts
    const pendingChecklistItems = [];
    const completedChecklistItems = [];
    
    checklistItemsQuery.forEach(doc => {
      const data = doc.data();
      if (data.status === 'pending') {
        pendingChecklistItems.push(data);
      } else {
        completedChecklistItems.push(data);
      }
    });
    
    const pendingCreditRequests = [];
    const completedCreditRequests = [];
    
    creditRequestsQuery.forEach(doc => {
      const data = doc.data();
      if (data.status === 'pending' || data.status === 'sent') {
        pendingCreditRequests.push(data);
      } else {
        completedCreditRequests.push(data);
      }
    });
    
    // Get recent activity
    const activityLogQuery = await db.collection('activityLog')
      .where('userId', '==', userId)
      .orderBy('timestamp', 'desc')
      .limit(5)
      .get();
    
    const recentActivity = [];
    activityLogQuery.forEach(doc => {
      const data = doc.data();
      recentActivity.push({
        id: doc.id,
        ...data,
        timestamp: data.timestamp?.toDate()?.toISOString() || null
      });
    });
    
    // Construct dashboard data
    const dashboardData = {
      counts: {
        orders: orderCount,
        invoices: invoiceCount,
        checklistItems: checklistItemCount,
        comparisons: comparisonCount,
        creditRequests: creditRequestCount
      },
      status: {
        checklistItems: {
          pending: pendingChecklistItems.length,
          completed: completedChecklistItems.length
        },
        creditRequests: {
          pending: pendingCreditRequests.length,
          completed: completedCreditRequests.length
        }
      },
      recentActivity
    };
    
    res.json({
      success: true,
      dashboard: dashboardData
    });
  } catch (error) {
    console.error('Error getting dashboard data:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get dashboard data',
        details: error.message
      }
    });
  }
};

// Get workflow status
const getWorkflowStatus = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get workflow steps
    const workflowStepsQuery = await db.collection('workflowSteps')
      .where('userId', '==', userId)
      .orderBy('order', 'asc')
      .get();
    
    const workflowSteps = [];
    workflowStepsQuery.forEach(doc => {
      const data = doc.data();
      workflowSteps.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate()?.toISOString() || null,
        completedAt: data.completedAt?.toDate()?.toISOString() || null
      });
    });
    
    // If no workflow steps found, create default workflow
    if (workflowSteps.length === 0) {
      const defaultWorkflow = [
        {
          name: 'Order Retrieval',
          description: 'Retrieve orders from Drug Comparison',
          status: 'completed',
          order: 1,
          type: 'order',
          createdAt: new Date()
        },
        {
          name: 'Checklist Creation',
          description: 'Create checklist items from orders',
          status: 'in_progress',
          order: 2,
          type: 'checklist',
          createdAt: new Date()
        },
        {
          name: 'Reconciliation',
          description: 'Compare invoices with orders',
          status: 'pending',
          order: 3,
          type: 'comparison',
          createdAt: new Date()
        },
        {
          name: 'Credit Request',
          description: 'Request credit for discrepancies',
          status: 'pending',
          order: 4,
          type: 'credit_request',
          createdAt: new Date()
        },
        {
          name: 'Reporting',
          description: 'Generate reports',
          status: 'pending',
          order: 5,
          type: 'report',
          createdAt: new Date()
        }
      ];
      
      const batch = db.batch();
      
      for (const step of defaultWorkflow) {
        const stepRef = db.collection('workflowSteps').doc();
        batch.set(stepRef, {
          id: stepRef.id,
          userId,
          ...step
        });
        
        workflowSteps.push({
          id: stepRef.id,
          userId,
          ...step,
          createdAt: step.createdAt.toISOString(),
          completedAt: null
        });
      }
      
      await batch.commit();
    }
    
    // Calculate overall progress
    const totalSteps = workflowSteps.length;
    const completedSteps = workflowSteps.filter(step => step.status === 'completed').length;
    const progress = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
    
    res.json({
      success: true,
      workflow: {
        steps: workflowSteps,
        progress,
        totalSteps,
        completedSteps
      }
    });
  } catch (error) {
    console.error('Error getting workflow status:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get workflow status',
        details: error.message
      }
    });
  }
};

// Complete workflow step
const completeWorkflowStep = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const stepId = req.params.id;
    
    // Get the workflow step
    const stepDoc = await db.collection('workflowSteps').doc(stepId).get();
    
    if (!stepDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Workflow step not found'
        }
      });
    }
    
    const stepData = stepDoc.data();
    
    // Check if the step belongs to the current user
    if (stepData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }
    
    // Check if the step is already completed
    if (stepData.status === 'completed') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Workflow step is already completed'
        }
      });
    }
    
    // Update the step
    await db.collection('workflowSteps').doc(stepId).update({
      status: 'completed',
      completedAt: new Date(),
      updatedAt: new Date()
    });
    
    // Get the next step
    const nextStepQuery = await db.collection('workflowSteps')
      .where('userId', '==', userId)
      .where('order', '==', stepData.order + 1)
      .get();
    
    // If there's a next step, update its status to in_progress
    if (!nextStepQuery.empty) {
      const nextStepDoc = nextStepQuery.docs[0];
      await db.collection('workflowSteps').doc(nextStepDoc.id).update({
        status: 'in_progress',
        updatedAt: new Date()
      });
    }
    
    // Log activity
    const activityLogRef = db.collection('activityLog').doc();
    await activityLogRef.set({
      id: activityLogRef.id,
      userId,
      action: 'complete_workflow_step',
      details: {
        stepId,
        stepName: stepData.name
      },
      timestamp: new Date()
    });
    
    // Get the updated workflow status
    const workflowStepsQuery = await db.collection('workflowSteps')
      .where('userId', '==', userId)
      .orderBy('order', 'asc')
      .get();
    
    const workflowSteps = [];
    workflowStepsQuery.forEach(doc => {
      const data = doc.data();
      workflowSteps.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate()?.toISOString() || null,
        completedAt: data.completedAt?.toDate()?.toISOString() || null,
        updatedAt: data.updatedAt?.toDate()?.toISOString() || null
      });
    });
    
    // Calculate overall progress
    const totalSteps = workflowSteps.length;
    const completedSteps = workflowSteps.filter(step => step.status === 'completed').length;
    const progress = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
    
    res.json({
      success: true,
      message: `Workflow step "${stepData.name}" completed`,
      workflow: {
        steps: workflowSteps,
        progress,
        totalSteps,
        completedSteps
      }
    });
  } catch (error) {
    console.error('Error completing workflow step:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to complete workflow step',
        details: error.message
      }
    });
  }
};

// Skip workflow step
const skipWorkflowStep = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const stepId = req.params.id;
    const { reason } = req.body;
    
    if (!reason) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Reason for skipping is required'
        }
      });
    }
    
    // Get the workflow step
    const stepDoc = await db.collection('workflowSteps').doc(stepId).get();
    
    if (!stepDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Workflow step not found'
        }
      });
    }
    
    const stepData = stepDoc.data();
    
    // Check if the step belongs to the current user
    if (stepData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }
    
    // Check if the step is already completed or skipped
    if (stepData.status === 'completed' || stepData.status === 'skipped') {
      return res.status(400).json({
        success: false,
        error: {
          message: `Workflow step is already ${stepData.status}`
        }
      });
    }
    
    // Update the step
    await db.collection('workflowSteps').doc(stepId).update({
      status: 'skipped',
      skipReason: reason,
      skippedAt: new Date(),
      updatedAt: new Date()
    });
    
    // Get the next step
    const nextStepQuery = await db.collection('workflowSteps')
      .where('userId', '==', userId)
      .where('order', '==', stepData.order + 1)
      .get();
    
    // If there's a next step, update its status to in_progress
    if (!nextStepQuery.empty) {
      const nextStepDoc = nextStepQuery.docs[0];
      await db.collection('workflowSteps').doc(nextStepDoc.id).update({
        status: 'in_progress',
        updatedAt: new Date()
      });
    }
    
    // Log activity
    const activityLogRef = db.collection('activityLog').doc();
    await activityLogRef.set({
      id: activityLogRef.id,
      userId,
      action: 'skip_workflow_step',
      details: {
        stepId,
        stepName: stepData.name,
        reason
      },
      timestamp: new Date()
    });
    
    // Get the updated workflow status
    const workflowStepsQuery = await db.collection('workflowSteps')
      .where('userId', '==', userId)
      .orderBy('order', 'asc')
      .get();
    
    const workflowSteps = [];
    workflowStepsQuery.forEach(doc => {
      const data = doc.data();
      workflowSteps.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate()?.toISOString() || null,
        completedAt: data.completedAt?.toDate()?.toISOString() || null,
        skippedAt: data.skippedAt?.toDate()?.toISOString() || null,
        updatedAt: data.updatedAt?.toDate()?.toISOString() || null
      });
    });
    
    // Calculate overall progress
    const totalSteps = workflowSteps.length;
    const completedSteps = workflowSteps.filter(step => step.status === 'completed').length;
    const skippedSteps = workflowSteps.filter(step => step.status === 'skipped').length;
    const progress = totalSteps > 0 ? Math.round(((completedSteps + skippedSteps) / totalSteps) * 100) : 0;
    
    res.json({
      success: true,
      message: `Workflow step "${stepData.name}" skipped`,
      workflow: {
        steps: workflowSteps,
        progress,
        totalSteps,
        completedSteps,
        skippedSteps
      }
    });
  } catch (error) {
    console.error('Error skipping workflow step:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to skip workflow step',
        details: error.message
      }
    });
  }
};

export {
  getDashboardData,
  getWorkflowStatus,
  completeWorkflowStep,
  skipWorkflowStep
};