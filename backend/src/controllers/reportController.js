import { firebaseInitializationPromise } from '../config/firebase.js';

// Generate new report
const generateReport = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const { type, dateRange, customOptions } = req.body;
    
    if (!type || !dateRange || !dateRange.start || !dateRange.end) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Report type and date range are required'
        }
      });
    }
    
    // Validate report type
    const validTypes = ['discrepancy', 'reconciliation', 'monthly'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Invalid report type. Must be one of: ${validTypes.join(', ')}`
        }
      });
    }
    
    // Parse date range
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    
    // Generate report data based on type (simplified for brevity)
    const reportData = {
      type,
      dateRange: {
        start: startDate,
        end: endDate
      },
      summary: {
        totalItems: 0,
        totalAmount: 0
      }
    };
    
    // Create report record
    const reportRef = db.collection('reports').doc();
    const report = {
      id: reportRef.id,
      userId,
      type,
      dateRange: {
        start: startDate,
        end: endDate
      },
      data: reportData,
      customOptions: customOptions || {},
      status: 'generated',
      generatedAt: new Date(),
      generatedBy: req.user.email || userId
    };
    
    await reportRef.set(report);
    
    res.json({
      success: true,
      message: 'Report generated successfully',
      report: {
        id: reportRef.id,
        type,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        status: 'generated',
        generatedAt: report.generatedAt.toISOString(),
        generatedBy: report.generatedBy
      }
    });
  } catch (error) {
    console.error('Error generating report:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to generate report',
        details: error.message
      }
    });
  }
};

// Get all reports
const getAllReports = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { type, startDate, endDate } = req.query;
    
    // Start with base query
    let query = db.collection('reports').where('userId', '==', userId);
    
    // Apply filters if provided
    if (type) {
      query = query.where('type', '==', type);
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const reports = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Apply date filtering in memory
      if (startDate || endDate) {
        const reportDate = data.generatedAt?.toDate() || new Date(0);
        
        if (startDate && new Date(startDate) > reportDate) {
          return; // Skip this item
        }
        
        if (endDate && new Date(endDate) < reportDate) {
          return; // Skip this item
        }
      }
      
      reports.push({
        id: doc.id,
        type: data.type,
        dateRange: {
          start: data.dateRange?.start?.toDate()?.toISOString() || null,
          end: data.dateRange?.end?.toDate()?.toISOString() || null
        },
        status: data.status,
        generatedAt: data.generatedAt?.toDate()?.toISOString() || null,
        generatedBy: data.generatedBy
      });
    });
    
    res.json({
      success: true,
      count: reports.length,
      reports
    });
  } catch (error) {
    console.error('Error getting reports:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get reports',
        details: error.message
      }
    });
  }
};

// Download report
const downloadReport = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const reportId = req.params.id;
    const { format } = req.query;
    
    if (!format) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Format is required (pdf, excel, csv)'
        }
      });
    }
    
    // Validate format
    const validFormats = ['pdf', 'excel', 'csv'];
    if (!validFormats.includes(format)) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Invalid format. Must be one of: ${validFormats.join(', ')}`
        }
      });
    }
    
    // Get the report
    const reportDoc = await db.collection('reports').doc(reportId).get();
    
    if (!reportDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Report not found'
        }
      });
    }
    
    const reportData = reportDoc.data();
    
    // Check if the report belongs to the current user
    if (reportData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }
    
    // For demonstration, we'll just return JSON with a message
    res.json({
      success: true,
      message: `Report would be downloaded in ${format} format`,
      reportId,
      format
    });
  } catch (error) {
    console.error('Error downloading report:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to download report',
        details: error.message
      }
    });
  }
};

// Email report
const emailReport = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const { reportId, email, format, message } = req.body;
    
    if (!reportId || !email || !format) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Report ID, email, and format are required'
        }
      });
    }
    
    // Validate format
    const validFormats = ['pdf', 'excel', 'csv'];
    if (!validFormats.includes(format)) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Invalid format. Must be one of: ${validFormats.join(', ')}`
        }
      });
    }
    
    // Get the report
    const reportDoc = await db.collection('reports').doc(reportId).get();
    
    if (!reportDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Report not found'
        }
      });
    }
    
    const reportData = reportDoc.data();
    
    // Check if the report belongs to the current user
    if (reportData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }
    
    // For demonstration, we'll just return a success message
    res.json({
      success: true,
      message: `Report would be emailed to ${email} in ${format} format`,
      details: {
        reportId,
        email,
        format,
        message: message || 'Please find the attached report.'
      }
    });
  } catch (error) {
    console.error('Error emailing report:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to email report',
        details: error.message
      }
    });
  }
};

// Get supplier performance metrics
const getSupplierMetrics = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { startDate, endDate } = req.query;
    
    // Parse date range
    const start = startDate ? new Date(startDate) : new Date(0);
    const end = endDate ? new Date(endDate) : new Date();
    
    // For demonstration, we'll just return a sample response
    res.json({
      success: true,
      metrics: {
        dateRange: {
          start: start.toISOString(),
          end: end.toISOString()
        },
        suppliers: [
          {
            name: 'Supplier A',
            metrics: {
              orderCount: 10,
              orderTotal: 1000,
              comparisonCount: 8,
              discrepancyCount: 3,
              exactMatchCount: 5,
              partialMatchCount: 2,
              mismatchCount: 1,
              resolvedCount: 2,
              matchRate: 62.5,
              resolutionRate: 25
            }
          },
          {
            name: 'Supplier B',
            metrics: {
              orderCount: 8,
              orderTotal: 800,
              comparisonCount: 6,
              discrepancyCount: 1,
              exactMatchCount: 5,
              partialMatchCount: 1,
              mismatchCount: 0,
              resolvedCount: 1,
              matchRate: 83.33,
              resolutionRate: 16.67
            }
          }
        ]
      }
    });
  } catch (error) {
    console.error('Error getting supplier metrics:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get supplier metrics',
        details: error.message
      }
    });
  }
};

// Get financial impact analysis
const getFinancialImpact = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { startDate, endDate } = req.query;
    
    // Parse date range
    const start = startDate ? new Date(startDate) : new Date(0);
    const end = endDate ? new Date(endDate) : new Date();
    
    // For demonstration, we'll just return a sample response
    res.json({
      success: true,
      financialImpact: {
        dateRange: {
          start: start.toISOString(),
          end: end.toISOString()
        },
        summary: {
          totalComparisons: 14,
          totalOvercharge: 250.75,
          totalUndercharge: 120.50,
          totalDiscrepancyAmount: 371.25,
          totalSavings: 200.25,
          roi: 20.03
        },
        discrepancyTypes: [
          {
            type: 'unitPrice',
            count: 8,
            amount: 180.50
          },
          {
            type: 'quantity',
            count: 4,
            amount: 120.75
          },
          {
            type: 'total',
            count: 2,
            amount: 70.00
          }
        ],
        supplierImpact: [
          {
            supplier: 'Supplier A',
            discrepancyCount: 3,
            overchargeAmount: 150.25,
            underchargeAmount: 50.50,
            totalAmount: 200.75,
            savingsAmount: 120.25
          },
          {
            supplier: 'Supplier B',
            discrepancyCount: 1,
            overchargeAmount: 100.50,
            underchargeAmount: 70.00,
            totalAmount: 170.50,
            savingsAmount: 80.00
          }
        ]
      }
    });
  } catch (error) {
    console.error('Error getting financial impact:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get financial impact',
        details: error.message
      }
    });
  }
};

export {
  generateReport,
  getAllReports,
  downloadReport,
  emailReport,
  getSupplierMetrics,
  getFinancialImpact
};
