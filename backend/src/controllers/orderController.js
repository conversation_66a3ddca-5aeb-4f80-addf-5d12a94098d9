import { firebaseInitializationPromise } from '../config/firebase.js';
import { scrapeOrders } from './drugComparisonController.js';

// Get all orders
const getAllOrders = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    
    const userId = req.user.uid;
    const ordersRef = db.collection('orders');
    const q = ordersRef.where('userId', '==', userId);
    const ordersSnapshot = await q.get();
    
    const orders = [];
    ordersSnapshot.forEach(doc => {
      const data = doc.data();
      // Ensure consistent date formatting
      const formatDate = (date) => {
        if (!date) return '';
        if (typeof date === 'string') {
          console.log('Date is already a string:', date);
          return date;
        }
        if (date.toDate) {
          // Handle Firestore Timestamp
          const d = date.toDate();
          const formattedDate = `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
          console.log('Formatted Firestore timestamp:', formattedDate);
          return formattedDate;
        }
        console.log('Unknown date format:', date);
        return '';
      };

      const createdAt = data.createdAt?.toDate() || new Date(0);
      orders.push({
        id: doc.id,
        ...data,
        createdAt: formatDate(data.createdAt),
        dateTime: data.dateTime || '',
        // Ensure numeric fields are numbers
        orderQty: parseFloat(data.orderQty) || 0,
        approvedQty: parseFloat(data.approvedQty) || 0,
        price: parseFloat(data.price) || 0,
        dtPrice: parseFloat(data.dtPrice) || 0,
        subTotal: parseFloat(data.subTotal) || 0,
        discount: parseFloat(data.discount) || 0,
        status: data.status || 'pending' // Default status if not present
      });
    });

    // Sort orders by createdAt descending
    orders.sort((a, b) => {
      const dateA = new Date(a.createdAt);
      const dateB = new Date(b.createdAt);
      return dateB.getTime() - dateA.getTime();
    });

    res.json(orders);
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch orders',
        details: error.message
      }
    });
  }
};

// Get specific order details
const getOrderById = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const orderId = req.params.id;
    const userId = req.user.uid;

    const orderDoc = await db.collection('orders').doc(orderId).get();

    if (!orderDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Order not found'
        }
      });
    }

    const orderData = orderDoc.data();

    // Check if the order belongs to the current user
    if (orderData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }

    // Format dates and ensure numeric fields
    const formatDate = (date) => {
      if (!date) return '';
      if (typeof date === 'string') return date;
      if (date.toDate) {
        // Handle Firestore Timestamp
        const d = date.toDate();
        return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
      }
      return '';
    };

    const order = {
      id: orderDoc.id,
      ...orderData,
      createdAt: formatDate(orderData.createdAt),
      dateTime: orderData.dateTime || '',
      // Ensure numeric fields are numbers
      orderQty: parseFloat(orderData.orderQty) || 0,
      approvedQty: parseFloat(orderData.approvedQty) || 0,
      price: parseFloat(orderData.price) || 0,
      dtPrice: parseFloat(orderData.dtPrice) || 0,
      subTotal: parseFloat(orderData.subTotal) || 0,
      discount: parseFloat(orderData.discount) || 0,
      status: orderData.status || 'pending' // Default status if not present
    };

    res.json(order);
  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch order',
        details: error.message
      }
    });
  }
};

// Update order status
const updateOrderStatus = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const orderId = req.params.id;
    const userId = req.user.uid;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['pending', 'processed', 'completed'];
    if (!status || !validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid status. Must be one of: pending, processed, completed'
        }
      });
    }

    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();

    if (!orderDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Order not found'
        }
      });
    }

    const orderData = orderDoc.data();

    // Check if the order belongs to the current user
    if (orderData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }

    // Update the status
    await db.collection('orders').doc(orderId).update({
      status,
      updatedAt: new Date()
    });

    res.json({
      success: true,
      message: `Order status updated to ${status}`,
      orderId
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update order status',
        details: error.message
      }
    });
  }
};

// Force synchronization with Drug Comparison
const syncOrders = async (req, res) => {
  try {
    const { startDate, endDate } = req.body;
    // Pass startDate and endDate to scrapeOrders function
    await scrapeOrders(req, res); // The date parameters will be available in req.body
  } catch (error) {
    console.error('Error syncing orders:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to sync orders',
        details: error.message
      }
    });
  }
};

export { getAllOrders, getOrderById, updateOrderStatus, syncOrders };