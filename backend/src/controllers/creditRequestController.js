import { firebaseInitializationPromise } from '../config/firebase.js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create new credit request
const createCreditRequest = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const { orderId, invoiceId, items, supplier, notes } = req.body;
    
    if (!orderId || !invoiceId || !items || !Array.isArray(items) || items.length === 0 || !supplier) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Order ID, Invoice ID, items array, and supplier are required'
        }
      });
    }
    
    // Validate items
    for (const item of items) {
      if (!item.productName || !item.pipCode || !item.quantity || !item.reason) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Each item must have productName, pipCode, quantity, and reason'
          }
        });
      }
    }
    
    // Create credit request
    const creditRequestRef = db.collection('creditRequests').doc();
    const creditRequest = {
      id: creditRequestRef.id,
      userId,
      orderId,
      invoiceId,
      items,
      supplier,
      requestDate: new Date(),
      status: 'pending',
      emailHistory: [],
      notes: notes || '',
      createdAt: new Date()
    };
    
    await creditRequestRef.set(creditRequest);
    
    res.status(201).json({
      success: true,
      message: 'Credit request created successfully',
      creditRequest: {
        ...creditRequest,
        requestDate: creditRequest.requestDate.toISOString(),
        createdAt: creditRequest.createdAt.toISOString()
      }
    });
  } catch (error) {
    console.error('Error creating credit request:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create credit request',
        details: error.message
      }
    });
  }
};

// Get all credit requests
const getAllCreditRequests = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { supplier, status, startDate, endDate } = req.query;
    
    // Start with base query
    let query = db.collection('creditRequests').where('userId', '==', userId);
    
    // Apply filters if provided
    if (supplier) {
      query = query.where('supplier', '==', supplier);
    }
    
    if (status) {
      query = query.where('status', '==', status);
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const creditRequests = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Apply date filtering in memory
      if (startDate || endDate) {
        const requestDate = data.requestDate?.toDate() || new Date(0);
        
        if (startDate && new Date(startDate) > requestDate) {
          return; // Skip this item
        }
        
        if (endDate && new Date(endDate) < requestDate) {
          return; // Skip this item
        }
      }
      
      creditRequests.push({
        id: doc.id,
        ...data,
        requestDate: data.requestDate?.toDate()?.toISOString() || null,
        createdAt: data.createdAt?.toDate()?.toISOString() || null,
        emailHistory: data.emailHistory?.map(email => ({
          ...email,
          sentAt: email.sentAt?.toDate()?.toISOString() || null
        })) || []
      });
    });
    
    res.json({
      success: true,
      count: creditRequests.length,
      creditRequests
    });
  } catch (error) {
    console.error('Error getting credit requests:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get credit requests',
        details: error.message
      }
    });
  }
};

// Update credit request status
const updateCreditRequestStatus = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const creditRequestId = req.params.id;
    const { status, notes } = req.body;
    
    if (!status) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Status is required'
        }
      });
    }
    
    // Validate status
    const validStatuses = ['pending', 'sent', 'approved', 'rejected'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
        }
      });
    }
    
    // Get the credit request
    const creditRequestDoc = await db.collection('creditRequests').doc(creditRequestId).get();
    
    if (!creditRequestDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Credit request not found'
        }
      });
    }
    
    const creditRequestData = creditRequestDoc.data();
    
    // Check if the credit request belongs to the current user
    if (creditRequestData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }
    
    // Update the credit request
    const updateData = {
      status,
      updatedAt: new Date()
    };
    
    if (notes !== undefined) {
      updateData.notes = notes;
    }
    
    await db.collection('creditRequests').doc(creditRequestId).update(updateData);
    
    // Get the updated credit request
    const updatedCreditRequestDoc = await db.collection('creditRequests').doc(creditRequestId).get();
    const updatedCreditRequest = updatedCreditRequestDoc.data();
    
    res.json({
      success: true,
      message: 'Credit request status updated successfully',
      creditRequest: {
        id: updatedCreditRequestDoc.id,
        ...updatedCreditRequest,
        requestDate: updatedCreditRequest.requestDate?.toDate()?.toISOString() || null,
        createdAt: updatedCreditRequest.createdAt?.toDate()?.toISOString() || null,
        updatedAt: updatedCreditRequest.updatedAt?.toDate()?.toISOString() || null,
        emailHistory: updatedCreditRequest.emailHistory?.map(email => ({
          ...email,
          sentAt: email.sentAt?.toDate()?.toISOString() || null
        })) || []
      }
    });
  } catch (error) {
    console.error('Error updating credit request status:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update credit request status',
        details: error.message
      }
    });
  }
};

// Send email to supplier
const sendEmailToSupplier = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const creditRequestId = req.params.id;
    const { email, subject, message, templateId } = req.body;
    
    if (!email || !subject || !message) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Email, subject, and message are required'
        }
      });
    }
    
    // Get the credit request
    const creditRequestDoc = await db.collection('creditRequests').doc(creditRequestId).get();
    
    if (!creditRequestDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Credit request not found'
        }
      });
    }
    
    const creditRequestData = creditRequestDoc.data();
    
    // Check if the credit request belongs to the current user
    if (creditRequestData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }
    
    // In a real implementation, you would send the email here
    // For demonstration, we'll just record the email in the history
    
    const emailRecord = {
      to: email,
      subject,
      message,
      templateId: templateId || null,
      sentAt: new Date(),
      status: 'sent'
    };
    
    // Update the credit request with the email record
    const emailHistory = [...(creditRequestData.emailHistory || []), emailRecord];
    
    await db.collection('creditRequests').doc(creditRequestId).update({
      emailHistory,
      status: 'sent',
      updatedAt: new Date()
    });
    
    // Get the updated credit request
    const updatedCreditRequestDoc = await db.collection('creditRequests').doc(creditRequestId).get();
    const updatedCreditRequest = updatedCreditRequestDoc.data();
    
    res.json({
      success: true,
      message: 'Email sent to supplier',
      emailRecord: {
        ...emailRecord,
        sentAt: emailRecord.sentAt.toISOString()
      },
      creditRequest: {
        id: updatedCreditRequestDoc.id,
        ...updatedCreditRequest,
        requestDate: updatedCreditRequest.requestDate?.toDate()?.toISOString() || null,
        createdAt: updatedCreditRequest.createdAt?.toDate()?.toISOString() || null,
        updatedAt: updatedCreditRequest.updatedAt?.toDate()?.toISOString() || null,
        emailHistory: updatedCreditRequest.emailHistory?.map(email => ({
          ...email,
          sentAt: email.sentAt?.toDate()?.toISOString() || null
        })) || []
      }
    });
  } catch (error) {
    console.error('Error sending email to supplier:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to send email to supplier',
        details: error.message
      }
    });
  }
};

// Upload images for credit request
const uploadImages = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const creditRequestId = req.params.id;
    
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'No files uploaded'
        }
      });
    }
    
    // Get the credit request
    const creditRequestDoc = await db.collection('creditRequests').doc(creditRequestId).get();
    
    if (!creditRequestDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Credit request not found'
        }
      });
    }
    
    const creditRequestData = creditRequestDoc.data();
    
    // Check if the credit request belongs to the current user
    if (creditRequestData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }
    
    // Process the uploaded files
    const uploadDir = path.join(__dirname, '../../uploads/credit-requests', creditRequestId);
    
    // Ensure upload directory exists
    try {
      await fs.mkdir(uploadDir, { recursive: true });
    } catch (err) {
      console.error('Error creating upload directory:', err);
    }
    
    const uploadedFiles = [];
    
    for (const file of req.files) {
      const fileData = await fs.readFile(file.path);
      
      // Create a unique filename
      const filename = `${Date.now()}-${file.originalname}`;
      const filePath = path.join(uploadDir, filename);
      
      // Save the file
      await fs.writeFile(filePath, fileData);
      
      uploadedFiles.push({
        originalName: file.originalname,
        filename,
        path: filePath,
        size: file.size,
        mimeType: file.mimetype
      });
      
      // Clean up the temporary file
      try {
        await fs.unlink(file.path);
      } catch (err) {
        console.error('Error deleting temporary file:', err);
      }
    }
    
    // Update the credit request with the image information
    const images = [...(creditRequestData.images || []), ...uploadedFiles];
    
    await db.collection('creditRequests').doc(creditRequestId).update({
      images,
      updatedAt: new Date()
    });
    
    res.json({
      success: true,
      message: `${uploadedFiles.length} images uploaded successfully`,
      uploadedFiles
    });
  } catch (error) {
    console.error('Error uploading images:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to upload images',
        details: error.message
      }
    });
  }
};

// Create batch credit requests
const createBatchCreditRequests = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const { supplier, requests } = req.body;
    
    if (!supplier || !requests || !Array.isArray(requests) || requests.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Supplier and requests array are required'
        }
      });
    }
    
    // Validate each request
    for (const request of requests) {
      if (!request.orderId || !request.invoiceId || !request.items || !Array.isArray(request.items) || request.items.length === 0) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Each request must have orderId, invoiceId, and items array'
          }
        });
      }
      
      // Validate items
      for (const item of request.items) {
        if (!item.productName || !item.pipCode || !item.quantity || !item.reason) {
          return res.status(400).json({
            success: false,
            error: {
              message: 'Each item must have productName, pipCode, quantity, and reason'
            }
          });
        }
      }
    }
    
    // Create batch credit requests
    const batch = db.batch();
    const createdRequests = [];
    
    for (const request of requests) {
      const creditRequestRef = db.collection('creditRequests').doc();
      const creditRequest = {
        id: creditRequestRef.id,
        userId,
        orderId: request.orderId,
        invoiceId: request.invoiceId,
        items: request.items,
        supplier,
        requestDate: new Date(),
        status: 'pending',
        emailHistory: [],
        notes: request.notes || '',
        createdAt: new Date()
      };
      
      batch.set(creditRequestRef, creditRequest);
      createdRequests.push({
        ...creditRequest,
        requestDate: creditRequest.requestDate.toISOString(),
        createdAt: creditRequest.createdAt.toISOString()
      });
    }
    
    await batch.commit();
    
    res.status(201).json({
      success: true,
      message: `${createdRequests.length} credit requests created successfully`,
      creditRequests: createdRequests
    });
  } catch (error) {
    console.error('Error creating batch credit requests:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create batch credit requests',
        details: error.message
      }
    });
  }
};

// Get email templates
const getEmailTemplates = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { supplier } = req.query;
    
    // Start with base query
    let query = db.collection('emailTemplates').where('userId', '==', userId);
    
    // Apply filters if provided
    if (supplier) {
      query = query.where('supplier', '==', supplier);
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const templates = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      templates.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate()?.toISOString() || null
      });
    });
    
    // If no templates found, return some default templates
    if (templates.length === 0) {
      const defaultTemplates = [
        {
          id: 'default-missing',
          name: 'Missing Items Template',
          subject: 'Credit Request for Missing Items - Order #{{orderId}}',
          body: `Dear {{supplier}},

We are writing to request credit for missing items from our recent order #{{orderId}}.

The following items were not received:
{{#each items}}
- {{productName}} (PIP: {{pipCode}}), Quantity: {{quantity}}
{{/each}}

Please process this credit request at your earliest convenience.

Thank you,
{{userName}}`,
          supplier: 'All',
          type: 'missing',
          isDefault: true
        },
        {
          id: 'default-damaged',
          name: 'Damaged Items Template',
          subject: 'Credit Request for Damaged Items - Order #{{orderId}}',
          body: `Dear {{supplier}},

We are writing to request credit for damaged items from our recent order #{{orderId}}.

The following items were received damaged:
{{#each items}}
- {{productName}} (PIP: {{pipCode}}), Quantity: {{quantity}}
{{/each}}

Photos of the damaged items are attached to this email.

Please process this credit request at your earliest convenience.

Thank you,
{{userName}}`,
          supplier: 'All',
          type: 'damaged',
          isDefault: true
        }
      ];
      
      templates.push(...defaultTemplates);
    }
    
    res.json({
      success: true,
      count: templates.length,
      templates
    });
  } catch (error) {
    console.error('Error getting email templates:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get email templates',
        details: error.message
      }
    });
  }
};

export {
  createCreditRequest,
  getAllCreditRequests,
  updateCreditRequestStatus,
  sendEmailToSupplier,
  uploadImages,
  createBatchCreditRequests,
  getEmailTemplates
};