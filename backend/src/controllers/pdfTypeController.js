import axios from 'axios';
import { readPdfFile } from '../utils/pdfUtils.js';

// Internal function for determining PDF type using OpenAI
async function _determinePdfTypeUsingAI(text) {
    const endpoint = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
    };
    
    const data = {
        model: "gpt-4o-mini",
        messages: [{
            "role": "user",
            "content": `Determine if this is an invoice or a statement. An invoice typically has line items with product details, quantities, and prices. A statement typically lists multiple invoices with their numbers and amounts. Only respond with either "invoice" or "statement". Here's the content:\n\n${text}`
        }],
        temperature: 0.0
    };

    try {
        const response = await axios.post(endpoint, data, { headers });
        const type = response.data.choices[0].message.content.toLowerCase().trim();
        return type === 'invoice' || type === 'statement' ? type : 'unknown';
    } catch (error) {
        console.error('Error determining PDF type:', error);
        throw error;
    }
}

// Function for determining PDF type from buffer
export async function determinePdfType(buffer) {
    try {
        if (!buffer || buffer.length === 0) {
            throw new Error('Empty PDF buffer');
        }

        // Check if it's a valid PDF
        const pdfHeader = buffer.slice(0, 5).toString();
        if (!pdfHeader.includes('%PDF-')) {
            throw new Error('Invalid PDF file: Missing PDF header');
        }

        // Read the PDF file
        const pages = await readPdfFile(buffer);
        
        // Use the first page to determine the type
        const documentType = await _determinePdfTypeUsingAI(pages[0]);
        return documentType;
    } catch (error) {
        console.error('Failed to determine PDF type:', error);
        throw error;
    }
}

export const processPdf = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        // Add validation and debugging for the uploaded file
        if (!req.file.buffer || req.file.buffer.length === 0) {
            return res.status(400).json({ error: 'Empty file buffer' });
        }

        // Determine the type directly from the buffer
        const documentType = await determinePdfType(req.file.buffer);

        res.json({
            success: true,
            documentType,
            message: `PDF identified as ${documentType}`
        });

    } catch (error) {
        console.error('Failed to process PDF:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
};