import axios from 'axios';
import { firebaseInitializationPromise } from '../config/firebase.js';
import { readPdfFile } from '../utils/pdfUtils.js';

async function parseGeneralTextWithOpenAI(text) {
    const endpoint = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
    };
    const data = {
        model: "gpt-4o-mini",
        messages: [{
            "role": "user",
            "content": `strictly return a json (do not start with 3 back-ticks json) of the invoice hereafter using the following format (disregard line items):
                {
                    "data": {
                        "VendorName": (this is the name of the vendor: AAH, Alliance, BNS, Colorama, Trident, Sigma, etc.),
                        "CustomerName": ,
                        "InvoiceDate": dd/mm/yyyy,
                        "DueDate": ,
                        "InvoiceId": ,
                        "CustomerAddress": ,
                        "CustomerAddressRecipient": ,
                        "CustomerId": ,
                        "ShippingAddress": ,
                        "ShippingAddressRecipient": (this is the name of the pharmacy or person receiving the goods),
                        "VendorAddress": ,
                        "VendorAddressRecipient": ,
                        "VendorTaxId": ,
                        "InvoiceTotal": (this is the net total without tax, no double quotes)
                    }
                }       
                ` + text
        }],
        temperature: 0.0
    };

    try {
        const response = await axios.post(endpoint, data, { headers });
        return response.data;
    } catch (error) {
        console.error('Error calling OpenAI API:', error.response ? error.response.data : error.message);
        throw error;
    }
}

async function parseLineItemWithOpenAI(text) {
    const endpoint = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
    };
    const data = {
        model: "gpt-4o-mini",
        messages: [{
            "role": "user",
            "content": `strictly return a json (do not start with 3 back-ticks json) of the line items of the invoice hereafter using the following format:
                {
                    Items: [{
                        "Amount": (this is the total price after tax, 2 decimal digits after points. This is the net total without tax, no double quotes),
                        "unitPrice": (this is the price of each single unit without tax, 2 decimal digits after points. Make sure you don't read the net price -total without tax- by mistake. To double check, you should get the same number if you divide the amount without tax by the quantity. No double quotes),
                        "Description": ,
                        "Quantity": (please double check the quantity by dividing Amount by Unit Price after removing tax. Only double check don't change the values. no double quotes),
                        "itemId": (this is its sequential order on the invoice 1,2,3, etc. no double quotes),
                        "pipCode": (this is the barcode of the product, typically on the column next to it or under it, it is never a float, it doesn't repeat),
                        "packSize": (this is the size of the pack, it usually is an int with a unit of measurement next to gm, tab, etc)
                    }]
                }` + text
        }],
        temperature: 0.0
    };

    try {
        const response = await axios.post(endpoint, data, { headers });
        return response.data;
    } catch (error) {
        console.error('Error calling OpenAI API:', error.response ? error.response.data : error.message);
        throw error;
    }
}

async function combineResults(fileHeader, pageResults) {
    const combinedResult = JSON.parse(fileHeader);
    combinedResult.data.Items = pageResults;
    return combinedResult;
}

export const processInvoice = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        // Add validation and debugging for the uploaded file
        if (!req.file.buffer || req.file.buffer.length === 0) {
            return res.status(400).json({ error: 'Empty file buffer' });
        }

        // Check if it's a valid PDF by looking for the PDF header
        const pdfHeader = req.file.buffer.slice(0, 5).toString();
        console.log('PDF Header:', pdfHeader);
        if (!pdfHeader.includes('%PDF-')) {
            return res.status(400).json({ error: 'Invalid PDF file: Missing PDF header' });
        }

        // Wait for Firebase initialization
        const { storage, db } = await firebaseInitializationPromise;

        // Process the PDF
        const pages = await readPdfFile(req.file.buffer);
        const results = [];
        
        // Parse header information
        const fileHeader = await parseGeneralTextWithOpenAI(pages[0]);
        
        // Parse line items from all pages
        for (let pageText of pages) {
            const response = await parseLineItemWithOpenAI(pageText);
            if (response?.choices?.[0]?.message?.content) {
                const parsedContent = JSON.parse(response.choices[0].message.content);
                results.push(...parsedContent.Items);
            }
        }

        // Combine results
        const finalResults = await combineResults(
            fileHeader.choices[0].message.content,
            results
        );

        // If filePath is provided in additionalData (from email processing), use it
        // Otherwise, store PDF in Firebase Storage with new path
        const filePath = req.additionalData?.filePath || `invoices/${Date.now()}-${req.file.originalname}`;
        
        if (!req.additionalData?.filePath) {
            const bucket = storage.bucket();
            const file = bucket.file(filePath);
            await file.save(req.file.buffer, {
                contentType: 'application/pdf',
                metadata: {
                    contentType: 'application/pdf'
                }
            });
        }

        // Store processed data in Firestore
        const invoiceRef = db.collection('invoices');
        const docRef = await invoiceRef.add({
            ...finalResults.data,
            filePath,
            processedAt: new Date(),
            status: 'processed',
            userId: req.user.uid
        });

        res.json({
            success: true,
            message: 'Invoice processed successfully',
            invoiceId: docRef.id,
            data: finalResults
        });

    } catch (error) {
        console.error('Failed to process invoice:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            stack: error.stack,
            request: {
                user: req.user?.uid,
                file: req.file ? {
                    originalname: req.file.originalname,
                    size: req.file.size,
                    mimetype: req.file.mimetype
                } : null,
                additionalData: req.additionalData || null
            }
        });
    }
};