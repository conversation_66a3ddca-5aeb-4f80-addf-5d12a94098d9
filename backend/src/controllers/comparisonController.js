import { firebaseInitializationPromise } from '../config/firebase.js';

// Compare invoice with order
const compareInvoiceWithOrder = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const { invoiceId, orderId } = req.body;
    
    if (!invoiceId || !orderId) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invoice ID and Order ID are required'
        }
      });
    }
    
    // Get the invoice
    const invoiceDoc = await db.collection('invoices').doc(invoiceId).get();
    
    if (!invoiceDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Invoice not found'
        }
      });
    }
    
    const invoiceData = invoiceDoc.data();
    
    // Check if the invoice belongs to the current user
    if (invoiceData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied to invoice'
        }
      });
    }
    
    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    
    if (!orderDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Order not found'
        }
      });
    }
    
    const orderData = orderDoc.data();
    
    // Check if the order belongs to the current user
    if (orderData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied to order'
        }
      });
    }
    
    // Check if comparison already exists
    const existingComparisonQuery = await db.collection('comparisons')
      .where('invoiceId', '==', invoiceId)
      .where('orderId', '==', orderId)
      .where('userId', '==', userId)
      .get();
    
    if (!existingComparisonQuery.empty) {
      // Return existing comparison
      const existingComparison = existingComparisonQuery.docs[0].data();
      return res.json({
        success: true,
        message: 'Comparison already exists',
        comparison: {
          id: existingComparisonQuery.docs[0].id,
          ...existingComparison,
          createdAt: existingComparison.createdAt?.toDate()?.toISOString() || null
        }
      });
    }
    
    // Perform comparison
    const discrepancies = [];
    
    // Compare PIP code
    if (invoiceData.pipCode !== orderData.pipCode) {
      discrepancies.push({
        field: 'pipCode',
        orderValue: orderData.pipCode,
        invoiceValue: invoiceData.pipCode,
        difference: 'PIP code mismatch'
      });
    }
    
    // Compare product name
    if (invoiceData.description !== orderData.description) {
      discrepancies.push({
        field: 'productName',
        orderValue: orderData.description,
        invoiceValue: invoiceData.description,
        difference: 'Product name mismatch'
      });
    }
    
    // Compare quantity
    if (invoiceData.quantity !== orderData.approvedQty) {
      discrepancies.push({
        field: 'quantity',
        orderValue: orderData.approvedQty,
        invoiceValue: invoiceData.quantity,
        difference: invoiceData.quantity - orderData.approvedQty
      });
    }
    
    // Compare price
    if (invoiceData.unitPrice !== orderData.price) {
      discrepancies.push({
        field: 'unitPrice',
        orderValue: orderData.price,
        invoiceValue: invoiceData.unitPrice,
        difference: invoiceData.unitPrice - orderData.price
      });
    }
    
    // Compare total
    const orderTotal = orderData.subTotal;
    const invoiceTotal = invoiceData.total;
    
    if (invoiceTotal !== orderTotal) {
      discrepancies.push({
        field: 'total',
        orderValue: orderTotal,
        invoiceValue: invoiceTotal,
        difference: invoiceTotal - orderTotal
      });
    }
    
    // Determine match status
    let matchStatus = 'exact';
    if (discrepancies.length > 0) {
      matchStatus = discrepancies.length > 2 ? 'mismatch' : 'partial';
    }
    
    // Create comparison record
    const comparisonRef = db.collection('comparisons').doc();
    const comparison = {
      id: comparisonRef.id,
      userId,
      invoiceId,
      orderId,
      matchStatus,
      discrepancies,
      createdAt: new Date(),
      resolved: false,
      resolutionNotes: ''
    };
    
    await comparisonRef.set(comparison);
    
    res.json({
      success: true,
      message: 'Comparison created successfully',
      comparison: {
        ...comparison,
        createdAt: comparison.createdAt.toISOString()
      }
    });
  } catch (error) {
    console.error('Error comparing invoice with order:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to compare invoice with order',
        details: error.message
      }
    });
  }
};

// Get all discrepancies
const getDiscrepancies = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { matchStatus, resolved, startDate, endDate } = req.query;
    
    // Start with base query
    let query = db.collection('comparisons').where('userId', '==', userId);
    
    // Apply filters if provided
    if (matchStatus) {
      query = query.where('matchStatus', '==', matchStatus);
    }
    
    if (resolved !== undefined) {
      const isResolved = resolved === 'true';
      query = query.where('resolved', '==', isResolved);
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const comparisons = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Apply date filtering in memory
      if (startDate || endDate) {
        const comparisonDate = data.createdAt?.toDate() || new Date(0);
        
        if (startDate && new Date(startDate) > comparisonDate) {
          return; // Skip this item
        }
        
        if (endDate && new Date(endDate) < comparisonDate) {
          return; // Skip this item
        }
      }
      
      comparisons.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate()?.toISOString() || null
      });
    });
    
    res.json({
      success: true,
      count: comparisons.length,
      comparisons
    });
  } catch (error) {
    console.error('Error getting discrepancies:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get discrepancies',
        details: error.message
      }
    });
  }
};

// Resolve discrepancy
const resolveDiscrepancy = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const comparisonId = req.params.id;
    const { resolved, resolutionNotes } = req.body;
    
    if (resolved === undefined) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Resolved status is required'
        }
      });
    }
    
    // Get the comparison
    const comparisonDoc = await db.collection('comparisons').doc(comparisonId).get();
    
    if (!comparisonDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Comparison not found'
        }
      });
    }
    
    const comparisonData = comparisonDoc.data();
    
    // Check if the comparison belongs to the current user
    if (comparisonData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }
    
    // Update the comparison
    const updateData = {
      resolved,
      updatedAt: new Date()
    };
    
    if (resolutionNotes !== undefined) {
      updateData.resolutionNotes = resolutionNotes;
    }
    
    await db.collection('comparisons').doc(comparisonId).update(updateData);
    
    // Get the updated comparison
    const updatedComparisonDoc = await db.collection('comparisons').doc(comparisonId).get();
    const updatedComparison = updatedComparisonDoc.data();
    
    res.json({
      success: true,
      message: 'Discrepancy resolution status updated',
      comparison: {
        id: updatedComparisonDoc.id,
        ...updatedComparison,
        createdAt: updatedComparison.createdAt?.toDate()?.toISOString() || null,
        updatedAt: updatedComparison.updatedAt?.toDate()?.toISOString() || null
      }
    });
  } catch (error) {
    console.error('Error resolving discrepancy:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to resolve discrepancy',
        details: error.message
      }
    });
  }
};

// Get comparison summary statistics
const getComparisonSummary = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { startDate, endDate } = req.query;
    
    // Start with base query
    const query = db.collection('comparisons').where('userId', '==', userId);
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const comparisons = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Apply date filtering in memory
      if (startDate || endDate) {
        const comparisonDate = data.createdAt?.toDate() || new Date(0);
        
        if (startDate && new Date(startDate) > comparisonDate) {
          return; // Skip this item
        }
        
        if (endDate && new Date(endDate) < comparisonDate) {
          return; // Skip this item
        }
      }
      
      comparisons.push(data);
    });
    
    // Calculate statistics
    const totalComparisons = comparisons.length;
    const matchStatusCounts = {
      exact: 0,
      partial: 0,
      mismatch: 0
    };
    
    const resolvedCounts = {
      resolved: 0,
      unresolved: 0
    };
    
    let totalDiscrepancies = 0;
    const discrepancyTypeCount = {};
    
    for (const comparison of comparisons) {
      // Count by match status
      matchStatusCounts[comparison.matchStatus] = (matchStatusCounts[comparison.matchStatus] || 0) + 1;
      
      // Count by resolution status
      if (comparison.resolved) {
        resolvedCounts.resolved++;
      } else {
        resolvedCounts.unresolved++;
      }
      
      // Count discrepancies
      if (comparison.discrepancies) {
        totalDiscrepancies += comparison.discrepancies.length;
        
        // Count by discrepancy type
        for (const discrepancy of comparison.discrepancies) {
          discrepancyTypeCount[discrepancy.field] = (discrepancyTypeCount[discrepancy.field] || 0) + 1;
        }
      }
    }
    
    // Calculate percentages
    const percentageResolved = totalComparisons > 0 
      ? (resolvedCounts.resolved / totalComparisons) * 100 
      : 0;
    
    const percentageExactMatch = totalComparisons > 0 
      ? (matchStatusCounts.exact / totalComparisons) * 100 
      : 0;
    
    res.json({
      success: true,
      summary: {
        totalComparisons,
        matchStatusCounts,
        resolvedCounts,
        totalDiscrepancies,
        discrepancyTypeCount,
        percentageResolved: Math.round(percentageResolved * 100) / 100, // Round to 2 decimal places
        percentageExactMatch: Math.round(percentageExactMatch * 100) / 100 // Round to 2 decimal places
      }
    });
  } catch (error) {
    console.error('Error getting comparison summary:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get comparison summary',
        details: error.message
      }
    });
  }
};

// Compare invoice with statement
const compareInvoiceWithStatement = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const { invoiceId, statementId } = req.body;
    
    if (!invoiceId || !statementId) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invoice ID and Statement ID are required'
        }
      });
    }
    
    // Get the invoice
    const invoiceDoc = await db.collection('invoices').doc(invoiceId).get();
    
    if (!invoiceDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Invoice not found'
        }
      });
    }
    
    const invoiceData = invoiceDoc.data();
    
    // Check if the invoice belongs to the current user
    if (invoiceData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied to invoice'
        }
      });
    }
    
    // Get the statement
    const statementDoc = await db.collection('statements').doc(statementId).get();
    
    if (!statementDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Statement not found'
        }
      });
    }
    
    const statementData = statementDoc.data();
    
    // Check if the statement belongs to the current user
    if (statementData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied to statement'
        }
      });
    }
    
    // Find the invoice in the statement
    const statementInvoice = statementData.invoices?.find(inv => inv.invoiceId === invoiceId);
    
    if (!statementInvoice) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Invoice not found in statement'
        }
      });
    }
    
    // Perform comparison
    const discrepancies = [];
    
    // Compare date
    const invoiceDate = invoiceData.date instanceof Date ? invoiceData.date : new Date(invoiceData.date);
    const statementInvoiceDate = statementInvoice.date instanceof Date ? statementInvoice.date : new Date(statementInvoice.date);
    
    if (invoiceDate.getTime() !== statementInvoiceDate.getTime()) {
      discrepancies.push({
        invoiceId,
        field: 'date',
        statementValue: statementInvoiceDate.toISOString(),
        invoiceValue: invoiceDate.toISOString(),
        difference: 'Date mismatch'
      });
    }
    
    // Compare amount
    if (invoiceData.total !== statementInvoice.amount) {
      discrepancies.push({
        invoiceId,
        field: 'amount',
        statementValue: statementInvoice.amount,
        invoiceValue: invoiceData.total,
        difference: statementInvoice.amount - invoiceData.total
      });
    }
    
    // Determine match status
    let matchStatus = 'matched';
    if (discrepancies.length > 0) {
      matchStatus = discrepancies.length > 1 ? 'unmatched' : 'partial';
    }
    
    // Update the statement invoice status
    const updatedInvoices = statementData.invoices.map(inv => {
      if (inv.invoiceId === invoiceId) {
        return {
          ...inv,
          status: matchStatus
        };
      }
      return inv;
    });
    
    await db.collection('statements').doc(statementId).update({
      invoices: updatedInvoices,
      discrepancies: [...(statementData.discrepancies || []), ...discrepancies]
    });
    
    // Get the updated statement
    const updatedStatementDoc = await db.collection('statements').doc(statementId).get();
    const updatedStatement = updatedStatementDoc.data();
    
    res.json({
      success: true,
      message: 'Invoice compared with statement successfully',
      matchStatus,
      discrepancies,
      statement: {
        id: updatedStatementDoc.id,
        ...updatedStatement
      }
    });
  } catch (error) {
    console.error('Error comparing invoice with statement:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to compare invoice with statement',
        details: error.message
      }
    });
  }
};

export {
  compareInvoiceWithOrder,
  getDiscrepancies,
  resolveDiscrepancy,
  getComparisonSummary,
  compareInvoiceWithStatement
};