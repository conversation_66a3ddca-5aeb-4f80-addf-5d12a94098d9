import { setTimeout } from 'node:timers/promises';
import puppeteer from 'puppeteer';
import fs from 'fs/promises';
import { firebaseInitializationPromise } from '../config/firebase.js';

const scrapeOrders = async (req, res) => {
    let browser = null;
    let context = null;

    console.log('Starting order scraping process with params:', req.body);
    try {
        // Debug: Log environment variables
        console.log('PUPPETEER_CACHE_DIR:', process.env.PUPPETEER_CACHE_DIR);

        // Get Chrome executable path from Puppeteer
        const chromePath = puppeteer.executablePath();
        console.log('Puppeteer using Chrome at:', chromePath);

        // Initialize Firebase first to fail early if there's an issue
        const { db } = await firebaseInitializationPromise;

        // Get user settings from Firebase
        const userId = req.user.uid;
        const userSettingsDoc = await db.collection('userSettings').doc(userId).get();

        if (!userSettingsDoc.exists) {
            throw new Error('User settings not found');
        }

        const userSettings = userSettingsDoc.data();

        if (!userSettings.drugComparisonEmail || !userSettings.drugComparisonPassword) {
            throw new Error('Drug comparison credentials not configured');
        }

        // Launch browser with explicit executable path and Render-specific configuration
        console.log('Launching browser...');
        browser = await puppeteer.launch({
            headless: true, // Standard for server-side scraping
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-gpu',
                '--disable-dev-shm-usage',
                '--disable-software-rasterizer',
                '--disable-features=TranslateUI',
                '--disable-background-networking',
                '--disable-background-timer-throttling',
                '--disable-breakpad',
                '--disable-client-side-phishing-detection',
                '--disable-default-apps',
                '--disable-domain-reliability',
                '--disable-hang-monitor',
                '--disable-popup-blocking',
                '--disable-sync',
                '--disable-web-resources',
                '--hide-scrollbars',
                '--metrics-recording-only',
                '--no-first-run',
                '--no-zygote'
            ]
        });
        console.log('Browser launched successfully');

        // Create page with retry mechanism
        let page;
        let retries = 3;
        while (retries > 0) {
            try {
                page = await browser.newPage();
                // Test the connection by evaluating a simple expression
                await page.evaluate(() => true);
                break;
            } catch (err) {
                retries--;
                if (retries === 0) throw err;
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        // Enable console logging from the browser
        page.on('console', (msg) => {
            for (let i = 0; i < msg.args().length; ++i)
                console.log(`${i}: ${msg.args()[i]}`);
        });

        // Login process
        await page.goto('https://www.drugcomparison.co.uk/customers/login', {
            waitUntil: 'networkidle2',
            timeout: 30000 // Increase timeout
        });
        await page.type('#CustomerEmail', userSettings.drugComparisonEmail);
        await page.type('#CustomerPassword', userSettings.drugComparisonPassword);

        await Promise.all([
            page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 }),
            page.click('.signinbtn')
        ]);

        // Additional wait to ensure page is fully loaded
        await setTimeout(2000);

        // Handle notification if present with better waiting and visibility check
        try {
            await page.waitForSelector('.closeallnoti', { visible: true, timeout: 5000 });
            await page.evaluate(() => {
                const closeBtn = document.querySelector('.closeallnoti');
                if (closeBtn && window.getComputedStyle(closeBtn).display !== 'none') {
                    closeBtn.click();
                }
            });
            // Add a small delay after clicking to ensure the notification closes
            await setTimeout(1000);
        } catch (err) {
            console.log('No notification to close or notification already closed');
        }

        // Parse and validate date parameters
        const { startDate, endDate } = req.body;
        console.log('Received date params:', { startDate, endDate });
        
        // Use provided dates or fall back to today
        const today = new Date();
        const dd = String(today.getDate()).padStart(2, '0');
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const yyyy = today.getFullYear();

        let start_date, end_date;

        // Validate date formats (YYYY-MM-DD HH:mm)
        const dateRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/;
        
        if (startDate && dateRegex.test(startDate)) {
          start_date = startDate;
        } else {
          start_date = `${yyyy}-${mm}-${dd} 00:00`;
          console.log('Using default start date:', start_date);
        }

        if (endDate && dateRegex.test(endDate)) {
          end_date = endDate;
        } else {
          end_date = `${yyyy}-${mm}-${dd} 23:59`;
          console.log('Using default end date:', end_date);
        }

        // Navigate to orders page
        const ordersUrl = `https://www.drugcomparison.co.uk/orders/searchorder?start_date=${encodeURIComponent(start_date)}&end_date=${encodeURIComponent(end_date)}`;
        await page.goto(ordersUrl, {
            waitUntil: 'networkidle0',
            timeout: 30000
        });

        // Additional wait to ensure page is stable
        await setTimeout(2000);

        // Handle PIN if required
        const newPinSelector = '#ReportpincodePasscode';
        if (await page.$(newPinSelector)) {
            if (!userSettings.drugComparisonPin) {
                throw new Error('Drug comparison PIN required but not configured');
            }
            // Set up dialog handler before submitting PIN
            page.on('dialog', async (dialog) => {
                console.log('Dialog appeared:', dialog.message());
                await dialog.accept();
                console.log('Dialog accepted');
            });

            await page.type(newPinSelector, userSettings.drugComparisonPin);
            await Promise.all([
                page.click('#passcodesubmit'),
                page.waitForNavigation({ waitUntil: 'networkidle2' }),
            ]);
        }

        // Parse the request date to get the date part for querying existing orders
        const scrapeDateStr = start_date.split(' ')[0]; // Get just the date part
        
        // Get existing orders for the specified date for this user
        const existingOrders = await db.collection('orders')
            .where('scrapedDate', '==', scrapeDateStr)
            .where('userId', '==', userId)
            .get();

        // Create array of existing order keys
        const existingKeys = existingOrders.docs.map(doc => {
            const data = doc.data();
            return `${data.orderNo}-${data.pipCode}-${data.dateTime}`;
        });

        // Wait for table to load
        await page.waitForSelector('#myTable', { timeout: 30000 });

        // Check table content and count valid orders
        const { hasValidData, totalScraped } = await page.evaluate(() => {
            const tbody = document.querySelector('#myTable tbody');
            if (!tbody) return { hasValidData: false, totalScraped: 0 };

            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Check for "NO RECORDS FOUND!" message
            if (rows.length === 1 && rows[0].textContent.trim() === 'NO RECORDS FOUND!') {
                return { hasValidData: false, totalScraped: 0 };
            }

            // Filter valid rows (must have at least 18 columns as per the data structure)
            const validRows = rows.filter(row => {
                const cells = row.querySelectorAll('td');
                return cells.length >= 18;
            });

            return {
                hasValidData: validRows.length > 0,
                totalScraped: validRows.length
            };
        });

        if (!hasValidData) {
            if (browser) {
                await browser.close();
                browser = null;
            }
            console.log('No valid orders found');
            res.setHeader('Content-Type', 'application/json');
            return res.status(200).json({
                success: true,
                message: `No valid orders found for the period ${start_date} to ${end_date}`,
                ordersCount: 0,
                totalScraped: 0,
                skippedCount: 0,
                dates: {
                    startDate: start_date,
                    endDate: end_date
                }
            });
        }

        // Scrape orders data
        const ordersData = await page.evaluate((existingKeys) => {
            const rows = Array.from(document.querySelectorAll('#myTable tbody tr'));
            // Only process rows that have the required number of columns
            const dataRows = rows.filter(row => row.querySelectorAll('td').length >= 18);
            const orders = [];

            for (const row of dataRows) {
                const cells = row.querySelectorAll('td');
                const orderNo = cells[4]?.innerText.trim() || null;
                const pipCode = cells[5]?.innerText.trim() || null;
                const dateTime = cells[3]?.innerText.trim() || null;

                // Skip if order already exists
                const key = `${orderNo}-${pipCode}-${dateTime}`;
                if (existingKeys.includes(key)) continue;

                // Log the raw dateTime value for debugging
                console.log('Raw dateTime from scraping:', dateTime);
                
                // Ensure dateTime is in a consistent format (DD-MM-YYYY HH:mm)
                let formattedDateTime = dateTime || '';
                if (dateTime) {
                    try {
                        // Check if it's already in the expected format
                        const dateTimeRegex = /^\d{2}-\d{2}-\d{4} \d{2}:\d{2}$/;
                        if (!dateTimeRegex.test(dateTime)) {
                            // If not, try to parse and reformat it
                            const dateParts = dateTime.split(/[\s\/\-:]+/);
                            if (dateParts.length >= 5) {
                                // Assuming format might be DD/MM/YYYY HH:mm or similar
                                const day = dateParts[0].padStart(2, '0');
                                const month = dateParts[1].padStart(2, '0');
                                const year = dateParts[2].length === 2 ? `20${dateParts[2]}` : dateParts[2];
                                const hour = dateParts[3].padStart(2, '0');
                                const minute = dateParts[4].padStart(2, '0');
                                formattedDateTime = `${day}-${month}-${year} ${hour}:${minute}`;
                                console.log('Reformatted dateTime:', formattedDateTime);
                            }
                        }
                    } catch (error) {
                        console.error('Error formatting dateTime:', error);
                    }
                }
                
                // Add new order
                orders.push({
                    sn: cells[0]?.innerText?.trim() || '',
                    dateTime: formattedDateTime,
                    orderNo: orderNo || '',
                    pipCode: pipCode || '',
                    description: cells[6]?.innerText?.trim() || '',
                    orderQty: Number(cells[7]?.innerText?.trim().replace(/[^0-9.-]+/g, '')) || 0,
                    approvedQty: Number(cells[8]?.innerText?.trim().replace(/[^0-9.-]+/g, '')) || 0,
                    price: Number(cells[9]?.innerText?.trim().replace(/[^0-9.-]+/g, '')) || 0,
                    dtPrice: Number(cells[10]?.innerText?.trim().replace(/[^0-9.-]+/g, '')) || 0,
                    subTotal: Number(cells[11]?.innerText?.trim().replace(/[^0-9.-]+/g, '')) || 0,
                    category: cells[12]?.innerText?.trim() || '',
                    supplier: cells[13]?.innerText?.trim() || '',
                    response: cells[14]?.innerText?.trim() || '',
                    discount: cells[15]?.innerText?.trim() || '',
                    notes: cells[16]?.innerText?.trim() || '',
                    orderRef: cells[17]?.innerText?.trim() || '',
                });
            }
            return orders;
        }, existingKeys);

        // Store new orders in Firestore using batch
        if (ordersData.length > 0) {
            if (ordersData.length === 0) {
                res.setHeader('Content-Type', 'application/json');
                return res.status(200).json({
                    success: true,
                    message: totalScraped === 0
                        ? `No new orders found for the period ${start_date} to ${end_date}`
                        : 'All orders have already been retrieved',
                    ordersCount: 0,
                    totalScraped,
                    skippedCount: totalScraped,
                    dates: {
                        startDate: start_date,
                        endDate: end_date
                    }
                });
            }

            const batch = db.batch();
            for (const order of ordersData) {
                const orderRef = db.collection('orders').doc();
                batch.set(orderRef, {
                    ...order,
                    createdAt: new Date(),
                    scrapedDate: scrapeDateStr,
                    userId: userId
                });
            }
            await batch.commit();
        }

        // Close browser
        await browser.close();
        browser = null;

        // Calculate stats
        const skippedCount = totalScraped - ordersData.length;

        // Send success response
        res.setHeader('Content-Type', 'application/json');
        res.status(200).json({
            success: true,
            message: `Successfully scraped ${totalScraped} orders (${ordersData.length} new, ${skippedCount} duplicates skipped)`,
            ordersCount: ordersData.length,
            totalScraped,
            skippedCount
        });

    } catch (error) {
        console.error('Error in scrapeOrders:', error.message);
        console.error('Stack trace:', error.stack);
        
        // Ensure proper headers are set even for errors
        res.setHeader('Content-Type', 'application/json');
        res.status(500).json({
            success: false,
            error: {
                message: error.message,
                type: error.name,
                details: process.env.NODE_ENV === 'development' ? error.stack : undefined
            }
        });
    } finally {
        // Ensure browser is always closed
        if (browser) {
            try {
                await browser.close();
            } catch (err) {
                console.error('Error closing browser:', err);
            }
            browser = null;
        }
    }
};

export { scrapeOrders };