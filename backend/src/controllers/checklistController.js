import { firebaseInitializationPromise } from '../config/firebase.js';
import {
  generateChecklistItemsFromOrder,
  generateChecklistItemsFromOrders,
  getSupplierTemplate
} from '../utils/orderToChecklistUtils.js';

// Create new checklist items
const createChecklistItems = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const { items } = req.body;

    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid request. Items array is required.'
        }
      });
    }

    // Validate each item
    for (const item of items) {
      if (!item.productName || !item.pipCode || !item.orderId || item.expectedQuantity === undefined) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Invalid item. Required fields: productName, pipCode, orderId, expectedQuantity'
          }
        });
      }
    }

    // Add items to Firestore
    const batch = db.batch();
    const createdItems = [];

    for (const item of items) {
      const checklistItemRef = db.collection('checklistItems').doc();
      const checklistItem = {
        ...item,
        id: checklistItemRef.id,
        userId,
        receivedQuantity: item.receivedQuantity || 0,
        status: item.status || 'pending',
        notes: item.notes || '',
        dateChecked: item.dateChecked || null,
        checkedBy: item.checkedBy || '',
        createdAt: new Date()
      };

      batch.set(checklistItemRef, checklistItem);
      createdItems.push(checklistItem);
    }

    await batch.commit();

    res.status(201).json({
      success: true,
      message: `Created ${items.length} checklist items`,
      items: createdItems
    });
  } catch (error) {
    console.error('Error creating checklist items:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create checklist items',
        details: error.message
      }
    });
  }
};

// Retrieve checklist items
const getChecklistItems = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { orderId, status, supplier, startDate, endDate } = req.query;
    
    // Start with base query
    let query = db.collection('checklistItems').where('userId', '==', userId);
    
    // Apply filters if provided
    if (orderId) {
      query = query.where('orderId', '==', orderId);
    }
    
    if (status) {
      query = query.where('status', '==', status);
    }
    
    if (supplier) {
      query = query.where('supplier', '==', supplier);
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const items = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Apply date filtering in memory (Firestore doesn't support range queries on multiple fields)
      if (startDate || endDate) {
        const itemDate = data.createdAt?.toDate() || new Date(0);
        
        if (startDate && new Date(startDate) > itemDate) {
          return; // Skip this item
        }
        
        if (endDate && new Date(endDate) < itemDate) {
          return; // Skip this item
        }
      }
      
      items.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate()?.toISOString() || null,
        dateChecked: data.dateChecked?.toDate()?.toISOString() || null
      });
    });
    
    res.json({
      success: true,
      count: items.length,
      items
    });
  } catch (error) {
    console.error('Error retrieving checklist items:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to retrieve checklist items',
        details: error.message
      }
    });
  }
};

// Update item status
const updateChecklistItem = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const itemId = req.params.id;
    const { status, receivedQuantity, notes } = req.body;
    
    // Validate status
    const validStatuses = ['pending', 'received', 'incorrect', 'missing'];
    if (status && !validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
        }
      });
    }
    
    // Get the item
    const itemDoc = await db.collection('checklistItems').doc(itemId).get();
    
    if (!itemDoc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Checklist item not found'
        }
      });
    }
    
    const itemData = itemDoc.data();
    
    // Check if the item belongs to the current user
    if (itemData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }
    
    // Prepare update data
    const updateData = {
      updatedAt: new Date()
    };
    
    if (status) {
      updateData.status = status;
      updateData.dateChecked = new Date();
      updateData.checkedBy = req.user.email || userId;
    }
    
    if (receivedQuantity !== undefined) {
      updateData.receivedQuantity = receivedQuantity;
    }
    
    if (notes !== undefined) {
      updateData.notes = notes;
    }
    
    // Update the item
    await db.collection('checklistItems').doc(itemId).update(updateData);
    
    // Get the updated item
    const updatedItemDoc = await db.collection('checklistItems').doc(itemId).get();
    const updatedItem = updatedItemDoc.data();
    
    res.json({
      success: true,
      message: 'Checklist item updated successfully',
      item: {
        id: updatedItemDoc.id,
        ...updatedItem,
        createdAt: updatedItem.createdAt?.toDate()?.toISOString() || null,
        dateChecked: updatedItem.dateChecked?.toDate()?.toISOString() || null,
        updatedAt: updatedItem.updatedAt?.toDate()?.toISOString() || null
      }
    });
  } catch (error) {
    console.error('Error updating checklist item:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update checklist item',
        details: error.message
      }
    });
  }
};

// Bulk update items
const bulkUpdateItems = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const { items, updates } = req.body;
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid request. Items array is required.'
        }
      });
    }
    
    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid request. Updates object is required.'
        }
      });
    }
    
    // Validate status if provided
    const validStatuses = ['pending', 'received', 'incorrect', 'missing'];
    if (updates.status && !validStatuses.includes(updates.status)) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
        }
      });
    }
    
    // Prepare update data
    const updateData = {
      updatedAt: new Date()
    };
    
    if (updates.status) {
      updateData.status = updates.status;
      updateData.dateChecked = new Date();
      updateData.checkedBy = req.user.email || userId;
    }
    
    if (updates.notes !== undefined) {
      updateData.notes = updates.notes;
    }
    
    // For receivedQuantity, we'll handle it separately for each item if provided
    
    // Update items in batch
    const batch = db.batch();
    const updatedItems = [];
    const errors = [];
    
    for (const itemId of items) {
      try {
        // Get the item
        const itemDoc = await db.collection('checklistItems').doc(itemId).get();
        
        if (!itemDoc.exists) {
          errors.push({
            itemId,
            error: 'Item not found'
          });
          continue;
        }
        
        const itemData = itemDoc.data();
        
        // Check if the item belongs to the current user
        if (itemData.userId !== userId) {
          errors.push({
            itemId,
            error: 'Access denied'
          });
          continue;
        }
        
        // Create item-specific update data
        const itemUpdateData = { ...updateData };
        
        // Handle receivedQuantity if provided in updates
        if (updates.receivedQuantity !== undefined) {
          // If it's a number, use it directly
          if (typeof updates.receivedQuantity === 'number') {
            itemUpdateData.receivedQuantity = updates.receivedQuantity;
          }
          // If it's a function of expected quantity (e.g., "match"), calculate it
          else if (updates.receivedQuantity === 'match' && itemData.expectedQuantity !== undefined) {
            itemUpdateData.receivedQuantity = itemData.expectedQuantity;
          }
          // Otherwise, leave it unchanged
        }
        
        // Update the item
        batch.update(db.collection('checklistItems').doc(itemId), itemUpdateData);
        updatedItems.push(itemId);
      } catch (error) {
        errors.push({
          itemId,
          error: error.message
        });
      }
    }
    
    // Commit the batch
    await batch.commit();
    
    res.json({
      success: true,
      message: `Updated ${updatedItems.length} checklist items`,
      updatedItems,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (error) {
    console.error('Error bulk updating checklist items:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to bulk update checklist items',
        details: error.message
      }
    });
  }
};

// Get checklist statistics
const getChecklistSummary = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { orderId, supplier, startDate, endDate } = req.query;
    
    // Start with base query
    let query = db.collection('checklistItems').where('userId', '==', userId);
    
    // Apply filters if provided
    if (orderId) {
      query = query.where('orderId', '==', orderId);
    }
    
    if (supplier) {
      query = query.where('supplier', '==', supplier);
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const items = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Apply date filtering in memory
      if (startDate || endDate) {
        const itemDate = data.createdAt?.toDate() || new Date(0);
        
        if (startDate && new Date(startDate) > itemDate) {
          return; // Skip this item
        }
        
        if (endDate && new Date(endDate) < itemDate) {
          return; // Skip this item
        }
      }
      
      items.push(data);
    });
    
    // Calculate statistics
    const totalItems = items.length;
    const statusCounts = {
      pending: 0,
      received: 0,
      incorrect: 0,
      missing: 0
    };
    
    let totalExpectedQuantity = 0;
    let totalReceivedQuantity = 0;
    
    for (const item of items) {
      // Count by status
      statusCounts[item.status] = (statusCounts[item.status] || 0) + 1;
      
      // Sum quantities
      totalExpectedQuantity += item.expectedQuantity || 0;
      totalReceivedQuantity += item.receivedQuantity || 0;
    }
    
    // Calculate percentages
    const percentageChecked = totalItems > 0 
      ? ((statusCounts.received + statusCounts.incorrect + statusCounts.missing) / totalItems) * 100 
      : 0;
    
    const percentageReceived = totalExpectedQuantity > 0 
      ? (totalReceivedQuantity / totalExpectedQuantity) * 100 
      : 0;
    
    res.json({
      success: true,
      summary: {
        totalItems,
        statusCounts,
        totalExpectedQuantity,
        totalReceivedQuantity,
        percentageChecked: Math.round(percentageChecked * 100) / 100, // Round to 2 decimal places
        percentageReceived: Math.round(percentageReceived * 100) / 100, // Round to 2 decimal places
        discrepancyCount: statusCounts.incorrect + statusCounts.missing
      }
    });
  } catch (error) {
    console.error('Error getting checklist summary:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get checklist summary',
        details: error.message
      }
    });
  }
};

// Generate checklist from order
const generateChecklistFromOrder = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { orderId, useTemplate } = req.body;
    
    if (!orderId) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Order ID is required'
        }
      });
    }
    
    try {
      // Use the utility function to generate checklist items
      const generatedItems = await generateChecklistItemsFromOrder(orderId, userId);
      
      // If useTemplate is true, apply supplier-specific template if available
      if (useTemplate && generatedItems.length > 0) {
        const { db } = await firebaseInitializationPromise;
        const item = generatedItems[0];
        
        if (item.supplier) {
          const template = await getSupplierTemplate(item.supplier, userId);
          
          if (template) {
            // Apply template properties to the item
            await db.collection('checklistItems').doc(item.id).update({
              templateId: template.id,
              // Add any template-specific fields here
              notes: template.defaultNotes || item.notes
            });
            
            // Update the item in our response
            item.templateId = template.id;
            item.notes = template.defaultNotes || item.notes;
          }
        }
      }
      
      res.status(201).json({
        success: true,
        message: 'Checklist item generated from order',
        items: generatedItems.map(item => ({
          ...item,
          createdAt: item.createdAt instanceof Date ? item.createdAt.toISOString() : item.createdAt
        }))
      });
    } catch (error) {
      if (error.message.includes('already exist')) {
        return res.status(409).json({
          success: false,
          error: {
            message: error.message
          }
        });
      }
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: {
            message: error.message
          }
        });
      }
      
      if (error.message.includes('Access denied')) {
        return res.status(403).json({
          success: false,
          error: {
            message: error.message
          }
        });
      }
      
      throw error;
    }
  } catch (error) {
    console.error('Error generating checklist from order:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to generate checklist from order',
        details: error.message
      }
    });
  }
};

// Batch generate checklist items from multiple orders
const batchGenerateChecklistItems = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { orderIds, useTemplate } = req.body;
    
    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Order IDs array is required'
        }
      });
    }
    
    try {
      // Use the utility function to generate checklist items from multiple orders
      const result = await generateChecklistItemsFromOrders(orderIds, userId);
      
      // If useTemplate is true, apply supplier-specific templates if available
      if (useTemplate && result.generatedItems && result.generatedItems.length > 0) {
        const { db } = await firebaseInitializationPromise;
        const batch = db.batch();
        const supplierTemplates = {};
        
        // Group items by supplier
        const itemsBySupplier = {};
        for (const item of result.generatedItems) {
          if (item.supplier) {
            if (!itemsBySupplier[item.supplier]) {
              itemsBySupplier[item.supplier] = [];
            }
            itemsBySupplier[item.supplier].push(item);
          }
        }
        
        // Apply templates for each supplier
        for (const supplier in itemsBySupplier) {
          // Get or cache the template
          if (!supplierTemplates[supplier]) {
            supplierTemplates[supplier] = await getSupplierTemplate(supplier, userId);
          }
          
          const template = supplierTemplates[supplier];
          if (template) {
            for (const item of itemsBySupplier[supplier]) {
              // Update the item with template properties
              batch.update(db.collection('checklistItems').doc(item.id), {
                templateId: template.id,
                notes: template.defaultNotes || item.notes
              });
              
              // Update the item in our response
              item.templateId = template.id;
              item.notes = template.defaultNotes || item.notes;
            }
          }
        }
        
        // Commit the batch if there are updates
        if (Object.keys(itemsBySupplier).length > 0) {
          await batch.commit();
        }
      }
      
      res.status(201).json({
        success: true,
        message: `Generated ${result.generatedItems.length} checklist items from ${orderIds.length} orders`,
        items: result.generatedItems.map(item => ({
          ...item,
          createdAt: item.createdAt instanceof Date ? item.createdAt.toISOString() : item.createdAt
        })),
        errors: result.errors
      });
    } catch (error) {
      if (error.message.includes('Order IDs array is required')) {
        return res.status(400).json({
          success: false,
          error: {
            message: error.message
          }
        });
      }
      
      throw error;
    }
  } catch (error) {
    console.error('Error batch generating checklist items:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to batch generate checklist items',
        details: error.message
      }
    });
  }
};

export {
  createChecklistItems,
  getChecklistItems,
  updateChecklistItem,
  bulkUpdateItems,
  getChecklistSummary,
  generateChecklistFromOrder,
  batchGenerateChecklistItems
};