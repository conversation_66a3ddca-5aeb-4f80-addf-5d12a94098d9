import axios from 'axios';
import FormData from 'form-data';
import Imap from 'node-imap';
import { simpleParser } from 'mailparser';
import { firebaseInitializationPromise } from '../config/firebase.js';
import { processPdf } from '../controllers/pdfTypeController.js';
import { processInvoice } from '../controllers/invoiceController.js';
import { processStatement } from '../controllers/statementController.js';
import { readPdfFile } from '../utils/pdfUtils.js';

import { determinePdfType as pdfTypeDetector } from '../controllers/pdfTypeController.js';

// Helper function to determine PDF type
async function determinePdfType(content) {
  try {
    return await pdfTypeDetector(content);
  } catch (error) {
    console.error('Error determining PDF type:', error);
    throw error;
  }
}

async function getImapConfig(userId) {
  const { db } = await firebaseInitializationPromise;
  const userSettingsDoc = await db.collection('userSettings').doc(userId).get();
  
  if (!userSettingsDoc.exists) {
    throw new Error('User settings not found');
  }

  const settings = userSettingsDoc.data();
  // Check which settings are missing
  const missingSettings = [];
  if (!settings.imapUser) missingSettings.push('Email username');
  if (!settings.imapPassword) missingSettings.push('Email password');
  if (!settings.imapHost) missingSettings.push('Email host');

  if (missingSettings.length > 0) {
    throw new Error(`Email settings not properly configured. Missing: ${missingSettings.join(', ')}`);
  }

  // Create config with defaults
  const config = {
    user: settings.imapUser,
    password: settings.imapPassword,
    host: settings.imapHost,
    port: settings.imapPort || 993, // Default IMAP port
    tls: settings.imapTls ?? true, // Default to TLS
    tlsOptions: settings.imapTlsOptions || {}, // Optional TLS settings
    connTimeout: settings.imapConnTimeout || 30000,
    authTimeout: settings.imapAuthTimeout || 30000,
    debug: settings.imapDebug || null // Optional debug handler
  };

  return config;
}

async function isEmailProcessed(userId, messageId, senderEmail) {
  const { db } = await firebaseInitializationPromise;
  const emailSnapshot = await db
    .collection('emails')
    .where('userId', '==', userId)
    .where('messageId', '==', messageId)
    .where('from', '==', senderEmail)
    .limit(1)
    .get();
  
  return !emailSnapshot.empty;
}

async function handleAttachments(userId, attachments, mail, toAddresses, senderEmail, messageId) {
  const { storage, db } = await firebaseInitializationPromise;
  const results = [];

  for (const attachment of attachments) {
    if (attachment.contentType === 'application/pdf') {
      try {
        // Store original email metadata
        const docRef = db.collection('emails').doc();
        await docRef.set({
          to: toAddresses,
          from: senderEmail,
          subject: mail.subject,
          timestamp: new Date(),
          userId: userId,
          messageId: messageId
        });

        // Process the PDF file
        const processingResult = await callPdfProcessor(userId, attachment);
        
        // Update email document with processing results
        await docRef.update({
          attachment: processingResult.filePath,
          documentType: processingResult.documentType,
          processingStatus: {
            invoicesProcessed: processingResult.invoicesProcessed,
            statementsProcessed: processingResult.statementsProcessed
          }
        });

        results.push({
          filename: attachment.filename,
          status: 'success',
          message: 'Processed and stored successfully',
          documentType: processingResult.documentType,
          processed: processingResult.invoicesProcessed + processingResult.statementsProcessed
        });
      } catch (error) {
        console.error(`Error processing attachment ${attachment.filename}:`, error);
        results.push({
          filename: attachment.filename,
          status: 'error',
          message: error.message
        });
      }
    } else {
      results.push({
        filename: attachment.filename,
        status: 'skipped',
        message: 'Not a PDF file'
      });
    }
  }
  return results;
}

async function callPdfProcessor(userId, attachment) {
  const { storage } = await firebaseInitializationPromise;

  try {
    // Upload PDF to Firebase Storage
    const filename = `invoices/${Date.now()}-${attachment.filename}`;
    const bucket = storage.bucket();
    const file = bucket.file(filename);
    await file.save(attachment.content, {
      contentType: 'application/pdf',
      metadata: {
        contentType: 'application/pdf'
      }
    });

    // Create a proper mock response object
    const mockRes = {
      json: (data) => data,
      status: function(code) { return this; }
    };

    // Determine document type
    const documentType = await determinePdfType(attachment.content);
    
    let processingResult;
    if (documentType === 'invoice') {
      processingResult = await processInvoice({
        file: {
          buffer: attachment.content,
          originalname: attachment.filename
        },
        user: { uid: userId },
        additionalData: { filePath: filename }
      }, mockRes);
    } else if (documentType === 'statement') {
      processingResult = await processStatement({
        file: {
          buffer: attachment.content,
          originalname: attachment.filename
        },
        user: { uid: userId }
      }, mockRes);
    }

    return {
      success: processingResult?.success || false,
      invoicesProcessed: documentType === 'invoice' ? 1 : 0,
      statementsProcessed: documentType === 'statement' ? 1 : 0,
      documentType,
      filePath: filename
    };

  } catch (error) {
    console.error('Failed to process document:', error);
    throw error;
  }
}

export const checkEmails = async (req, res) => {
  const userId = req.user.uid;
  let imap;

  try {
    const imapConfig = await getImapConfig(userId);
    const results = {
      processed: 0,
      errors: [],
      details: []
    };

    const processEmails = () => new Promise((resolve, reject) => {
      imap = new Imap(imapConfig);

      function openInbox(cb) {
        imap.openBox('INBOX', false, cb);
      }

      imap.once('ready', () => {
        openInbox(async (err, box) => {
          if (err) {
            reject(err);
            return;
          }

          // Create date string for yesterday in IMAP format (DD-MMM-YYYY)
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);
          // Format date in DD-MMM-YYYY format required by IMAP
          const searchDate = yesterday.toLocaleString('en-US', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
          }).replace(/,/g, '');

          console.log('[Debug] IMAP Connection Details:', {
            host: imapConfig.host,
            port: imapConfig.port,
            user: imapConfig.user,
            tls: imapConfig.tls,
            searchDate
          });

          // Search for all emails since yesterday, both seen and unseen
          console.log(`[Debug] Searching for emails since: ${searchDate}`);
          
          imap.search([['SINCE', searchDate]], async (err, searchResults) => {
            if (err) {
              console.error('[Debug] IMAP search error:', err);
              reject(err);
              return;
            }

            console.log(`[Debug] Found ${searchResults.length} emails since ${searchDate}`);

            if (!searchResults.length) {
              resolve({ message: 'No emails found in the last 24 hours', results });
              imap.end();
              return;
            }

            const fetch = imap.fetch(searchResults, { bodies: '', struct: true, markSeen: true });
            
            fetch.on('message', (msg, seqno) => {
              msg.on('body', (stream, info) => {
                simpleParser(stream, async (err, mail) => {
                  if (err) {
                    console.error('[Debug] Parser error:', err);
                    results.errors.push({ seqno, error: err.message });
                    return;
                  }

                  try {
                    console.log(`[Debug] Processing email [${seqno}]:
                      Subject: ${mail.subject}
                      From: ${mail.from?.value[0]?.address}
                      MessageId: ${mail.messageId}
                      Has Attachments: ${mail.attachments?.length || 0}
                    `);

                    const senderEmail = mail.from.value[0].address.toLowerCase();
                    const messageId = mail.messageId || `${seqno}-${Date.now()}`; // Fallback if no messageId
                    const toAddresses = mail.to.value.map(to => to.address.toLowerCase());

                    // Check if email was already processed
                    const alreadyProcessed = await isEmailProcessed(userId, messageId, senderEmail);
                    if (alreadyProcessed) {
                      console.log(`[Debug] Email [${seqno}] was already processed`);
                      results.details.push({
                        seqno,
                        status: 'skipped',
                        reason: 'Email already processed'
                      });
                      return;
                    }

                    // Only process if there are attachments
                    if (mail.attachments?.length > 0) {
                      console.log(`[Debug] Processing ${mail.attachments.length} attachments for email [${seqno}]`);
                      console.log('[Debug] Attachment types:', mail.attachments.map(att => att.contentType).join(', '));
                      const attachmentResults = await handleAttachments(
                        userId,
                        mail.attachments,
                        mail,
                        toAddresses,
                        senderEmail,
                        messageId
                      );
                      results.processed++;
                      results.details.push({
                        seqno,
                        status: 'processed',
                        attachments: attachmentResults
                      });
                    }
                  } catch (error) {
                    results.errors.push({
                      seqno,
                      error: error.message
                    });
                  }
                });
              });
            });

            fetch.once('end', () => {
              imap.expunge(() => {
                imap.end();
                resolve(results);
              });
            });
          });
        });
      });

      imap.once('error', (err) => {
        console.error('IMAP error:', err);
        let errorType = 'IMAP error';
        let errorMessage = err.message;

        // Handle specific IMAP errors
        if (err.textCode === 'AUTHENTICATIONFAILED' ||
            err.message.includes('Invalid credentials') ||
            err.message.includes('Authentication failed')) {
          errorType = 'Authentication error';
          errorMessage = 'Invalid email credentials. Please verify your email username and password in settings.';
        } else if (err.code === 'ECONNREFUSED' || err.code === 'ETIMEDOUT') {
          errorType = 'Connection error';
          errorMessage = 'Unable to connect to email server. Please verify your host and port settings.';
        }

        const error = new Error(errorMessage);
        error.type = errorType;
        error.originalError = err;

        results.errors.push({
          type: errorType,
          message: errorMessage,
          code: err.code
        });
        
        reject(error);
      });

      imap.once('end', () => {
        console.log('Connection ended');
      });

      imap.connect();
    });

    const processResults = await processEmails();
    res.json(processResults);

  } catch (error) {
    console.error('Error in checkEmails:', error);
    let statusCode = 500;
    let errorMessage = error.message;
    
    // Handle specific error cases
    if (error.message.includes('Email settings not properly configured')) {
      statusCode = 400;
      errorMessage = error.message;
    } else if (error.message === 'User settings not found') {
      statusCode = 404;
      errorMessage = 'Email settings not found. Please configure your email settings first.';
    } else if (error.type === 'Authentication error') {
      statusCode = 401;
      errorMessage = error.message;
    } else if (error.type === 'Connection error' || error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      statusCode = 503;
      errorMessage = error.message || 'Unable to connect to email server. Please check your email settings and try again.';
    }

    // Add error details for better debugging
    const errorDetails = {
      type: error.type || 'Unknown error',
      code: error.code,
      originalError: error.originalError ? {
        message: error.originalError.message,
        code: error.originalError.code,
        textCode: error.originalError.textCode
      } : undefined
    };

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      details: {
        ...errorDetails,
        suggestion: statusCode === 401 ?
          'Please check your email username and password in the settings page.' :
          statusCode === 503 ?
          'Please verify your IMAP host and port settings and ensure the email server is accessible.' :
          statusCode === 400 ?
          'Please configure all required email settings in the settings page.' :
          'Please review your email settings and try again.'
      }
    });
  } finally {
    if (imap && imap.state !== 'disconnected') {
      imap.end();
    }
  }
};

export const processEmails = async (req, res) => {
  try {
    await checkEmails(req, res);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};
