import { firebaseInitializationPromise } from '../config/firebase.js';

// Update notification preferences
const updateNotificationSettings = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const { settings } = req.body;
    
    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Notification settings object is required'
        }
      });
    }
    
    // Get user settings
    const userSettingsDoc = await db.collection('userSettings').doc(userId).get();
    
    let userSettings = {};
    if (userSettingsDoc.exists) {
      userSettings = userSettingsDoc.data();
    }
    
    // Update notification settings
    const updatedSettings = {
      ...userSettings,
      notificationSettings: {
        ...(userSettings.notificationSettings || {}),
        ...settings
      },
      updatedAt: new Date()
    };
    
    await db.collection('userSettings').doc(userId).set(updatedSettings);
    
    res.json({
      success: true,
      message: 'Notification settings updated successfully',
      settings: updatedSettings.notificationSettings
    });
  } catch (error) {
    console.error('Error updating notification settings:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update notification settings',
        details: error.message
      }
    });
  }
};

// Get activity log
const getActivityLog = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { action, startDate, endDate, limit } = req.query;
    
    // Start with base query
    let query = db.collection('activityLog')
      .where('userId', '==', userId)
      .orderBy('timestamp', 'desc');
    
    // Apply filters if provided
    if (action) {
      query = query.where('action', '==', action);
    }
    
    // Apply limit if provided
    if (limit) {
      query = query.limit(parseInt(limit));
    } else {
      query = query.limit(100); // Default limit
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const activities = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Apply date filtering in memory
      if (startDate || endDate) {
        const activityDate = data.timestamp?.toDate() || new Date(0);
        
        if (startDate && new Date(startDate) > activityDate) {
          return; // Skip this item
        }
        
        if (endDate && new Date(endDate) < activityDate) {
          return; // Skip this item
        }
      }
      
      activities.push({
        id: doc.id,
        ...data,
        timestamp: data.timestamp?.toDate()?.toISOString() || null
      });
    });
    
    res.json({
      success: true,
      count: activities.length,
      activities
    });
  } catch (error) {
    console.error('Error getting activity log:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get activity log',
        details: error.message
      }
    });
  }
};

// Get audit trail
const getAuditTrail = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    
    // Get query parameters for filtering
    const { entityType, entityId, startDate, endDate, limit } = req.query;
    
    // Start with base query
    let query = db.collection('auditTrail')
      .where('userId', '==', userId)
      .orderBy('timestamp', 'desc');
    
    // Apply filters if provided
    if (entityType) {
      query = query.where('entityType', '==', entityType);
    }
    
    if (entityId) {
      query = query.where('entityId', '==', entityId);
    }
    
    // Apply limit if provided
    if (limit) {
      query = query.limit(parseInt(limit));
    } else {
      query = query.limit(100); // Default limit
    }
    
    // Execute query
    const snapshot = await query.get();
    
    // Process results
    const auditEntries = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      
      // Apply date filtering in memory
      if (startDate || endDate) {
        const entryDate = data.timestamp?.toDate() || new Date(0);
        
        if (startDate && new Date(startDate) > entryDate) {
          return; // Skip this item
        }
        
        if (endDate && new Date(endDate) < entryDate) {
          return; // Skip this item
        }
      }
      
      auditEntries.push({
        id: doc.id,
        ...data,
        timestamp: data.timestamp?.toDate()?.toISOString() || null
      });
    });
    
    res.json({
      success: true,
      count: auditEntries.length,
      auditTrail: auditEntries
    });
  } catch (error) {
    console.error('Error getting audit trail:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get audit trail',
        details: error.message
      }
    });
  }
};

export {
  updateNotificationSettings,
  getActivityLog,
  getAuditTrail
};