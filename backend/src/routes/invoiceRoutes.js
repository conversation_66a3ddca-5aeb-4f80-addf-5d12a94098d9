import { Router } from 'express';
import { processInvoice } from '../controllers/invoiceController.js';
import { checkAuth } from '../middlewares/auth.js';
import { upload } from '../middlewares/upload.js';
import { firebaseInitializationPromise } from '../config/firebase.js';
const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// GET /api/invoices - Get all invoices
router.get('/', async (req, res) => {
  try {
    console.log('GET /api/invoices - Fetching invoices');
    const { db } = await firebaseInitializationPromise;
    console.log('Firebase initialized successfully');
    
    const userId = req.user.uid;
    const invoicesRef = db.collection('invoices');
    // Get all documents for this user
    console.log('Executing Firestore query for user:', userId);
    const invoicesSnapshot = await invoicesRef.where('userId', '==', userId).get();
    
    console.log(`Found ${invoicesSnapshot.size} invoices`);
    
    const invoices = [];
    invoicesSnapshot.forEach(doc => {
      console.log(`Processing invoice ${doc.id}`);
      const data = doc.data();

      // Ensure consistent date formatting
      const formatDate = (date) => {
        if (!date) return '';
        if (typeof date === 'string') return date;
        if (date.toDate) {
          // Handle Firestore Timestamp
          const d = date.toDate();
          return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
        }
        return '';
      };

      invoices.push({
        id: doc.id,
        ...data,
        InvoiceDate: formatDate(data.InvoiceDate),
        DueDate: formatDate(data.DueDate),
        processedAt: formatDate(data.processedAt),
        orderReconciled: Boolean(data.orderReconciled),
        statementReconciled: Boolean(data.statementReconciled),
        InvoiceTotal: Number(data.InvoiceTotal) || 0,
        Items: Array.isArray(data.Items) ? data.Items : []
      });
    });

    res.setHeader('Content-Type', 'application/json');
    // Always return an array, even if empty
    res.json(invoices);
  } catch (error) {
    console.error('Error details:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch invoices',
        details: error.message
      }
    });
  }
});

// GET /api/invoices/:id - Get a specific invoice
router.get('/:id', async (req, res) => {
// DEBUG: Log headers to diagnose missing token
  console.log('Request headers:', req.headers);
  try {
    console.log(`GET /api/invoices/${req.params.id} - Fetching specific invoice`);
    const { db } = await firebaseInitializationPromise;
    
    const userId = req.user.uid;
    const invoiceRef = db.collection('invoices').doc(req.params.id);
    const doc = await invoiceRef.get();

    if (!doc.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Invoice not found'
        }
      });
    }

    const invoiceData = doc.data();
    if (invoiceData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied'
        }
      });
    }

    // Ensure consistent date formatting
    const formatDate = (date) => {
      if (!date) return '';
      if (typeof date === 'string') return date;
      if (date.toDate) {
        const d = date.toDate();
        return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
      }
      return '';
    };

    const invoice = {
      id: doc.id,
      ...invoiceData,
      InvoiceDate: formatDate(invoiceData.InvoiceDate),
      DueDate: formatDate(invoiceData.DueDate),
      processedAt: formatDate(invoiceData.processedAt),
      orderReconciled: Boolean(invoiceData.orderReconciled),
      statementReconciled: Boolean(invoiceData.statementReconciled),
      InvoiceTotal: Number(invoiceData.InvoiceTotal) || 0,
      Items: Array.isArray(invoiceData.Items) ? invoiceData.Items : []
    };

    res.setHeader('Content-Type', 'application/json');
    res.json(invoice);
  } catch (error) {
    console.error('Error details:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch invoice',
        details: error.message
      }
    });
  }
});

// POST /api/invoices/process - Process a PDF invoice
router.post('/process', 
  upload.single('file'),
  (req, res, next) => {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'No file uploaded'
        }
      });
    }
    if (req.file.mimetype !== 'application/pdf') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid file type. Only PDF files are allowed.'
        }
      });
    }
    next();
  },
  processInvoice
);

// GET /api/invoices/:id/pdf - Get the PDF file for a specific invoice
// POST /api/invoices/delete - Delete multiple invoices
router.post('/delete', async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid request: ids array is required'
        }
      });
    }

    // Delete invoices one by one, checking permissions for each
    const batch = db.batch();
    const results = [];

    // First verify all invoices exist and belong to the user
    for (const id of ids) {
      const doc = await db.collection('invoices').doc(id).get();
      if (!doc.exists) {
        return res.status(404).json({
          success: false,
          error: {
            message: `Invoice ${id} not found`
          }
        });
      }
      
      const invoiceData = doc.data();
      if (invoiceData.userId !== userId) {
        return res.status(403).json({
          success: false,
          error: {
            message: `Access denied for invoice ${id}`
          }
        });
      }

      // Add delete operation to batch
      batch.delete(db.collection('invoices').doc(id));
      results.push(id);
    }

    // Execute the batch
    await batch.commit();

    res.json({
      success: true,
      message: `Successfully deleted ${results.length} invoices`,
      deletedIds: results
    });
  } catch (error) {
    console.error('Error deleting invoices:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to delete invoices',
        details: error.message
      }
    });
  }
});

router.get('/:id/pdf', checkAuth, async (req, res) => {
  try {
    // Get the invoice document first to verify it exists and check permissions
    const { db, storage } = await firebaseInitializationPromise;
    const userId = req.user.uid;
    const doc = await db.collection('invoices').doc(req.params.id).get();
    
    if (!doc.exists) {
      return res.status(404).json({
        success: false,
        error: { message: 'Invoice not found' }
      });
    }

    const invoiceData = doc.data();
    if (invoiceData.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: { message: 'Access denied' }
      });
    }

    const data = doc.data();
    if (!data.pdfUrl) {
      return res.status(404).json({
        success: false,
        error: { message: 'PDF file not found for this invoice' }
      });
    }

    // Get storage bucket
    const bucket = storage.bucket();
    console.log("[PDF Route] Using bucket:", bucket.name);

    // Extract filename from pdfUrl
    let decodedPath;
    if (data.pdfUrl.startsWith("gs://")) {
      decodedPath = data.pdfUrl.replace(/^gs:\/\/[^/]+\//, "");
    } else if (data.pdfUrl.includes("/o/")) {
      const pdfPath = data.pdfUrl.split('/o/')[1].split('?')[0];
      decodedPath = decodeURIComponent(pdfPath);
    } else if (!data.pdfUrl.startsWith("http")) {
      decodedPath = data.pdfUrl;
    } else {
      return res.status(400).json({
        success: false,
        error: { message: "Invalid pdfUrl format" }
      });
    }

    console.log("[PDF Route] Attempting to access file:", decodedPath);

    // Get file from bucket
    const file = bucket.file(decodedPath);
    const [exists] = await file.exists();
    console.log("[PDF Route] File exists:", exists);

    if (!exists) {
      console.error("[PDF Route] PDF file not found in storage:", decodedPath);
      return res.status(404).json({
        success: false,
        error: { message: 'PDF file not found in storage' }
      });
    }

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="invoice-${req.params.id}.pdf"`);

    const stream = file.createReadStream();
    stream.pipe(res);

    stream.on('error', (error) => {
      console.error('Error streaming PDF:', error);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: { message: 'Error streaming PDF file', details: error.message }
        });
      }
    });
    stream.on('end', () => {
      console.log("[PDF Route] Successfully streamed PDF:", decodedPath);
    });
  } catch (error) {
    console.error('Error fetching PDF:', error);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to fetch PDF file',
          details: error.message
        }
      });
    }
  }
});

export default router;