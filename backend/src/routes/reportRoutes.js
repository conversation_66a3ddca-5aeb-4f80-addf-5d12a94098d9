import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.js';
import {
  generateReport,
  getAllReports,
  downloadReport,
  emailReport,
  getSupplierMetrics,
  getFinancialImpact
} from '../controllers/reportController.js';

const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// POST /api/reports/generate - Generate new report
router.post('/generate', generateReport);

// GET /api/reports - Get all reports
router.get('/', getAllReports);

// GET /api/reports/:id/download - Download report
router.get('/:id/download', downloadReport);

// POST /api/reports/email - Email report
router.post('/email', emailReport);

// GET /api/reports/suppliers - Get supplier performance metrics
router.get('/suppliers', getSupplierMetrics);

// GET /api/reports/financial - Get financial impact analysis
router.get('/financial', getFinancialImpact);

export default router;