import { Router } from 'express';
import { login, getUserSettings, updateUserSettings, devGetPassword, sendPasswordReset } from '../controllers/authController.js';
import { checkAuth } from '../middlewares/auth.js';

const router = Router();

// Public routes
router.post('/login', login);

// Password reset endpoint
router.post('/reset-password', sendPasswordReset);

// DEVELOPMENT ONLY: Get password for test user
router.get('/dev-get-password', devGetPassword);

// Protected routes
router.use(checkAuth); // Apply auth middleware to all routes below
router.get('/settings', getUserSettings);
router.put('/settings', updateUserSettings);

export default router;