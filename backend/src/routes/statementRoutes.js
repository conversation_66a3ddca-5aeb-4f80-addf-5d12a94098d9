import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.js';
import { upload } from '../middlewares/upload.js';
import {
  getAllStatements,
  getStatementById,
  uploadStatement,
  processStatementsFromEmail,
  getStatementFile // Import the new function
} from '../controllers/statementController.js';

const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// GET /api/statements - Get all statements
router.get('/', getAllStatements);

// GET /api/statements/:id - Get specific statement
router.get('/:id', getStatementById);

// POST /api/statements/upload - Upload statement PDF/CSV
router.post('/upload', upload.single('file'), uploadStatement);

// POST /api/statements/process-email - Process statements from email
router.post('/process-email', processStatementsFromEmail);

// GET /api/statements/:id/pdf - Get statement file content
router.get('/:id/pdf', getStatementFile);

export default router;