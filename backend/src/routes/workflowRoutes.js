import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.js';
import {
  getDashboardData,
  getWorkflowStatus,
  completeWorkflowStep,
  skipWorkflowStep
} from '../controllers/workflowController.js';

const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// GET /api/dashboard - Get dashboard data
router.get('/dashboard', getDashboardData);

// GET /api/workflow/status - Get workflow status
router.get('/status', getWorkflowStatus);

// POST /api/workflow/steps/:id/complete - Complete workflow step
router.post('/steps/:id/complete', completeWorkflowStep);

// POST /api/workflow/steps/:id/skip - Skip workflow step
router.post('/steps/:id/skip', skipWorkflowStep);

export default router;