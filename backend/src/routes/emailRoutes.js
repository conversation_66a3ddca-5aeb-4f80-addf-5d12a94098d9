import { Router } from 'express';
import { processEmails } from '../controllers/emailController.js';
import { checkAuth } from '../middlewares/auth.js';
import { firebaseInitializationPromise } from '../config/firebase.js';

const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// GET /api/emails - Get all processed emails
router.get('/', async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    
    const emailsRef = db.collection('emails');
    const q = emailsRef.orderBy('timestamp', 'desc');
    const emailsSnapshot = await q.get();
    
    const emails = [];
    emailsSnapshot.forEach(doc => {
      const data = doc.data();
      // Ensure consistent date formatting
      const formatDate = (date) => {
        if (!date) return '';
        if (typeof date === 'string') return date;
        if (date.toDate) {
          // Handle Firestore Timestamp
          const d = date.toDate();
          return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
        }
        return '';
      };

      emails.push({
        id: doc.id,
        ...data,
        timestamp: formatDate(data.timestamp),
        to: Array.isArray(data.to) ? data.to : [data.to],
        supplier: data.supplier || {}
      });
    });

    res.setHeader('Content-Type', 'application/json');
    // Always return an array, even if empty
    res.json(emails);
  } catch (error) {
    console.error('Error fetching emails:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch emails',
        details: error.message
      }
    });
  }
});

// POST /api/emails/process - Manually trigger email processing
router.post('/process', processEmails);

export default router;