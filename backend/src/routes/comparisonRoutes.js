import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.js';
import {
  compareInvoiceWithOrder,
  getDiscrepancies,
  resolveDiscrepancy,
  getComparisonSummary,
  compareInvoiceWithStatement
} from '../controllers/comparisonController.js';

const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// POST /api/compare/invoice-order - Compare invoice with order
router.post('/invoice-order', compareInvoiceWithOrder);

// GET /api/compare/discrepancies - Get all discrepancies
router.get('/discrepancies', getDiscrepancies);

// PATCH /api/compare/resolve/:id - Resolve discrepancy
router.patch('/resolve/:id', resolveDiscrepancy);

// GET /api/compare/summary - Get comparison statistics
router.get('/summary', getComparisonSummary);

// POST /api/compare/invoice-statement - Compare invoice with statement
router.post('/invoice-statement', compareInvoiceWithStatement);

export default router;