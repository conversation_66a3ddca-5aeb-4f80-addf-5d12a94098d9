import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.js';
import {
  updateNotificationSettings,
  getActivityLog,
  getAuditTrail
} from '../controllers/activityController.js';

const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// POST /api/notifications/settings - Update notification preferences
router.post('/settings', updateNotificationSettings);

// GET /api/activity-log - Get activity history
router.get('/activity-log', getActivityLog);

// GET /api/audit-trail - Get audit trail
router.get('/audit-trail', getAuditTrail);

export default router;