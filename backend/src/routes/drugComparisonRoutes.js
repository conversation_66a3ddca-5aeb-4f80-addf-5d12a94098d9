import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.js';
import { scrapeOrders } from '../controllers/drugComparisonController.js';
import { firebaseInitializationPromise } from '../config/firebase.js';

const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// GET /api/scrape/orders - Get all scraped orders
router.get('/orders', async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    
    const userId = req.user.uid;
    const ordersRef = db.collection('orders');
    const q = ordersRef.where('userId', '==', userId);
    const ordersSnapshot = await q.get();
    
    const orders = [];
    ordersSnapshot.forEach(doc => {
      const data = doc.data();
      // Ensure consistent date formatting
      const formatDate = (date) => {
        if (!date) return '';
        if (typeof date === 'string') return date;
        if (date.toDate) {
          // Handle Firestore Timestamp
          const d = date.toDate();
          return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
        }
        return '';
      };

      const createdAt = data.createdAt?.toDate() || new Date(0);
      orders.push({
        id: doc.id,
        ...data,
        createdAt: formatDate(data.createdAt),
        dateTime: data.dateTime || '',
        // Ensure numeric fields are numbers
        orderQty: parseFloat(data.orderQty) || 0,
        approvedQty: parseFloat(data.approvedQty) || 0,
        price: parseFloat(data.price) || 0,
        dtPrice: parseFloat(data.dtPrice) || 0,
        subTotal: parseFloat(data.subTotal) || 0,
        discount: parseFloat(data.discount) || 0
      });
    });

    res.setHeader('Content-Type', 'application/json');
    // Sort orders by createdAt descending
    orders.sort((a, b) => {
      const dateA = new Date(a.createdAt);
      const dateB = new Date(b.createdAt);
      return dateB.getTime() - dateA.getTime();
    });

    // Always return an array, even if empty
    res.json(orders);
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch orders',
        details: error.message
      }
    });
  }
});

// POST /api/scrape/retrieve - Scrape orders from drugcomparison
router.post('/retrieve', async (req, res, next) => {
  console.log('Received request at /api/scrape/retrieve');
  console.log('Request body:', req.body);
  try {
    await scrapeOrders(req, res);
  } catch (error) {
    console.error('Error in scrape route:', error);
    next(error);
  }
});

export default router;