import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.js';
import { getAllOrders, getOrderById, updateOrderStatus, syncOrders } from '../controllers/orderController.js';

const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// GET /api/orders - Get all orders
router.get('/', getAllOrders);

// GET /api/orders/sync - Force synchronization with Drug Comparison
// This route must be defined before the /:id route to avoid conflicts
router.post('/sync', syncOrders);

// GET /api/orders/:id - Get specific order details
router.get('/:id', getOrderById);

// PATCH /api/orders/:id - Update order status
router.patch('/:id', updateOrderStatus);

export default router;