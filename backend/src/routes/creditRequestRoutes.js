import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.js';
import { upload } from '../middlewares/upload.js';
import {
  createCreditRequest,
  getAllCreditRequests,
  updateCreditRequestStatus,
  sendEmailToSupplier,
  uploadImages,
  createBatchCreditRequests,
  getEmailTemplates
} from '../controllers/creditRequestController.js';

const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// POST /api/credit-requests - Create new request
router.post('/', createCreditRequest);

// GET /api/credit-requests - Get all requests
router.get('/', getAllCreditRequests);

// PATCH /api/credit-requests/:id - Update request status
router.patch('/:id', updateCreditRequestStatus);

// POST /api/credit-requests/:id/email - Send email to supplier
router.post('/:id/email', sendEmailToSupplier);

// POST /api/credit-requests/:id/images - Upload images
router.post('/:id/images', upload.array('images'), uploadImages);

// POST /api/credit-requests/batch - Create batch requests
router.post('/batch', createBatchCreditRequests);

// GET /api/credit-requests/templates - Get email templates
router.get('/templates', getEmailTemplates);

export default router;