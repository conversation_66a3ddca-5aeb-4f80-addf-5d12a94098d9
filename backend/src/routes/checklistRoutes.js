import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.js';
import {
  createChecklistItems,
  getChecklistItems,
  updateChecklistItem,
  bulkUpdateItems,
  getChecklistSummary,
  generateChecklistFromOrder,
  batchGenerateChecklistItems
} from '../controllers/checklistController.js';

const router = Router();

// Protect all routes with authentication
router.use(checkAuth);

// POST /api/checklist/items - Create new checklist items
router.post('/items', createChecklistItems);

// GET /api/checklist/items - Retrieve checklist items
router.get('/items', getChecklistItems);

// PATCH /api/checklist/items/:id - Update item status
router.patch('/items/:id', updateChecklistItem);

// POST /api/checklist/bulk - Bulk update items
router.post('/bulk', bulkUpdateItems);

// GET /api/checklist/summary - Get checklist statistics
router.get('/summary', getChecklistSummary);

// POST /api/checklist/generate - Generate checklist from order
router.post('/generate', generateChecklistFromOrder);

// POST /api/checklist/batch-generate - Batch generate checklist items from multiple orders
router.post('/batch-generate', batchGenerateChecklistItems);

export default router;