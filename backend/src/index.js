import express from 'express';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import dotenv from 'dotenv';
import authRoutes from './routes/authRoutes.js';
import drugComparisonRoutes from './routes/drugComparisonRoutes.js';
import orderRoutes from './routes/orderRoutes.js';
import checklistRoutes from './routes/checklistRoutes.js';
import comparisonRoutes from './routes/comparisonRoutes.js';
import reportRoutes from './routes/reportRoutes.js';
import creditRequestRoutes from './routes/creditRequestRoutes.js';
import workflowRoutes from './routes/workflowRoutes.js';
import activityRoutes from './routes/activityRoutes.js';
import emailRoutes from './routes/emailRoutes.js';
import invoiceRoutes from './routes/invoiceRoutes.js';
import statementRoutes from './routes/statementRoutes.js';
import pdfRoutes from './routes/pdfRoutes.js';
import reconciliationRoutes from './routes/reconciliationRoutes.js';
import { firebaseInitializationPromise } from './config/firebase.js';

import fs from 'fs';
import puppeteer from 'puppeteer';

// Load environment variables
dotenv.config();

const app = express();
app.use(cookieParser());
const PORT = process.env.PORT || 3000;
const isDevelopment = process.env.NODE_ENV === 'development';
const allowedOrigins = [
  'http://localhost:3001',
  'https://pharmaccounts-frontend.onrender.com'
];


// Initialize Firebase before setting up routes
async function initializeApp() {
  try {
    console.log('Initializing Firebase...');
    const { db } = await firebaseInitializationPromise;
    console.log('Firebase initialized successfully');
    app.locals.db = db;

    // CORS configuration with better error handling
    app.use(cors({
      origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);

        if (allowedOrigins.indexOf(origin) === -1) {
          const msg = 'The CORS policy for this site does not allow access from the specified Origin.';
          return callback(new Error(msg), false);
        }
        return callback(null, true);
      },
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization'],
      credentials: true,
      optionsSuccessStatus: 200 // Some legacy browsers (IE11, various SmartTVs) choke on 204
    }));

    // Add a pre-flight route handler
    app.options('*', cors());

    // Basic middlewares
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    // Debug middleware
    app.use((req, res, next) => {
      console.log(`${req.method} ${req.url}`);
      next();
    });

    // Error handling for JSON parsing
    app.use((err, req, res, next) => {
      if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
        return res.status(400).json({ error: 'Invalid JSON' });
      }
      next();
    });

    // Routes
    app.use('/api/auth', authRoutes);
    app.use('/api/scrape', drugComparisonRoutes);
    app.use('/api/orders', orderRoutes);
    app.use('/api/checklist', checklistRoutes);
    app.use('/api/compare', comparisonRoutes);
    app.use('/api/reports', reportRoutes);
    app.use('/api/credit-requests', creditRequestRoutes);
    app.use('/api/workflow', workflowRoutes);
    app.use('/api/notifications', activityRoutes);
    app.use('/api/activity-log', activityRoutes);
    app.use('/api/audit-trail', activityRoutes);
    app.use('/api/emails', emailRoutes);
    app.use('/api/invoices', invoiceRoutes);
    app.use('/api/statements', statementRoutes);
    app.use('/api/pdf', pdfRoutes);
    app.use('/api/reconciliation', reconciliationRoutes);

    // Health check endpoint
    app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        endpoints: {
          invoices: {
            list: 'GET /api/invoices',
            upload: 'POST /api/invoices/process'
          },
          drugComparison: 'GET /api/scrape/scrape',
          orders: {
            list: 'GET /api/orders',
            details: 'GET /api/orders/:id',
            updateStatus: 'PATCH /api/orders/:id',
            sync: 'GET /api/orders/sync'
          },
          checklist: {
            create: 'POST /api/checklist/items',
            list: 'GET /api/checklist/items',
            update: 'PATCH /api/checklist/items/:id',
            bulk: 'POST /api/checklist/bulk',
            summary: 'GET /api/checklist/summary',
            generate: 'POST /api/checklist/generate',
            batchGenerate: 'POST /api/checklist/batch-generate'
          },
          compare: {
            invoiceOrder: 'POST /api/compare/invoice-order',
            discrepancies: 'GET /api/compare/discrepancies',
            resolve: 'PATCH /api/compare/resolve/:id',
            summary: 'GET /api/compare/summary',
            invoiceStatement: 'POST /api/compare/invoice-statement'
          },
          statements: {
            list: 'GET /api/statements',
            details: 'GET /api/statements/:id',
            upload: 'POST /api/statements/upload'
          },
          reports: {
            generate: 'POST /api/reports/generate',
            list: 'GET /api/reports',
            download: 'GET /api/reports/:id/download',
            email: 'POST /api/reports/email',
            suppliers: 'GET /api/reports/suppliers',
            financial: 'GET /api/reports/financial'
          },
          creditRequests: {
            create: 'POST /api/credit-requests',
            list: 'GET /api/credit-requests',
            update: 'PATCH /api/credit-requests/:id',
            email: 'POST /api/credit-requests/:id/email',
            images: 'POST /api/credit-requests/:id/images',
            batch: 'POST /api/credit-requests/batch',
            templates: 'GET /api/credit-requests/templates'
          },
          workflow: {
            dashboard: 'GET /api/workflow/dashboard',
            status: 'GET /api/workflow/status',
            completeStep: 'POST /api/workflow/steps/:id/complete',
            skipStep: 'POST /api/workflow/steps/:id/skip'
          },
          notifications: {
            settings: 'POST /api/notifications/settings'
          },
          activityLog: 'GET /api/activity-log',
          auditTrail: 'GET /api/audit-trail',
          emails: 'POST /api/emails/process',
          pdf: {
            determineType: 'POST /api/pdf/determine-type'
          },
          reconciliation: {
            summary: 'GET /api/reconciliation/summary'
          }
        }
      });
    });

    // Enhanced global error handling middleware
    app.use((err, req, res, next) => {
      console.error('Error:', err);

      // Ensure we're sending JSON response
      res.setHeader('Content-Type', 'application/json');

      // Handle CORS errors
      if (err.message && err.message.includes('CORS')) {
        return res.status(403).json({
          error: {
            message: 'CORS error',
            details: err.message
          }
        });
      }

      // Handle Multer errors
      if (err.name === 'MulterError') {
        return res.status(400).json({
          error: {
            message: 'File upload error',
            details: err.message
          }
        });
      }

      // Handle Firebase errors
      if (err.name === 'FirebaseError') {
        return res.status(500).json({
          error: {
            message: 'Database operation failed',
            details: err.message
          }
        });
      }

      // Handle Puppeteer errors
      if (err.message && (
        err.message.includes('Chrome') ||
        err.message.includes('Puppeteer') ||
        err.message.includes('browser')
      )) {
        return res.status(500).json({
          error: {
            message: 'Browser automation error',
            details: isDevelopment ? err.message : 'Internal browser error'
          }
        });
      }

      // Handle other errors
      res.status(err.status || 500).json({
        error: {
          message: isDevelopment ? err.message : 'Internal Server Error',
          ...(isDevelopment && { stack: err.stack }),
          status: err.status || 500
        }
      });
    });

    // Start server
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      console.log('Available endpoints:');
      console.log('- GET  /health                   - Health check and endpoint listing');
      console.log('- GET  /api/scrape/orders       - List all scraped orders');
      console.log('- GET  /api/scrape/scrape       - Scrape orders from Drug Comparison');
      console.log('- GET  /api/orders              - List all orders');
      console.log('- GET  /api/orders/:id          - Get specific order details');
      console.log('- PATCH /api/orders/:id         - Update order status');
      console.log('- GET  /api/orders/sync         - Force synchronization with Drug Comparison');
      console.log('- POST /api/checklist/items     - Create new checklist items');
      console.log('- GET  /api/checklist/items     - Retrieve checklist items');
      console.log('- PATCH /api/checklist/items/:id - Update item status');
      console.log('- POST /api/checklist/bulk      - Bulk update items');
      console.log('- GET  /api/checklist/summary   - Get checklist statistics');
      console.log('- POST /api/checklist/generate  - Generate checklist from order');
      console.log('- POST /api/checklist/batch-generate - Batch generate checklist items from orders');
      console.log('- POST /api/compare/invoice-order - Compare invoice with order');
      console.log('- GET  /api/compare/discrepancies - Get all discrepancies');
      console.log('- PATCH /api/compare/resolve/:id - Resolve discrepancy');
      console.log('- GET  /api/compare/summary    - Get comparison statistics');
      console.log('- POST /api/compare/invoice-statement - Compare invoice with statement');
      console.log('- GET  /api/statements         - Get all statements');
      console.log('- GET  /api/statements/:id     - Get specific statement');
      console.log('- POST /api/statements/upload  - Upload statement PDF/CSV');
      console.log('- POST /api/reports/generate   - Generate new report');
      console.log('- GET  /api/reports            - Get all reports');
      console.log('- GET  /api/reports/:id/download - Download report');
      console.log('- POST /api/reports/email      - Email report');
      console.log('- GET  /api/reports/suppliers  - Get supplier performance metrics');
      console.log('- GET  /api/reports/financial  - Get financial impact analysis');
      console.log('- POST /api/credit-requests    - Create new credit request');
      console.log('- GET  /api/credit-requests    - Get all credit requests');
      console.log('- PATCH /api/credit-requests/:id - Update credit request status');
      console.log('- POST /api/credit-requests/:id/email - Send email to supplier');
      console.log('- POST /api/credit-requests/:id/images - Upload images');
      console.log('- POST /api/credit-requests/batch - Create batch credit requests');
      console.log('- GET  /api/credit-requests/templates - Get email templates');
      console.log('- GET  /api/workflow/dashboard  - Get dashboard data');
      console.log('- GET  /api/workflow/status     - Get workflow status');
      console.log('- POST /api/workflow/steps/:id/complete - Complete workflow step');
      console.log('- POST /api/workflow/steps/:id/skip - Skip workflow step');
      console.log('- POST /api/notifications/settings - Update notification preferences');
      console.log('- GET  /api/activity-log        - Get activity history');
      console.log('- GET  /api/audit-trail         - Get audit trail');
      console.log('- GET  /api/emails              - List all processed emails');
      console.log('- POST /api/emails/process      - Process incoming emails for invoices');
      console.log('- GET  /api/invoices           - List all invoices');
      console.log('- POST /api/invoices/process    - Process PDF invoices');
      console.log('- GET  /api/statements         - List all statements');
      console.log('- POST /api/statements/process  - Process PDF statements');
      console.log('- POST /api/pdf/determine-type - Determine if PDF is invoice or statement');
      // Print the Puppeteer version (just for debugging)
      console.log('Puppeteer version:', puppeteer.version);

      // Get Puppeteer's idea of the Chromium executable path
      const chromePath = puppeteer.executablePath();
      console.log('Puppeteer executablePath:', chromePath);

      // Check if that path actually exists on the filesystem
      const chromeExists = fs.existsSync(chromePath);
      console.log('Does the Chromium binary exist at that path?', chromeExists);
    });
  } catch (error) {
    console.error('Failed to initialize application:', error);
    process.exit(1);
  }
}

// Start the application
initializeApp().catch(console.error);