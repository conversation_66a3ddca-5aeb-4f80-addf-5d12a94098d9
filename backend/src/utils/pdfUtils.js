// Import pdf-parse dynamically to avoid initialization issues
const importPdfParse = async () => {
    const module = await import('pdf-parse/lib/pdf-parse.js');
    return module.default;
};

export async function readPdfFile(buffer) {
    try {
        const pdfParse = await importPdfParse();
        console.log('Buffer type:', buffer.constructor.name);
        console.log('Buffer size:', buffer.length);

        // Parse the PDF directly with pdf-parse
        const data = await pdfParse(buffer);
        console.log('PDF parsed successfully, text length:', data.text.length);
        
        // Split the text into pages based on common page markers
        const pages = data.text.split(/\f|\n(?=Page \d+)/).filter(page => page.trim());
        console.log('Number of pages extracted:', pages.length);

        return pages;
    } catch (error) {
        console.error('Failed to read PDF file:', error);
        throw error;
    }
}