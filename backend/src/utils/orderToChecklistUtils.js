import { firebaseInitializationPromise } from '../config/firebase.js';

/**
 * Converts an order to a checklist item
 * @param {Object} order - The order object
 * @param {string} userId - The user ID
 * @returns {Object} The checklist item
 */
const convertOrderToChecklistItem = (order, userId) => {
  return {
    orderId: order.id,
    userId,
    productName: order.description || '',
    pipCode: order.pipCode || '',
    expectedQuantity: order.approvedQty || 0,
    receivedQuantity: 0,
    status: 'pending',
    notes: '',
    dateChecked: null,
    checkedBy: '',
    supplier: order.supplier || '',
    createdAt: new Date()
  };
};

/**
 * Generates checklist items from an order
 * @param {string} orderId - The order ID
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} Array of generated checklist items
 */
const generateChecklistItemsFromOrder = async (orderId, userId) => {
  try {
    const { db } = await firebaseInitializationPromise;
    
    // Get the order
    const orderDoc = await db.collection('orders').doc(orderId).get();
    
    if (!orderDoc.exists) {
      throw new Error('Order not found');
    }
    
    const orderData = orderDoc.data();
    
    // Check if the order belongs to the current user
    if (orderData.userId !== userId) {
      throw new Error('Access denied');
    }
    
    // Check if checklist items already exist for this order
    const existingItemsSnapshot = await db.collection('checklistItems')
      .where('orderId', '==', orderId)
      .where('userId', '==', userId)
      .get();
    
    if (!existingItemsSnapshot.empty) {
      throw new Error(`Checklist items already exist for this order (${existingItemsSnapshot.size} items)`);
    }
    
    // Create checklist item from the order
    const checklistItem = convertOrderToChecklistItem({
      id: orderId,
      ...orderData
    }, userId);
    
    // Save to Firestore
    const checklistItemRef = db.collection('checklistItems').doc();
    await checklistItemRef.set({
      id: checklistItemRef.id,
      ...checklistItem
    });
    
    return [{
      id: checklistItemRef.id,
      ...checklistItem
    }];
  } catch (error) {
    console.error('Error generating checklist items from order:', error);
    throw error;
  }
};

/**
 * Generates checklist items from multiple orders
 * @param {Array<string>} orderIds - Array of order IDs
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} Array of generated checklist items
 */
const generateChecklistItemsFromOrders = async (orderIds, userId) => {
  try {
    const { db } = await firebaseInitializationPromise;
    
    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      throw new Error('Order IDs array is required');
    }
    
    // Get all orders
    const orders = [];
    const batch = db.batch();
    const generatedItems = [];
    const errors = [];
    
    // Get orders and check ownership
    for (const orderId of orderIds) {
      try {
        const orderDoc = await db.collection('orders').doc(orderId).get();
        
        if (!orderDoc.exists) {
          errors.push({
            orderId,
            error: 'Order not found'
          });
          continue;
        }
        
        const orderData = orderDoc.data();
        
        // Check if the order belongs to the current user
        if (orderData.userId !== userId) {
          errors.push({
            orderId,
            error: 'Access denied'
          });
          continue;
        }
        
        orders.push({
          id: orderId,
          ...orderData
        });
      } catch (error) {
        errors.push({
          orderId,
          error: error.message
        });
      }
    }
    
    // Check for existing checklist items for these orders
    const existingItemsQuery = await db.collection('checklistItems')
      .where('userId', '==', userId)
      .where('orderId', 'in', orderIds)
      .get();
    
    const existingOrderIds = new Set();
    existingItemsQuery.forEach(doc => {
      const data = doc.data();
      existingOrderIds.add(data.orderId);
    });
    
    // Generate checklist items for orders that don't have them yet
    for (const order of orders) {
      if (existingOrderIds.has(order.id)) {
        errors.push({
          orderId: order.id,
          error: 'Checklist items already exist for this order'
        });
        continue;
      }
      
      const checklistItem = convertOrderToChecklistItem(order, userId);
      const checklistItemRef = db.collection('checklistItems').doc();
      
      batch.set(checklistItemRef, {
        id: checklistItemRef.id,
        ...checklistItem
      });
      
      generatedItems.push({
        id: checklistItemRef.id,
        ...checklistItem
      });
    }
    
    // Commit the batch if there are items to create
    if (generatedItems.length > 0) {
      await batch.commit();
    }
    
    return {
      generatedItems,
      errors: errors.length > 0 ? errors : undefined
    };
  } catch (error) {
    console.error('Error generating checklist items from orders:', error);
    throw error;
  }
};

/**
 * Creates a supplier-specific checklist template
 * @param {string} supplier - The supplier name
 * @param {string} userId - The user ID
 * @param {Object} template - The template data
 * @returns {Promise<Object>} The created template
 */
const createSupplierTemplate = async (supplier, userId, template) => {
  try {
    const { db } = await firebaseInitializationPromise;
    
    if (!supplier) {
      throw new Error('Supplier name is required');
    }
    
    if (!template || typeof template !== 'object') {
      throw new Error('Template data is required');
    }
    
    // Check if template already exists
    const existingTemplateQuery = await db.collection('checklistTemplates')
      .where('userId', '==', userId)
      .where('supplier', '==', supplier)
      .get();
    
    if (!existingTemplateQuery.empty) {
      throw new Error(`Template already exists for supplier ${supplier}`);
    }
    
    // Create template
    const templateRef = db.collection('checklistTemplates').doc();
    const templateData = {
      id: templateRef.id,
      userId,
      supplier,
      ...template,
      createdAt: new Date()
    };
    
    await templateRef.set(templateData);
    
    return {
      id: templateRef.id,
      ...templateData
    };
  } catch (error) {
    console.error('Error creating supplier template:', error);
    throw error;
  }
};

/**
 * Gets a supplier-specific checklist template
 * @param {string} supplier - The supplier name
 * @param {string} userId - The user ID
 * @returns {Promise<Object>} The template
 */
const getSupplierTemplate = async (supplier, userId) => {
  try {
    const { db } = await firebaseInitializationPromise;
    
    if (!supplier) {
      throw new Error('Supplier name is required');
    }
    
    // Get template
    const templateQuery = await db.collection('checklistTemplates')
      .where('userId', '==', userId)
      .where('supplier', '==', supplier)
      .get();
    
    if (templateQuery.empty) {
      return null;
    }
    
    const templateDoc = templateQuery.docs[0];
    const templateData = templateDoc.data();
    
    return {
      id: templateDoc.id,
      ...templateData,
      createdAt: templateData.createdAt?.toDate()?.toISOString() || null
    };
  } catch (error) {
    console.error('Error getting supplier template:', error);
    throw error;
  }
};

export {
  convertOrderToChecklistItem,
  generateChecklistItemsFromOrder,
  generateChecklistItemsFromOrders,
  createSupplierTemplate,
  getSupplierTemplate
};