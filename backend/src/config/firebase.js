import admin from 'firebase-admin';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __dirname = dirname(fileURLToPath(import.meta.url));
const serviceAccountPath = join(__dirname, '../../keys/invoscan-phoenix-firebase-adminsdk-fbsvc-e40f7c5149.json');

let db, storage;

const firebaseInitializationPromise = (async () => {
  try {
    console.log('Starting Firebase initialization...');
    console.log('Service account path:', serviceAccountPath);
    
    // Check if service account file exists
    try {
      const fileExists = readFileSync(serviceAccountPath, { encoding: 'utf8', flag: 'r' }) ? true : false;
      console.log('Service account file exists:', fileExists);
    } catch (fileError) {
      console.error('❌ Error accessing service account file:', fileError.message);
      throw new Error(`Service account file not accessible: ${fileError.message}`);
    }
    
    // Read and parse the service account key synchronously
    let serviceAccount;
    try {
      const rawData = readFileSync(serviceAccountPath, 'utf8');
      serviceAccount = JSON.parse(rawData);
      console.log('✅ Service account loaded:', {
        projectId: serviceAccount.project_id,
        clientEmail: serviceAccount.client_email,
      });
    } catch (parseError) {
      console.error('❌ Error parsing service account JSON:', parseError);
      throw new Error(`Invalid service account JSON: ${parseError.message}`);
    }

    if (!admin.apps.length) {
      console.log('Initializing Firebase app...');
      try {
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          storageBucket: "invoscan-phoenix.firebasestorage.app",
          projectId: serviceAccount.project_id,
          // Optionally, remove databaseURL if not needed for Firestore
          databaseURL: `https://${serviceAccount.project_id}.firebaseio.com`,
        });
        console.log('🔥 Firebase initialized successfully using service account');
      } catch (initError) {
        console.error('❌ Firebase initialization error:', initError);
        throw new Error(`Firebase initialization failed: ${initError.message}`);
      }
    } else {
      console.log('Firebase app already initialized');
    }

    console.log('Initializing Firestore...');
    try {
      db = admin.firestore();
      console.log('🔥 Firestore initialized');
    } catch (firestoreError) {
      console.error('❌ Firestore initialization error:', firestoreError);
      throw new Error(`Firestore initialization failed: ${firestoreError.message}`);
    }
    
    console.log('Initializing Firebase Storage...');
    try {
      storage = admin.storage();
      console.log('🔥 Firebase Storage initialized');
    } catch (storageError) {
      console.error('❌ Firebase Storage initialization error:', storageError);
      throw new Error(`Firebase Storage initialization failed: ${storageError.message}`);
    }

    // Test Firestore connection
    try {
      console.log('Testing Firestore connection...');
      const collections = await db.listCollections();
      console.log('📂 Available Firestore collections:', collections.map((col) => col.id));
    } catch (connectionError) {
      console.error('❌ Firestore connection test failed:', connectionError);
      throw new Error(`Firestore connection test failed: ${connectionError.message}`);
    }

    return { db, storage };
  } catch (error) {
    console.error('❌ Failed to initialize Firebase:', error);
    console.error('Error stack:', error.stack);
    
    // Categorize the error for better debugging
    if (error.code === 'ENOENT') {
      console.error('❌ Service account file not found. Check the path:', serviceAccountPath);
    } else if (error.message && error.message.includes('Failed to parse')) {
      console.error('❌ Invalid JSON in service account file');
    } else if (error.message && error.message.includes('credential')) {
      console.error('❌ Invalid credential configuration');
    } else if (error.message && error.message.includes('project')) {
      console.error('❌ Project ID issue in Firebase configuration');
    }
    
    throw error;
  }
})();

export { db, storage, firebaseInitializationPromise };
