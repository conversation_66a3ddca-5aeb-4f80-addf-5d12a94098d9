import { setTimeout } from 'node:timers/promises';
import puppeteer from 'puppeteer';
import fs from 'fs/promises';
import { parse } from 'json2csv';

(async () => {
    // Launch the browser; set headless: false to see what’s happening.
    const browser = await puppeteer.launch({ headless: false });
    const page = await browser.newPage();

    // Enable console logging from the browser
    page.on('console', (msg) => {
        for (let i = 0; i < msg.args().length; ++i)
            console.log(`${i}: ${msg.args()[i]}`);
    });

    // Navigate to the login page.
    console.log('Navigating to login page: https://www.drugcomparison.co.uk/customers/login');
    await page.goto('https://www.drugcomparison.co.uk/customers/login', { waitUntil: 'networkidle2' });
    console.log('Navigated to login page.');

    // --- Login Section ---

    // // Fill in the email address
    await page.type('#CustomerEmail', '<EMAIL>');
    console.log('Email typed.');

    // // Fill in the password
    await page.type('#CustomerPassword', 'SMPB92');
    console.log('Password typed.');

    // Submit the login form
    await Promise.all([
        page.click('.signinbtn'),
        page.waitForNavigation({ waitUntil: 'networkidle2' }),
    ]);
    console.log('Clicked "Sign in" button and waited for navigation.');

    // --- End of Login Section ---

    const notificationSelector = '.closeallnoti'; //Correct selector
    const notificationElement = await page.$(notificationSelector);

    if (notificationElement) {
        console.log('Notification element found.');
        const isDisplayed = await page.evaluate((el) => {
            const style = window.getComputedStyle(el);
            return style && style.display !== 'none';
        }, notificationElement);

        if (isDisplayed) {
            console.log('Notification is displayed.');
            // Log the inner text of the notification button.
            const notificationText = await page.evaluate((el) => el.textContent, notificationElement);
            console.log(`Notification text is: ${notificationText}`);

            //Log the current url, before clicking the button
            const currentUrlBefore = page.url();
            console.log(`Current URL before clicking notification: ${currentUrlBefore}`);
            console.log('Clicking notification button.');

            //Clicking the button and wait for the page to stop loading
            await page.click(notificationSelector);

            //Log the current url, after clicking the button
            const currentUrlAfter = page.url();
            console.log(`Current URL after clicking notification: ${currentUrlAfter}`);
            console.log('Clicked notification button and waited for navigation.');
        } else {
            console.log('Notification is not displayed.');
        }
    } else {
        console.log('Notification element not found.');
    }

    // --- Handle Potential Redirect to Home ---
    let currentURL = page.url();
    if (currentURL.includes('/customers/home') || currentURL.includes('no-back')) {
        console.log(`Detected redirect to ${currentURL}. Redirecting to the specific orders page with today's date...`);

        // Build today’s date parameters
        const today = new Date();
        const dd = String(today.getDate()).padStart(2, '0');
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const yyyy = today.getFullYear();
        // const start_date = `${yyyy}-${mm}-${dd} 00:00`;
        // const end_date = `${yyyy}-${mm}-${dd} 23:59`;
        const start_date = `2025-02-26 00:00`;
        const end_date = `2025-02-26 23:59`;

        // URL-encode the dates.
        const start_date_encoded = encodeURIComponent(start_date);
        const end_date_encoded = encodeURIComponent(end_date);

        // Construct the orders page URL
        const ordersUrl = `https://www.drugcomparison.co.uk/orders/searchorder?start_date=${start_date_encoded}&end_date=${end_date_encoded}`;

        await page.goto(ordersUrl, { waitUntil: 'networkidle2' });
        console.log(`Redirected to ${ordersUrl}`);
        currentURL = page.url(); //Update current URL
    }
    // --- End of Handle Potential Redirect to Home ---


    // --- Pin Handling ---

    //Always add the pin. Remove the condition.
    const newPinSelector = '#ReportpincodePasscode';

    if (await page.$(newPinSelector)) {
        console.log('PIN input field found.');
        // Enter the PIN.
        console.log('Typing PIN: 2411');
        await page.type(newPinSelector, '2411'); // Using the example pin from your previous code.
        console.log('PIN typed.');

        // Set up dialog handler before submitting PIN
        page.on('dialog', async (dialog) => {
            console.log('Dialog appeared:', dialog.message());
            await dialog.accept();
            console.log('Dialog accepted');
        });

        // Submit the PIN form
        console.log('Clicking PIN submit button.');
        await Promise.all([
            page.click('#passcodesubmit'),
            page.waitForNavigation({ waitUntil: 'networkidle2' }),
        ]);
        console.log('Clicked PIN submit button and waited for navigation.');
    } else {
        console.log("Pin Input field not found.");
    }

    // --- End of Pin Handling ---


    // Go to the orders page if it was not redirected and it wasn't in the order page.
    // This part is not needed anymore. The order page is now always achieved.

    // Extract the orders from the page.
    // Wait for the table to be rendered
    await page.waitForSelector('#myTable');

    // Scrape the table rows
    const ordersData = await page.evaluate(() => {
        // Get all rows from the table's tbody
        const rows = Array.from(document.querySelectorAll('#myTable tbody tr'));

        // Filter out any empty rows (e.g., rows with a cell that has the "table-sort-search-empty" class)
        const dataRows = rows.filter(row => !row.querySelector('.table-sort-search-empty'));

        // Map each row into an object
        return dataRows.map(row => {
            const cells = row.querySelectorAll('td');
            return {
                sn: cells[0] ? cells[0].innerText.trim() : null,
                dateTime: cells[3] ? cells[3].innerText.trim() : null,
                orderNo: cells[4] ? cells[4].innerText.trim() : null,
                pipCode: cells[5] ? cells[5].innerText.trim() : null,
                description: cells[6] ? cells[6].innerText.trim() : null,
                orderQty: cells[7] ? cells[7].innerText.trim() : null,
                approvedQty: cells[8] ? cells[8].innerText.trim() : null,
                price: cells[9] ? cells[9].innerText.trim() : null,
                dtPrice: cells[10] ? cells[10].innerText.trim() : null,
                subTotal: cells[11] ? cells[11].innerText.trim() : null,
                category: cells[12] ? cells[12].innerText.trim() : null,
                supplier: cells[13] ? cells[13].innerText.trim() : null,
                response: cells[14] ? cells[14].innerText.trim() : null,
                discount: cells[15] ? cells[15].innerText.trim() : null,
                notes: cells[16] ? cells[16].innerText.trim() : null,
                orderRef: cells[17] ? cells[17].innerText.trim() : null,
            };
        });
    });

    // --- CSV Conversion and Writing ---
    try {
        // Define the fields for the CSV
        const fields = [
            'sn',
            'dateTime',
            'orderNo',
            'pipCode',
            'description',
            'orderQty',
            'approvedQty',
            'price',
            'dtPrice',
            'subTotal',
            'category',
            'supplier',
            'response',
            'discount',
            'notes',
            'orderRef'
        ];

        // Convert the JSON data to CSV format
        const csv = parse(ordersData, { fields });

        // Write the CSV data to a file
        const filename = 'orders.csv';
        await fs.writeFile(filename, csv);
        console.log(`Successfully wrote to ${filename}`);
    } catch (err) {
        console.error('Error writing to CSV:', err);
    }
    // --- End of CSV Conversion and Writing ---

    // Close the browser
    await browser.close();
    console.log('Browser closed.');
})();
